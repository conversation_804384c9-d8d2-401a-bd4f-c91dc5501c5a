//'use strict';

class DataFetcher {
    constructor(baseUrl, headers) {
        this.baseUrl = baseUrl;
        this.headers = headers;
    }

    get(url) {}

    post(url, data) {
        return fetch(`${this.baseUrl}${url}`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify(data),
        });
    }
}

function Creator({ data: propsData }) {
    const sessionKey = window.localStorage.getItem('_garderoboSessionKey'); //'13f5a6c11aab40c88ea43ba1269b1ff0';

    const dataFetcher = new DataFetcher(propsData.apiBaseUri, {
        'Content-Type': 'application/json;charset=utf-8',
        Authorization: 'Bearer ' + sessionKey,
        'X-Api-Key': propsData.key, // 'c0737cf450fb42d685853399f9699fe3'
    });

    const [products, setProducts] = React.useState([]);
    const [look, setLook] = React.useState(null);
    const [lookIndex, setLookIndex] = React.useState(0);
    const [similarProducts, setSimilarProducts] = React.useState({}); // [look_item_id]: {index: null, products: []}
    const [lookItemId, setLookItemId] = React.useState(null); // id товара в луке, для которого был запущен поиск похожих
    const [refreshedProduct, setRefreshedProduct] = React.useState(null); // продукт, замененный другим (рефреш)
    const [lookChanged, setLookChanged] = React.useState(false); // лук изменен если - добавлен/удален/изменен товар

    const formatPrice = (price) => {
        if (!price) return '-';
        return price
            .toString()
            .split('')
            .reverse()
            .map((digit, i) => ((i + 1) % 3 === 0 ? ' ' + digit : digit))
            .reverse()
            .join('');
    };

    /** Добавить товар в лук по кнопке + */
    const addProduct = () => {
        const productNew = { new_product_id: new Date().getTime() };
        setProducts([...products, productNew]);
    };

    /** Удаление товара, который уже был в луке  */
    const deleteProduct = (productId) => {
        if (confirm('Удалить товар?')) {
            setProducts(products.filter((product) => product.id !== productId));
            setLookChanged(true);
        }
    };

    /** Удаление товара, который добавлен с помощью кнопки + */
    const deleteNewProduct = (productId) => {
        setProducts(products.filter((product) => product.new_product_id !== productId));
    };

    const refreshProduct = (productId) => {
        const productNew = { new_product_id: new Date().getTime() };
        const index = products.findIndex((product) => productId === product.id);
        if (index !== -1) {
            const productsNew = [...products];
            setRefreshedProduct(...productsNew.splice(index, 1, productNew));
            setProducts(productsNew);
        }
    };

    /** загрузка похожих товаров в state */
    const gotoNextProduct = (lookItemId, productId, direction) => {
        const product = similarProducts[lookItemId];
        setLookItemId(lookItemId);

        if (product) {
            let index = (product.index + direction) % product.products.length;
            index = index < 0 ? product.products.length + index : index;
            setSimilarProducts({
                ...similarProducts,
                [lookItemId]: {
                    index,
                    products: product.products,
                },
            });
        } else {
            dataFetcher
                .post('get_similar_products/', {
                    look_item_id: lookItemId,
                    product_id: productId,
                })
                .then((value) => {
                    value.json().then((res) => {
                        if (res && res.result === 'ok') {
                            setSimilarProducts({
                                ...similarProducts,
                                [lookItemId]: {
                                    index: 1, // берем 1й, тк в похожих продуктах дуюлируется текущий - он 0й
                                    products: res.products || [],
                                },
                            });
                        }
                    });
                });
        }
    };

    /** смена текущего товара на похожий */
    React.useEffect(() => {
        const product = similarProducts[lookItemId];
        if (product) {
            const newProduct = product.products[product.index];
            newProduct.look_item_id = lookItemId;
            setProducts(
                products.map((product) => {
                    if (product.look_item_id === lookItemId) {
                        return newProduct;
                    } else {
                        return product;
                    }
                })
            );
            setLookChanged(true);
        }
    }, [similarProducts]);

    const createLook = () => {
        if (confirm('Создать новый эталонный лук?')) {
            const productsNew = [...products].filter((product) => look.main_product_id === product.wareId);
            setProducts(productsNew);
            setLook({ main_product_id: look.main_product_id });
            setLookIndex(0);
            setSimilarProducts({});
            setLookItemId(null);
            setLookChanged(false);
        }
    };

    const saveLook = (isReplace) => {
        const data = {
            look_id: (look && look.look_id) || undefined,
            product_id: (look && look.main_product_id) || undefined,
            items: products.filter((product) => product.wareId || product.id).map((product) => ({ product_id: product.wareId || product.id })),
            is_replace: isReplace,
        };
        dataFetcher.post('looks/look_save/', data).then((value) => {
            value.json().then((res) => {
                if (res && res.result === 'ok') {
                    alert('Look Saved');
                    loadLook({
                        page_type: 'product',
                        yml_id: (propsData.options && propsData.options.product_id) || undefined,
                        mode: 'creator',
                        look_id: res.look_id || undefined,
                        ...propsData.options,
                    });
                } else if (res && res.result === 'error') {
                    alert('Look Save Error: ' + res.msg);
                } else {
                    alert('Look Save Error: Unknown');
                }
            });
        });
    };

    const deleteLook = (id) => {
        if (confirm('Удалить эталонный лук?')) {
            dataFetcher
                .post('looks/delete/', {
                    look_id: id,
                })
                .then((value) => {
                    value.json().then((res) => {
                        if (res && res.result === 'ok') {
                            this.gotoNextLook(lookIndex);
                        } else {
                            alert('Не получилось удалить лук!');
                        }
                    });
                });
        }
    };

    const gotoNextLook = (index) => {
        const condition = lookChanged ? confirm('Все изменения будут удалены!\nПерейти к другому образу?') : true;
        if (condition) {
            dataFetcher
                .post('looks/get_next_look/', {
                    product_id: look.main_product_id,
                    index: index,
                })
                .then((value) => {
                    value.json().then((res) => {
                        if (res && res.result === 'ok') {
                            const look = res.blocks.find((block) => block.type === 'looks');
                            setLook(look);
                            setProducts(look.products);
                            setLookIndex(look.index);
                            setLookChanged(false);
                        }
                    });
                });
        }
    };

    const saveProduct = (newProductId, imageUrl) => {
        dataFetcher
            .post('looks/get_product_info/', {
                url: imageUrl,
            })
            .then((value) => {
                value
                    .json()
                    .then((res) => {
                        if (res && res.result === 'ok') {
                            const productsUpdated = products.map((product) => (product.new_product_id === newProductId ? res.data : product));
                            setProducts(productsUpdated);
                            setLookChanged(true);
                        } else {
                            if (refreshedProduct) {
                                const productsUpdated = products.map((product) => (product.new_product_id === newProductId ? refreshedProduct : product));
                                setProducts(productsUpdated);
                            }
                            alert('Ошибка загрузки товара!');
                        }
                    })
                    .finally(() => {
                        setRefreshedProduct(null);
                        console.log('final');
                    });
            });
    };

    const getSvgTrash = () => {
        return (
            <svg xmlns="http://www.w3.org/2000/svg" height="32px" version="1.1" viewBox="0 0 32 32" width="32px">
                <g fill="none" fillRule="evenodd" stroke="none" strokeWidth="1">
                    <g fill="#333">
                        <path d="M23,7 L21,7 L21,7 L21,5.0048815 C21,3.89761602 20.1041422,3 19.0026083,3 L13.9973917,3 C12.8942627,3 12,3.8938998 12,5.0048815 L12,7 L10,7 L6,7 L6,8 L8,8 L8,26.9931517 C8,28.6537881 9.33396149,30 11.0001262,30 L21.9998738,30 C23.6567977,30 25,28.6640085 25,26.9931517 L25,8 L27,8 L27,7 L23,7 L23,7 Z M12,10 L12,27 L13,27 L13,10 L12,10 L12,10 Z M16,10 L16,27 L17,27 L17,10 L16,10 L16,10 Z M20,10 L20,27 L21,27 L21,10 L20,10 L20,10 Z M14.0029293,4 C13.4490268,4 13,4.44266033 13,4.99895656 L13,7 L20,7 L20,4.99895656 C20,4.44724809 19.5621186,4 18.9970707,4 L14.0029293,4 L14.0029293,4 Z" />
                    </g>
                </g>
            </svg>
        );
    };

    React.useEffect(() => {
        // on mount only?
        const data = {
            page_type: 'product',
            yml_id: (propsData.options && propsData.options.product_id) || undefined,
            ...propsData.options,
        };
        loadLook(data);
    }, []);

    const loadLook = (params) => {
        dataFetcher.post('looks/open_page/', params).then((value) => {
            value.json().then((res) => {
                if (res && res.result === 'ok') {
                    const look = res.blocks.find((block) => block.type === 'looks');
                    setLook(look);
                    setProducts(look.products || []);
                    setSimilarProducts({});
                    setLookItemId(null);
                    setLookChanged(false);
                } else {
                    alert('Роут looks/open_page/ не вернул ни одного лука!');
                }
            });
        });
    };

    return (
        <div className="garderobo-creator">
            <h3 className="garderobo-creator-header">{look && look.name}</h3>
            <div className="garderobo-creator-products-container">
                {products.map((product) => {
                    const productRef = React.createRef();
                    const isMainProduct = look && product.wareId === look.main_product_id;

                    return !product.new_product_id ? (
                        <div className="garderobo-creator-product-wrapper" key={product.id}>
                            <div
                                className={`garderobo-creator-product ${isMainProduct ? 'garderobo-creator-product--main' : ''}`}
                                style={{
                                    backgroundImage: `url(${product.picture})`,
                                }}
                            >
                                {look && product.wareId === look.main_product_id ? null : (
                                    <div className="garderobo-creator-product__btns">
                                        <button type="button" className="garderobo-creator-product__btn-delete" onClick={() => deleteProduct(product.id)}>
                                            {getSvgTrash()}
                                        </button>
                                        <button
                                            type="button"
                                            className="garderobo-creator-product__btn-refresh"
                                            onClick={() => refreshProduct(product.id)}
                                        ></button>

                                        {!product.look_item_id ? null : (
                                            <button
                                                type="button"
                                                className="garderobo-creator-product__btn-left"
                                                onClick={() => gotoNextProduct(product.look_item_id, product.wareId, -1)}
                                            ></button>
                                        )}
                                        {!product.look_item_id ? null : (
                                            <button
                                                type="button"
                                                className="garderobo-creator-product__btn-right"
                                                onClick={() => gotoNextProduct(product.look_item_id, product.wareId, 1)}
                                            ></button>
                                        )}
                                    </div>
                                )}
                            </div>
                            <div className="garderobo-creator-product__name">
                                {product.link ? (
                                    <a className="garderobo-creator-product__name-link" href={product.link} target="_blank">
                                        {product.name}
                                    </a>
                                ) : (
                                    product.name
                                )}
                            </div>
                            <div className="garderobo-creator-product__price">{formatPrice(product.price)}</div>
                        </div>
                    ) : (
                        <div className="garderobo-creator-product garderobo-creator-product--new" key={product.new_product_id}>
                            <button type="button" className="garderobo-creator-product__btn-delete" onClick={() => deleteNewProduct(product.new_product_id)}>
                                {getSvgTrash()}
                            </button>
                            <input className="garderobo-creator-input-text" type="text" placeholder="url..." ref={productRef}></input>
                            <button
                                className="garderobo-creator-btn"
                                type="button"
                                onClick={() => saveProduct(product.new_product_id, productRef.current.value)}
                            >
                                Сохранить
                            </button>
                        </div>
                    );
                })}

                <div className="garderobo-creator-product garderobo-creator-product--add-new">
                    <button className="garderobo-creator-product__btn-new" onClick={addProduct}></button>
                </div>
            </div>
            <div className="garderobo-creator-bottom-controls-wrapper">
                <div className="garderobo-creator-bottom-controls">
                    <button type="button" className="garderobo-creator-btn-link garderobo-creator-btn-link--prev" onClick={() => gotoNextLook(lookIndex - 1)}>
                        Предыдущий образ
                    </button>
                    <button type="button" className="garderobo-creator-btn-link garderobo-creator-btn-link--next" onClick={() => gotoNextLook(lookIndex + 1)}>
                        Следующий образ
                    </button>
                    <button type="button" className="garderobo-creator-btn" onClick={() => createLook()}>
                        Новый
                    </button>
                    {products.filter((product) => product.id).length >= 2 ? (
                        <React.Fragment>
                            <button type="button" className="garderobo-creator-btn" onClick={() => saveLook()}>
                                Сохранить
                            </button>
                            <button type="button" className="garderobo-creator-btn" onClick={() => saveLook(1)}>
                                Обновить
                            </button>
                        </React.Fragment>
                    ) : null}
                    <button type="button" className="garderobo-creator-btn" onClick={() => deleteLook(look.look_id)}>
                        Удалить
                    </button>
                </div>
            </div>
        </div>
    );
}

_garderoboWidget.then(function (widget) {
    const creatorData = widget.getDataForCreator();
    try {
        const container = document.createElement('div');
        container.setAttribute('id', 'garderobo-creator');
        creatorData.containerEl.parentNode.insertBefore(container, creatorData.containerEl);

        ReactDOM.render(<Creator data={creatorData} />, container);
    } catch (error) {
        console.error('Error in rendering Creator into container');
    }
});
