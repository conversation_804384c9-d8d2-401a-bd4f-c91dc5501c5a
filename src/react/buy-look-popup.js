class DataFetcherPopup {
    constructor(baseUrl, headers) {
        this.baseUrl = baseUrl;
        this.headers = headers;
    }

    get(url) {}

    post(url, data) {
        return fetch(`${this.baseUrl}${url}`, {
            method: 'POST',
            headers: this.headers,
            body: JSON.stringify(data),
        });
    }
}

function BuyLookPopupSizesSort(a, b) {
    if (a.name > b.name) {
        return 1;
    }
    if (a.name < b.name) {
        return -1;
    }
    return 0;
}

function BuyLookPopup({ data, lookId, productId }) {
    const sessionKey = window.localStorage.getItem('_garderoboSessionKey'); //'13f5a6c11aab40c88ea43ba1269b1ff0';

    const dataFetcher = new DataFetcherPopup(data.apiBaseUri, {
        'Content-Type': 'application/json;charset=utf-8',
        Authorization: 'Bearer ' + sessionKey,
        'X-Api-Key': data.key, // 'c0737cf450fb42d685853399f9699fe3'
    });

    const page = data.page;

    const [isOpened, setIsOpened] = React.useState(false);
    const [products, setProducts] = React.useState();
    const [currProduct, setCurrProduct] = React.useState();
    const [look, setLook] = React.useState();
    const [similars, setSimilars] = React.useState(); //[look_item_id]: products
    const [selectedSizes, setSelectedSizes] = React.useState({});
    // const [desktopIsLeftDisabled, setDesktopIsLeftDisabled] = React.useState({});
    // const [mobIsLeftDisabled, setMobIsLeftDisabled] = React.useState({});
    const scrollOptions = { behavior: 'smooth', block: 'nearest' };
    const scrollOptionsVert = { behavior: 'smooth', inline: 'nearest' };

    const closePopup = () => setIsOpened(false);

    const addProductToCart = (product) => {
        page._widget._callbacks.addToCart.apply(null, [product.id, selectedSizes[product.id]]);
    };

    const selectProduct = (product) => {
        setCurrProduct(product);
    };

    const updateProducts = (productNew, productOld) => {
        const productsNew = products.map((_product) => {
            if (_product.id === productOld.id) {
                return productNew;
            }
            return _product;
        });
        setProducts(productsNew);
    };

    const selectSize = (product, size) => {
        let sizesNew = { ...selectedSizes };
        if (size) {
            sizesNew[product.id] = size;
        } else {
            delete sizesNew[product.id];
        }
        setSelectedSizes(sizesNew);
    };

    const slideUp = () => {
        const container = document.querySelector(`.garderobo-widget-popup-2__left-block-content`);
        if (container && container.children) {
            const containerLimit = container.getBoundingClientRect().y;
            for (let i = 0; i < container.children.length; i++) {
                const childBox = container.children[i].getBoundingClientRect();
                let prevChildBox;
                if (container.children[i - 1]) {
                    prevChildBox = container.children[i - 1].getBoundingClientRect();
                }
                if (Math.round(childBox.y) < Math.round(containerLimit) && Math.round(childBox.y + childBox.height) > Math.round(containerLimit)) {
                    container.children[i].scrollIntoView(scrollOptionsVert);
                    //console.log('in thumb');
                    break;
                } else if (
                    prevChildBox &&
                    Math.round(childBox.y) >= Math.round(containerLimit) &&
                    Math.round(prevChildBox.y + prevChildBox.height) <= Math.round(containerLimit)
                ) {
                    //console.log('in gap');
                    container.children[i - 1].scrollIntoView(scrollOptionsVert);
                    break;
                }
            }
        }
    };

    const slideDown = () => {
        const container = document.querySelector(`.garderobo-widget-popup-2__left-block-content`);
        if (container && container.children) {
            const containerLimit = container.getBoundingClientRect().y + container.getBoundingClientRect().height;
            for (let i = 0; i < container.children.length; i++) {
                const childBox = container.children[i].getBoundingClientRect();
                let nextChildBox;
                if (container.children[i + 1]) {
                    nextChildBox = container.children[i + 1].getBoundingClientRect();
                }
                if (Math.round(childBox.y) <= Math.round(containerLimit) && Math.round(childBox.y + childBox.height) > Math.round(containerLimit)) {
                    container.children[i].scrollIntoView(scrollOptionsVert);
                    //console.log('in thumb');
                    break;
                } else if (
                    nextChildBox &&
                    Math.round(childBox.y + childBox.height) <= Math.round(containerLimit) &&
                    Math.round(nextChildBox.y) >= containerLimit
                ) {
                    //console.log('in gap');
                    container.children[i + 1].scrollIntoView(scrollOptionsVert);
                    break;
                }
            }
        }
    };

    const slideLeft = (productId, isDesktop = false) => {
        const parent = isDesktop ? '.garderobo-widget-popup-2__right-block' : '.garderobo-widget-popup-2__right-blocks';
        const container = document.querySelector(`${parent} .garderobo-widget-popup-2__right-similars[data-product="${productId}"]`);
        const containerLeft = container.getBoundingClientRect().x;
        if (container && container.children) {
            for (let i = 0; i < container.children.length; i++) {
                const childBox = container.children[i].getBoundingClientRect();
                let prevChildBox;
                if (container.children[i - 1]) {
                    prevChildBox = container.children[i - 1].getBoundingClientRect();
                }
                if (Math.round(childBox.x) < Math.round(containerLeft) && Math.round(childBox.x + childBox.width) > Math.round(containerLeft)) {
                    container.children[i].scrollIntoView(scrollOptions);
                    //console.log('in thumb');
                    break;
                } else if (
                    prevChildBox &&
                    Math.round(childBox.x) >= Math.round(containerLeft) &&
                    Math.round(prevChildBox.x + prevChildBox.width) <= Math.round(containerLeft)
                ) {
                    //console.log('in gap');
                    container.children[i - 1].scrollIntoView(scrollOptions);
                    break;
                }
            }
        }
    };

    const slideRight = (productId, isDesktop = false) => {
        const parent = isDesktop ? '.garderobo-widget-popup-2__right-block' : '.garderobo-widget-popup-2__right-blocks';
        const container = document.querySelector(`${parent} .garderobo-widget-popup-2__right-similars[data-product="${productId}"]`);
        const containerRight = container.getBoundingClientRect().x + container.getBoundingClientRect().width;
        if (container && container.children) {
            for (let i = 0; i < container.children.length; i++) {
                const childBox = container.children[i].getBoundingClientRect();
                let nextChildBox;
                if (container.children[i + 1]) {
                    nextChildBox = container.children[i + 1].getBoundingClientRect();
                }
                if (Math.round(childBox.x) <= Math.round(containerRight) && Math.round(childBox.x + childBox.width) > Math.round(containerRight)) {
                    container.children[i].scrollIntoView(scrollOptions);
                    //console.log('in thumb');
                    break;
                } else if (
                    nextChildBox &&
                    Math.round(childBox.x + childBox.width) <= Math.round(containerRight) &&
                    Math.round(nextChildBox.x) >= containerRight
                ) {
                    //console.log('in gap');
                    container.children[i + 1].scrollIntoView(scrollOptions);
                    break;
                }
            }
        }

        // if (isDesktop) {
        //     const leftDisabled = { ...desktopIsLeftDisabled };
        //     leftDisabled[productId] = false;
        //     setDesktopIsLeftDisabled(leftDisabled);
        // } else {
        //     const mobLeftDisabled = { ...mobIsLeftDisabled };
        //     mobLeftDisabled[productId] = false;
        //     setMobIsLeftDisabled(mobLeftDisabled);
        // }
    };

    const getPricesHtml = (product) => {
        return (
            <div className={`garderobo-widget-popup-2__prices ${!product.old_price ? '' : 'garderobo-widget-popup-2__prices--discounted'}`}>
                {!product.old_price ? null : <span className="garderobo-widget-popup-2__price-old">{page.priceFormatter(product.old_price)}</span>}
                <span className="garderobo-widget-popup-2__price">{page.priceFormatter(product.price)}</span>
            </div>
        );
    };

    const fetchLook = () => {
        dataFetcher
            .post('looks/get_look/', {
                look_id: lookId,
                product_id: productId,
            })
            .then((value) => {
                value.json().then((res) => {
                    if (res && res.result === 'ok') {
                        const look = res.blocks.find((block) => block.type === 'looks');
                        if (look && look.products[0]) {
                            setProducts(look.products);
                            setCurrProduct(look.products[0]);
                            setLook(look);
                            setIsOpened(true);
                        }
                    }
                });
            });
    };

    // mounting
    React.useEffect(() => {
        fetchLook();
    }, []);

    React.useEffect(() => {
        if (!currProduct) {
            return;
        }

        //scroll similars
        const swapEl = document.querySelector(`[data-product="${currProduct.look_item_id}"] .garderobo-widget-popup-2__right-sim:first-child`);
        if (swapEl) {
            swapEl.scrollIntoView({ block: 'nearest' });
        }
    }, [currProduct]);

    React.useEffect(() => {
        if (!look) {
            return;
        }

        const similarsNew = {};

        if (!similars || !similars[_product.look_item_id]) {
            const fetches = look.products.map((_product) => {
                return dataFetcher
                    .post('get_similar_products/', {
                        look_item_id: _product.look_item_id,
                        product_id: _product.wareId,
                    })
                    .then((resp) => resp.json())
                    .then((res) => {
                        if (res && res.result === 'ok') {
                            similarsNew[_product.look_item_id] = (res.products || []).map((_p) => {
                                _p.look_item_id = _product.look_item_id;
                                return _p;
                            });
                        }
                    });
            });
            Promise.all(fetches).then(() => {
                setSimilars(similarsNew);
            });
        }
    }, [look]);

    const getArrowUpSvg = () => {
        return (
            <svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M6 0C6.25599 0 6.51198 0.0979967 6.70697 0.29299L11.7068 5.29281C12.0977 5.6838 12.0977 6.31577 11.7068 6.70676C11.3158 7.09775 10.6838 7.09775 10.2928 6.70676L6 2.41391L1.70718 6.70676C1.3162 7.09775 0.684221 7.09775 0.293238 6.70676C-0.0977459 6.31577 -0.0977459 5.6838 0.293238 5.29281L5.29303 0.29299C5.48802 0.0979967 5.74401 0 6 0Z"
                    fill="#181818"
                />
            </svg>
        );
    };

    const getCartBtnHtml = (currProduct) => {
        // look.block_with_sizes = 0;
        // if (look && look.block_with_sizes) {
        return (
            <button
                className="garderobo-widget-popup-2__right-btn-buy"
                type="button"
                disabled={!selectedSizes[currProduct.id] ? true : null}
                onClick={() => addProductToCart(currProduct)}
            >
                Add to cart
            </button>
        );
        //}
        // return (
        //     <a className="garderobo-widget-popup-2__right-btn-buy" target="_blank" href={currProduct.link}>
        //         Add to cart
        //     </a>
        // );
    };

    const getRightProductBlock = (product) => {
        return (
            <div className="garderobo-widget-popup-2__right-blocks-item">
                <div className="garderobo-widget-popup-2__right-top">
                    <div
                        className="garderobo-widget-popup-2__right-thumb"
                        style={{
                            backgroundImage: `url(${product.picture})`,
                        }}
                    ></div>
                    <div>
                        <div className="garderobo-widget-popup-2__right-title">{product.name}</div>
                        {getPricesHtml(product)}
                    </div>
                </div>

                <div className="garderobo-widget-popup-2__right-sizes-title">Заменить:</div>
                {!similars[product.look_item_id] ? null : (
                    <div className="garderobo-widget-popup-2__right-similars-wrapper">
                        <button
                            className="garderobo-widget-popup-2__arrow garderobo-widget-popup-2__arrow--left"
                            type="button"
                            // disabled={!!mobIsLeftDisabled[product.look_item_id]}
                            onClick={() => slideLeft(product.look_item_id)}
                        >
                            {getArrowUpSvg()}
                        </button>
                        <div className="garderobo-widget-popup-2__right-similars" data-product={product.look_item_id}>
                            {similars[product.look_item_id].map((sim) => (
                                <div
                                    className={`garderobo-widget-popup-2__right-sim ${
                                        sim.id === product.id || sim.id === product.wareId ? 'garderobo-widget-popup-2__right-sim--selected' : ''
                                    }`}
                                    key={sim.id}
                                    onClick={() => {
                                        selectProduct({ ...sim, look_item_id: product.look_item_id });
                                        updateProducts(sim, product);
                                        selectSize(product, null);
                                    }}
                                >
                                    <div className="garderobo-widget-popup-2__right-sim-pic" title={sim.name}>
                                        <div
                                            className="garderobo-widget-popup-2__right-sim-pic-inner"
                                            style={{
                                                backgroundImage: `url(${sim.picture})`,
                                            }}
                                        ></div>
                                    </div>
                                    <div
                                        className={`garderobo-widget-popup-2__right-sim-price ${
                                            sim.old_price && sim.price ? 'garderobo-widget-popup-2__prices--discounted' : ''
                                        }`}
                                    >
                                        <span className="garderobo-widget-popup-2__price">{page.priceFormatter(sim.price)}</span>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <button
                            className="garderobo-widget-popup-2__arrow garderobo-widget-popup-2__arrow--right"
                            type="button"
                            onClick={() => slideRight(product.look_item_id)}
                        >
                            {getArrowUpSvg()}
                        </button>
                    </div>
                )}

                {!product.sizes || !product.sizes.length ? null : (
                    <div>
                        <div className="garderobo-widget-popup-2__right-sizes-title">Размер:</div>
                        <div className="garderobo-widget-popup-2__right-sizes">
                            <select
                                className="garderobo-widget-popup-2__right-sizes-select"
                                value={selectedSizes[product.id]}
                                onChange={(e) => {
                                    selectSize(product, +e.target.value);
                                }}
                            >
                                <option>Выбрать размер</option>
                                {product.sizes
                                    .filter((size) => size.is_available)
                                    .sort(BuyLookPopupSizesSort)
                                    .map((size) => (
                                        <option key={size.yml_id} value={size.yml_id}>
                                            {size.name}
                                        </option>
                                        // <div
                                        //     key={size.yml_id}
                                        //     className={`garderobo-widget-popup-2__right-size ${
                                        //         selectedSize && selectedSize.yml_id === size.yml_id ? 'garderobo-widget-popup-2__right-size--selected' : ''
                                        //     }`}
                                        //     onClick={() => setSelectedSize(size)}
                                        // >
                                        //     {size.name}
                                        // </div>
                                    ))}
                            </select>
                        </div>
                        <div className="garderobo-widget-popup-2__right-controls">{getCartBtnHtml(product)}</div>
                    </div>
                )}
            </div>
        );
    };

    if (!look || !products || !currProduct || !similars) {
        return null;
    }

    return (
        <div className={`garderobo-widget-popup-container ${isOpened ? 'garderobo-widget-popup-container--opened' : ''}`}>
            <div className="garderobo-widget-popup garderobo-widget-popup-2">
                <button type="button" className="garderobo-widget-popup__btn-close" onClick={() => closePopup()}></button>
                <div className="garderobo-widget-popup-2__content">
                    {/* <h3 className="garderobo-widget-popup-2__header">Buy the look</h3> */}
                    <div className="garderobo-widget-popup-2__left-block">
                        <button className="garderobo-widget-popup-2__arrow" type="button" onClick={() => slideUp()}>
                            {getArrowUpSvg()}
                        </button>
                        <hr className="garderobo-widget-popup-2__left-block-line" />
                        <div className="garderobo-widget-popup-2__left-block-content">
                            {products.map((product) => (
                                <div
                                    key={product.id}
                                    className={`garderobo-widget-popup-2__thumb-container ${
                                        product.id === currProduct.id || product.id === currProduct.wareId
                                            ? 'garderobo-widget-popup-2__thumb-container--selected'
                                            : ''
                                    }`}
                                    onClick={() => {
                                        selectProduct(product);
                                    }}
                                >
                                    <img className="garderobo-widget-popup-2__thumb" src={product.picture} />
                                    <div className="garderobo-widget-popup-2__thumb-title">{product.name}</div>

                                    {getPricesHtml(product)}
                                </div>
                            ))}
                        </div>
                        <hr className="garderobo-widget-popup-2__left-block-line" />
                        <button className="garderobo-widget-popup-2__arrow garderobo-widget-popup-2__arrow--down" type="button" onClick={() => slideDown()}>
                            {getArrowUpSvg()}
                        </button>
                    </div>
                    <div className="garderobo-widget-popup-2__right-block">
                        <div className="garderobo-widget-popup-2__right-top">
                            <div
                                className="garderobo-widget-popup-2__right-thumb"
                                style={{
                                    backgroundImage: `url(${currProduct.picture})`,
                                }}
                            ></div>
                            <div>
                                <div className="garderobo-widget-popup-2__right-title">{currProduct.name}</div>
                                {getPricesHtml(currProduct)}
                            </div>
                        </div>

                        <div className="garderobo-widget-popup-2__right-sizes-title">Заменить:</div>
                        {!similars[currProduct.look_item_id] ? null : (
                            <div className="garderobo-widget-popup-2__right-similars-wrapper">
                                <button
                                    className="garderobo-widget-popup-2__arrow garderobo-widget-popup-2__arrow--left"
                                    type="button"
                                    // disabled={!!desktopIsLeftDisabled[currProduct.look_item_id]}
                                    onClick={() => slideLeft(currProduct.look_item_id, true)}
                                >
                                    {getArrowUpSvg()}
                                </button>
                                <div className="garderobo-widget-popup-2__right-similars" data-product={currProduct.look_item_id}>
                                    {similars[currProduct.look_item_id].map((sim) => (
                                        <div
                                            key={sim.id}
                                            className={`garderobo-widget-popup-2__right-sim ${
                                                sim.id === currProduct.id || sim.id === currProduct.wareId
                                                    ? 'garderobo-widget-popup-2__right-sim--selected'
                                                    : ''
                                            }`}
                                            onClick={() => {
                                                selectProduct({ ...sim, look_item_id: currProduct.look_item_id });
                                                updateProducts(sim, currProduct);
                                                selectSize(currProduct, null);
                                            }}
                                        >
                                            <div className="garderobo-widget-popup-2__right-sim-pic" title={sim.name}>
                                                <div
                                                    className="garderobo-widget-popup-2__right-sim-pic-inner"
                                                    style={{
                                                        backgroundImage: `url(${sim.picture})`,
                                                    }}
                                                ></div>
                                            </div>
                                            <div
                                                className={`garderobo-widget-popup-2__right-sim-price ${
                                                    sim.old_price && sim.price ? 'garderobo-widget-popup-2__prices--discounted' : ''
                                                }`}
                                            >
                                                <span className="garderobo-widget-popup-2__price">{page.priceFormatter(sim.price)}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                                <button
                                    className="garderobo-widget-popup-2__arrow garderobo-widget-popup-2__arrow--right"
                                    type="button"
                                    onClick={() => slideRight(currProduct.look_item_id, true)}
                                >
                                    {getArrowUpSvg()}
                                </button>
                            </div>
                        )}
                        {!currProduct.sizes || !currProduct.sizes.length ? null : (
                            <div>
                                <div className="garderobo-widget-popup-2__right-sizes-title">Размер:</div>
                                <div className="garderobo-widget-popup-2__right-sizes">
                                    <select
                                        className="garderobo-widget-popup-2__right-sizes-select"
                                        value={selectedSizes[currProduct.id]}
                                        onChange={(e) => {
                                            selectSize(currProduct, +e.target.value);
                                        }}
                                    >
                                        <option>Выбрать размер</option>
                                        {currProduct.sizes
                                            .filter((size) => size.is_available)
                                            .sort(BuyLookPopupSizesSort)
                                            .map((size) => (
                                                <option key={size.yml_id} value={size.yml_id}>
                                                    {size.name}
                                                </option>
                                                // <div
                                                //     key={size.yml_id}
                                                //     className={`garderobo-widget-popup-2__right-size ${
                                                //         selectedSize && selectedSize.yml_id === size.yml_id ? 'garderobo-widget-popup-2__right-size--selected' : ''
                                                //     }`}
                                                //     onClick={() => setSelectedSize(size)}
                                                // >
                                                //     {size.name}
                                                // </div>
                                            ))}
                                    </select>
                                </div>
                                <div className="garderobo-widget-popup-2__right-controls">{getCartBtnHtml(currProduct)}</div>
                            </div>
                        )}
                    </div>

                    <div className="garderobo-widget-popup-2__right-blocks">{products.map((product) => getRightProductBlock(product))}</div>
                </div>
                {/* <div className="garderobo-widget-popup-2__mob-controls">
                    <button
                        className="garderobo-widget-popup-2__right-btn-buy"
                        type="button"
                        disabled={!Object.entries(selectedSizes).length ? true : null}
                        onClick={() => addToCartMob()}
                    >
                        Add to cart
                    </button>
                </div> */}
            </div>
        </div>
    );
}

_garderoboWidget.then(function (widget) {
    const data = widget.getDataForNewPopup2();

    // if (data.page.special_look_id) {
    //     window._garderoboWidgetBuyLookResolver(document.getElementById('btn-special-look'));
    // }

    //_garderoboWidgetBuyLook.then(function (btnMain) {
    const btnMain = document.querySelector(`#btn-special-look[data-id="${data.page.special_look_id}"]`);

    if (!btnMain) {
        console.error('#btn-special-look not found!');
        return;
    }

    if (window.requirejs && window.require) {
        require(['react', 'react-dom'], function (React, ReactDOM) {
            //console.log('React, ReactDOM', React, ReactDOM);
            window.React = React;
            window.ReactDOM = ReactDOM;

            btnMain.addEventListener('click', () => {
                const lookId = btnMain.getAttribute('data-id');
                const productId = btnMain.getAttribute('data-product-id'); //+data.options.product_id
                try {
                    const oldContainer = document.querySelector('#garderobo-widget-popup-2');
                    if (oldContainer) {
                        oldContainer.parentNode.removeChild(oldContainer);
                    }

                    const container = document.createElement('div');
                    container.setAttribute('id', 'garderobo-widget-popup-2');
                    document.body.appendChild(container);

                    ReactDOM.render(<BuyLookPopup data={data} lookId={+lookId} productId={+productId} />, container);
                } catch (error) {
                    console.error('Error in rendering widget-popup-2 into container');
                }
            });
            btnMain.classList.remove('garderobo-widget-displayed-none');
        }, function (error) {
            console.log('error loading React and|or ReactDOM', error);
        });
    } else {
        console.error('requirejs is undefined');
    }
    //});
});
