/**
 * <PERSON><PERSON> class for widget data processing in React Native
 */
import React from 'react';
import HorizontalWidget from './HorizontalWidget';

export default class WidgetsHandler {
  /**
   * Create a new WidgetsHandler instance
   * @param {string} apiKey - The API key for authentication
   * @param {Object} callbacks - Object containing callback functions
   * @param {Object} options - Widget options
   * @param {Object} analytics - Analytics service instance
   */
  constructor(apiKey, callbacks = {}, options = {}, analytics) {
    if (!apiKey) {
      console.error('WidgetsHandler: API key is required');
      return;
    }

    this.apiKey = apiKey;
    this.callbacks = callbacks;
    this.options = options;
    this.analytics = analytics;
    
    // Instead of DOM containers, we store widget component references
    this.widgets = {
      horizontal: null,
      looks: null,
      platform: null
    };
    
    // Store widget data
    this.data = {
      horizontalwidgetsData: [],
      looksData: [],
      special_look_image: null
    };
    
    // Cache for pending widget data
    this.pendingData = {
      horizontal: null,
      looks: null
    };
  }

  /**
   * Get the HorizontalWidget component instance
   * @returns {React.ReactElement} HorizontalWidget component
   */
  getHorizontalWidget() {
    console.log('[WidgetsHandler] Getting HorizontalWidget component');
    // This is a crucial fix - the ref must be properly connected to the component
    return <HorizontalWidget ref={(ref) => {
      console.log('[WidgetsHandler] Setting horizontal widget ref');
      this.widgets.horizontal = ref;
      
      // If we have pending data, apply it now
      if (this.pendingData.horizontal && ref && ref.updateData) {
        console.log('[WidgetsHandler] Applying pending data to horizontal widget');
        ref.updateData(this.pendingData.horizontal);
        this.pendingData.horizontal = null;
      }
    }} />;
  }

  /**
   * Set a widget reference
   * @param {string} type - Widget type ('horizontal', 'looks', or 'platform')
   * @param {Object} ref - React component reference
   */
  setWidgetRef(type, ref) {
    if (this.widgets.hasOwnProperty(type)) {
      this.widgets[type] = ref;
    }
  }

  /**
   * Process data from the open page API response
   * @param {Object} data - Open page data
   */
  processOpenPageData(data) {
    try {
      console.log('[WidgetsHandler] Processing open page data');
      
      if (!data) {
        console.error('[WidgetsHandler] No data provided to processOpenPageData');
        return;
      }
      
      console.log('[WidgetsHandler] Data structure:', Object.keys(data));
      
      // Store the data
      this.data = data;
      
      // API returns either horizontalwidgets directly or inside blocks
      let horizontalWidgetsData = [];
      
      // Check for horizontalwidgets array directly
      if (data.horizontalwidgets && Array.isArray(data.horizontalwidgets)) {
        console.log(`[WidgetsHandler] Found ${data.horizontalwidgets.length} horizontal widgets directly`);
        horizontalWidgetsData = data.horizontalwidgets;
      } 
      // Check for horizontalwidgets in blocks structure
      else if (data.blocks && Array.isArray(data.blocks)) {
        console.log(`[WidgetsHandler] Processing ${data.blocks.length} blocks`);
        
        console.log('-------------------------------data.blocks', data.blocks);
        // Filter blocks that contain products
        horizontalWidgetsData = data.blocks.filter(block => block.type !== 'looks');
        
        console.log(`[WidgetsHandler] Found ${horizontalWidgetsData.length} blocks with products`);
      } else {
        console.warn('[WidgetsHandler] No horizontal widgets data found in response');
      }
      

      console.log('-------------------------------horizontalWidgetsData', horizontalWidgetsData);
      
      // If we found horizontal widget data, update the component
      if (horizontalWidgetsData.length > 0) {
        if (this.widgets.horizontal && this.widgets.horizontal.updateData) {
          console.log('[WidgetsHandler] Updating horizontal widget with data');
          this.widgets.horizontal.updateData(horizontalWidgetsData);
        } else {
          console.log('[WidgetsHandler] Horizontal widget not ready, storing data for later');
          // Store the data to apply when the widget becomes available
          this.pendingData.horizontal = horizontalWidgetsData;
        }
      }
      
      // Process looks widget if available
      if (data.looks && Array.isArray(data.looks)) {
        console.log(`[WidgetsHandler] Processing ${data.looks.length} looks`);
        
        if (this.widgets.looks && this.widgets.looks.updateData) {
          this.widgets.looks.updateData(data.looks, data.special_look_image);
        } else if (data.looks.length > 0) {
          // Store looks data for when widget becomes available
          this.pendingData.looks = {
            looks: data.looks,
            special_look_image: data.special_look_image
          };
        }
      }
      
    } catch (error) {
      console.error('[WidgetsHandler] Error processing open page data:', error);
    }
  }

  /**
   * Initialize platform widget
   */
  platformInit() {
    if (this.widgets.platform && this.widgets.platform.init) {
      this.widgets.platform.init();
    }
  }

  /**
   * Get stored widget data
   * @returns {Promise<Object>} Widget data
   */
  // async getStoredData() {
  //   try {
  //     const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.WIDGET_DATA);
  //     return jsonValue != null ? JSON.parse(jsonValue) : null;
  //   } catch (error) {
  //     console.error('Error reading widget data:', error);
  //     return null;
  //   }
  // }

  /**
   * Initialize widgets with stored data
   */
  // async initializeWidgets() {
  //   try {
  //     const storedData = await this.getStoredData();
      
  //     if (storedData) {
  //       this.data = storedData;
        
  //       // Initialize looks widget
  //       if (storedData.looksData && 
  //           storedData.looksData.length > 0 && 
  //           this.widgets.looks && 
  //           this.widgets.looks.updateData) {
  //         this.widgets.looks.updateData(
  //           storedData.looksData, 
  //           storedData.special_look_image
  //         );
  //       }
        
  //       // Initialize horizontal widget
  //       if (storedData.horizontalwidgetsData && 
  //           storedData.horizontalwidgetsData.length > 0 && 
  //           this.widgets.horizontal && 
  //           this.widgets.horizontal.updateData) {
  //         this.widgets.horizontal.updateData(storedData.horizontalwidgetsData);
  //       }
  //     }
  //   } catch (error) {
  //     console.error('Error initializing widgets with stored data:', error);
  //   }
  // }
} 