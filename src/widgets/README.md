# React Native Widgets

This directory contains widget components for displaying product data in a React Native application.

## HorizontalWidget

The `HorizontalWidget` component displays products in a horizontal scrollable list, similar to the one in version2.

### Usage

```jsx
import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import WidgetsHandler from './react_native_sdk/src/widgets/WidgetsHandler';

const App = () => {
  // Create a WidgetsHandler instance with your API key
  const widgetsHandler = new WidgetsHandler('your-api-key');
  
  useEffect(() => {
    // Fetch data from your API and process it
    const fetchData = async () => {
      try {
        const response = await fetch('your-api-endpoint');
        const data = await response.json();
        
        // Process the data with WidgetsHandler
        await widgetsHandler.processOpenPageData(data);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    
    fetchData();
  }, []);
  
  return (
    <View style={styles.container}>
      <Text style={styles.header}>Products For You</Text>
      
      {/* Get and render the HorizontalWidget */}
      {widgetsHandler.getHorizontalWidget()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 16,
  },
});

export default App;
```

### How it works

1. Create a `WidgetsHandler` instance with your API key.
2. Call the `processOpenPageData` method with the data from your API.
3. Use `widgetsHandler.getHorizontalWidget()` to render the horizontal widget in your UI.

The `WidgetsHandler` automatically manages the state of the widget and updates it when new data is received.

## Advanced Usage

If you need more control over the widget, you can customize its appearance or behavior. See the `HorizontalWidget.js` file for more details. 