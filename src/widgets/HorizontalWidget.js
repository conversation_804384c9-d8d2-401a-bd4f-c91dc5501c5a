import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { FlatList, Image, Linking, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

const HorizontalWidget = forwardRef((props, ref) => {
  const [widgetBlocks, setWidgetBlocks] = useState(props.initialData || []);
  const [widgetsHandler, setWidgetsHandler] = useState(null);

  console.log('[HorizontalWidget] Rendering component, blocks:', widgetBlocks.length);
  console.log('[HorizontalWidget] Has initial data:', props.initialData ? `${props.initialData.length} items` : 'no');

  // Use initialData from props if provided
  useEffect(() => {
    if (props.initialData && Array.isArray(props.initialData) && props.initialData.length > 0) {
      console.log('[HorizontalWidget] Setting data from initialData props:', props.initialData.length);
      setWidgetBlocks(props.initialData);
    }
  }, [props.initialData]);

  // Expose updateData method to be called from WidgetsHandler
  useImperativeHandle(ref, () => ({
    updateData: (newData) => {
      console.log('[HorizontalWidget] updateData called with data:', 
        newData ? `array of ${newData.length} items` : 'no data');
      
      if (newData && Array.isArray(newData) && newData.length > 0) {
        console.log('[HorizontalWidget] Setting new widget data');
        setWidgetBlocks(newData);
        
        // Log the first block's details
        const firstBlock = newData[0];
        console.log('[HorizontalWidget] First block details:', {
          name: firstBlock.name || firstBlock.title || 'unnamed',
          productsCount: firstBlock.products?.length || 0
        });
      } else {
        console.warn('[HorizontalWidget] Invalid data received by updateData', newData);
      }
    },
  }));

  useEffect(() => {
    // Log widget data when it changes
    if (widgetsHandler && widgetsHandler.data?.horizontalwidgetsData?.length > 0) {
      console.log('[HorizontalWidget] Widget has data from widgetsHandler:', 
        `${widgetsHandler.data.horizontalwidgetsData.length} blocks`);
    }
  }, [widgetsHandler]);

  // Find image URL from product data
  const getProductImageUrl = (product) => {
    if (product.picture) {
      return product.picture;
    }
  };

  const renderProduct = ({ item: product }) => {
    const productId = product.id || product.yml_id || 'unknown';
    // console.log(`[HorizontalWidget] Rendering product ${productId}`);
    const imageUrl = getProductImageUrl(product);
    
    const handleProductPress = () => {
      if (product.link) {
        Linking.openURL(product.link);
      }
    };
    
    return (
      <View style={styles.productItem}>
        <TouchableOpacity 
          onPress={handleProductPress} 
          disabled={!product.link}
          style={styles.productLinkContainer}
        >
          {/* <Image 
            source={{ uri: imageUrl }} 
            style={styles.productImage}
            resizeMode="contain"
            onError={(e) => console.log(`[HorizontalWidget] Image error for ${productId}:`, e.nativeEvent.error)}
            //onLoad={() => console.log(`[HorizontalWidget] Image loaded for ${productId}`)}
          /> */}
          <Text style={styles.productName}>{product.name}</Text>
        </TouchableOpacity>
        {product.price && (
          <Text style={styles.productDetail}>
            {product.price_format || `${product.price}`}
          </Text>
        )}
      </View>
    );
  };

  // Render a single widget block with its products
  const renderWidgetBlock = ({ item: block }) => {
    console.log('renderWidgetBlock Block:', block);
    
    const blockName = block.name;
    const productsCount = block.products?.length || 0;
    console.log(`[HorizontalWidget] Rendering block "${blockName}" with ${productsCount} products`);
    
    return (
      <View style={styles.widgetBlock}>
        {block.title && <Text style={styles.blockTitle}>{block.title}</Text>}
        {!block.title && block.name && <Text style={styles.blockTitle}>{block.name}</Text>}
        <FlatList
          data={block.products || []}
          renderItem={renderProduct}
          keyExtractor={(product, index) => `product-${index}`}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.productList}
          onScrollBeginDrag={() => console.log(`[HorizontalWidget] Scrolling products in "${blockName}"`)}
        />
      </View>
    );
  };

  if (widgetBlocks.length === 0) {
    console.log('[HorizontalWidget] No widget blocks to display, returning null');
    return null; 
  }

  // Use a FlatList instead of ScrollView to avoid nested VirtualizedLists warning
  return (
    <View style={styles.container}>
      <Text style={styles.debugText}>!!!!!! Widget blocks: {widgetBlocks.length}</Text>
      <FlatList
        data={widgetBlocks}
        renderItem={renderWidgetBlock}
        keyExtractor={(block, index) => `block-${index}`}
        horizontal={false} // Vertical scrolling for the main list
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.blocksContainer}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    paddingVertical: 10,
    width: '100%',
  },
  blocksContainer: {
    paddingBottom: 10,
  },
  widgetBlock: {
    borderRadius: 8,
    padding: 10,
    marginBottom: 15,
  },
  blockTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  productList: {
    paddingVertical: 5,
  },
  productItem: {
    backgroundColor: '#ffffff',
    borderRadius: 6,
    padding: 8,
    marginRight: 10,
    width: 300,
    elevation: 2,
  },
  productImage: {
    width: '100%',
    height: 300,
    borderRadius: 4,
    marginBottom: 8,
  },
  productName: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  productDetail: {
    fontSize: 12,
    color: '#555',
  },
  debugText: {
    fontSize: 12,
    color: '#999',
    marginBottom: 5,
  },
  productLinkContainer: {
    width: '100%',
  },
});

export default HorizontalWidget; 