import AsyncStorage from '@react-native-async-storage/async-storage';
import { Dimensions, Platform } from 'react-native';
import Api from '../api/api';
import { cartState } from '../utils/cartState';
import { DEFAULT_OPTIONS, STORAGE_KEYS } from '../utils/constants';
import { globalState } from '../utils/globalState';
import WidgetsHandler from './WidgetsHandler';

/**
 * Main Widget class for Garderobo e-commerce recommendations
 */
export default class GarderoboWidget {
  /**
   * Create a new Widget instance
   * @param {string} apiKey - The API key for authentication
   */
  constructor(apiKey) {
    if (!apiKey) {
      console.error('Widget: API key is required');
      return;
    }

    this.isInitialized = false;
    this.api = null;
    this.callbacks = {};
    this.widgetApi = this._createWidgetApi();
    this.platform = this._detectPlatform();
    this.isMobile = this._detectMobile();
    this.isTouch = true; // React Native is always touch-based
    
    // Get device dimensions
    const { width, height } = Dimensions.get('window');
    this.viewportWidth = width;
    this.viewportHeight = height;
    
    this.classPrefix = 'garderobo'; // Default prefix for styling
    this.widgetsHandler = null;
    this.apiKey = apiKey;
    
    // Add dimension change listener
    Dimensions.addEventListener('change', this._handleResize.bind(this));
  }

  /**
   * Detect platform type
   * @returns {Object} Platform information
   * @private
   */
  _detectPlatform() {
    return {
      isIOS: Platform.OS === 'ios',
      isAndroid: Platform.OS === 'android',
      isWeb: Platform.OS === 'web',
      version: Platform.Version,
      platformName: Platform.OS,
    };
  }

  /**
   * Detect if device is mobile
   * @returns {boolean} True if mobile device
   * @private
   */
  _detectMobile() {
    // In React Native, we're almost always on mobile
    return Platform.OS === 'ios' || Platform.OS === 'android';
  }

  /**
   * Handle screen dimension changes
   * @private
   */
  _handleResize({ window }) {
    this.viewportWidth = window.width;
    this.viewportHeight = window.height;
    
    // Trigger any necessary layout adjustments
    if (this.isInitialized) {
      this._adjustLayout();
    }
  }

  /**
   * Adjust widget layout based on screen size
   * @private
   */
  _adjustLayout() {
    // This will be handled by React Native's responsive layout system
    // Additional custom adjustments can be added here if needed
  }

  /**
   * Helper to store data in AsyncStorage
   * @param {string} key - Storage key
   * @param {any} value - Value to store
   * @private
   */
  async _storeData(key, value) {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
    } catch (e) {
      console.error('Storage error:', e);
    }
  }

  /**
   * Helper to retrieve data from AsyncStorage
   * @param {string} key - Storage key
   * @returns {any} Retrieved value
   * @private
   */
  async _getData(key) {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (e) {
      console.error('Storage error:', e);
      return null;
    }
  }

  /**
   * Create the public widget API
   * @returns {Object} Widget API
   * @private
   */
  _createWidgetApi() {
    const self = this; // Store reference to this
    return {
      /**
       * Initialize the widget
       * @param {string} apiKey - The API key for authentication
       * @param {string} pageType - The type of page
       * @param {Object} options - Widget options
       * @returns {Object} Widget API
       */
      init: async (apiKey, pageType, options = {}) => {
        try {
          if (!apiKey) {
            return self.widgetApi;
          }

          // Update the API key if it was previously temporary
          if (self.apiKey === 'temp') {
            self.apiKey = apiKey;
          }

          const apiBaseUrl = options.apiBaseUrl || DEFAULT_OPTIONS.apiBaseUrl;
          
          self.api = new Api(apiBaseUrl, apiKey);
          self.pageType = pageType;
          self.options = {
            ...DEFAULT_OPTIONS,
            ...options,
            is_inline: options.is_inline || false,
            is_platform: options.is_platform || false,
          };

          globalState.setPageType(self.pageType);

          // Initialize session
          await self._initializeSession();
          
          // Initialize widget parameters
          await self._initializeWidgetParams();
          
          // Initialize widgets handler
          self.widgetsHandler = new WidgetsHandler(
            self.apiKey,
            self.callbacks,
            self.options
          );
          
          // Load data for the widget
          const openPageData = await self._fetchOpenPageData();
          if (openPageData) {
            self.widgetsHandler.processOpenPageData(openPageData);
          }

          self.isInitialized = true;
          
          // Call the onInitialized callback if registered
          if (self.callbacks.onInitialized && typeof self.callbacks.onInitialized === 'function') {
            try {
              self.callbacks.onInitialized();
            } catch (callbackError) {
              console.warn('Error in onInitialized callback:', callbackError);
              // Don't let callback errors break the initialization
            }
          }
          
          console.log('Widget initialized successfully');
          
          return self.widgetApi;
        } catch (error) {
          console.error('Error initializing widget:', error);
          return self.widgetApi;
        }
      },
      
      /**
       * Register a callback function
       * @param {string} event - Event name
       * @param {Function} callback - Callback function
       * @returns {Object} Widget API
       */
      registerCallback: (event, callback) => {
        if (typeof callback === 'function') {
          self.callbacks[event] = callback;
        }
        return self.widgetApi;
      },
      
      /**
       * Add a product to the cart
       * @param {string} productId - Product ID
       * @returns {Object} Widget API
       */
      addToCart: (productId) => {
        if (productId && self.callbacks.addToCart) {
          self.callbacks.addToCart(productId);
          
          // Update cart state
          cartState.addToCart(productId);
        }
        return self.widgetApi;
      },
      
      /**
       * Get the current page type
       * @returns {string} Page type
       */
      getPageType: () => self.pageType,
      
      /**
       * Get the widget options
       * @returns {Object} Widget options
       */
      getOptions: () => self.options,
      
      /**
       * Get API key
       * @returns {string} API key
       */
      getApiKey: () => self.apiKey,
      
      /**
       * Check if widget is initialized
       * @returns {boolean} Is initialized
       */
      isInitialized: () => self.isInitialized,
      
      /**
       * Refresh the widget data
       * @returns {Promise<Object>} Widget API
       */
      refresh: async () => {
        if (self.isInitialized) {
          try {
            const openPageData = await self._fetchOpenPageData();
            if (openPageData && self.widgetsHandler) {
              self.widgetsHandler.processOpenPageData(openPageData);
            }
          } catch (error) {
            console.error('Error refreshing widget:', error);
          }
        }
        return self.widgetApi;
      }
    };
  }

  /**
   * Initialize session for authentication
   * @private
   */
  async _initializeSession() {
    try {
      console.log('[Widget] Initializing session');
      
      // Check for existing session key
      let sessionKey = await this._getData(STORAGE_KEYS.SESSION_KEY);
      console.log(`[Widget] Existing session key: ${sessionKey ? 'found' : 'not found'}`);
      
      if (!sessionKey) {
        const initData = {
          platform: Platform.OS
          // Simplified data payload to avoid potential issues
        };

        // Add user info if available
        if (this.options.userId) initData.user_id = this.options.userId;
        if (this.options.email) initData.email = this.options.email;
        
        console.log('[Widget] Creating new session');
        
        try {
          const sessionResponse = await this.api.createSession(initData);
          
          if (sessionResponse && sessionResponse.session) {
            sessionKey = sessionResponse.session;
            await this._storeData(STORAGE_KEYS.SESSION_KEY, sessionKey);
            console.log('[Widget] New session created successfully');
          } else {
            console.warn('[Widget] No session in response, using fallback');
            // Create a fallback session key
            sessionKey = 'temp-' + Date.now();
            await this._storeData(STORAGE_KEYS.SESSION_KEY, sessionKey);
          }
        } catch (sessionError) {
          console.error('[Widget] Session creation error:', sessionError.message);
          // Use fallback session
          sessionKey = 'temp-' + Date.now();
          await this._storeData(STORAGE_KEYS.SESSION_KEY, sessionKey);
          console.log('[Widget] Using fallback session key');
        }
      }
      
      // Set the session key for the API
      if (sessionKey) {
        console.log('[Widget] Setting session key for API requests');
        this.api.setSessionKey(sessionKey);
      }
    } catch (error) {
      console.error('[Widget] Error initializing session:', error);
      // Always ensure the widget continues to initialize even if session fails
    }
  }

  /**
   * Initialize widget parameters
   * @private
   */
  async _initializeWidgetParams() {
    try {
      console.log('[Widget] Initializing widget parameters');
      
      // Default minimal params in case API fails
      const defaultParams = {
        widgets: {
          horizontalwidgets: true,
          looks: false
        }
      };
      
      this.widgetParams = defaultParams; // Set defaults first
      
      try {
        // Check for cached widget parameters
        const cachedParams = await this._getData(STORAGE_KEYS.WIDGET_PARAMS);
        const lastUpdate = await this._getData(STORAGE_KEYS.WIDGET_PARAMS_LAST_UPDATE);
        
        // Check if params are fresh enough (less than 1 hour old)
        const isParamsFresh = lastUpdate && (new Date() - new Date(lastUpdate) < 3600000);
        
        if (cachedParams && isParamsFresh) {
          console.log('[Widget] Using cached widget parameters');
          this.widgetParams = cachedParams;
          return;
        }
  
        const params = {
          page_type: this.pageType
        };
        
        // Add optional parameters
        if (this.options.productId) params.product_id = this.options.productId;
        if (this.options.categoryId) params.category_id = this.options.categoryId;
        
        console.log('[Widget] Fetching widget parameters');
        
        // Fetch fresh parameters from API
        const paramsResponse = await this.api.getWidgetParams(params);
        
        if (paramsResponse) {
          console.log('[Widget] Received widget parameters from API');
          this.widgetParams = paramsResponse;
          await this._storeData(STORAGE_KEYS.WIDGET_PARAMS, paramsResponse);
          await this._storeData(STORAGE_KEYS.WIDGET_PARAMS_LAST_UPDATE, new Date().toISOString());
        }
      } catch (error) {
        console.error('[Widget] Error fetching widget parameters:', error.message);
        // Continue with default parameters
      }
    } catch (error) {
      console.error('Error initializing widget parameters:', error);
      // Ensure we continue with defaults
    }
  }

  /**
   * Fetch data for the open page
   * @returns {Object} Page data
   * @private
   */
  async _fetchOpenPageData() {
    try {
      const { pageType, options } = this;
      
      // Always prepare the fallback data first
      const fallbackData = this._getFallbackData();
      
      // Retrieve the session key
      const sessionKey = await this._getData(STORAGE_KEYS.SESSION_KEY);
      console.log(`[Widget] Fetching open page data for ${pageType}, session key exists: ${Boolean(sessionKey)}`);
      
      // Prepare parameters
      const params = {
        page_type: this.pageType,
        session_key: sessionKey || 'temp-session'
      };
      
      // For product pages
      if (pageType === 'product' && options.productId) {
        params.yml_id = options.productId;
      }
      
      // For category pages
      if (pageType === 'category' && options.categoryId) {
        params.categoryId = options.categoryId;
      }
      
      // For search pages
      if (pageType === 'search' && options.searchQuery) {
        params.searchQuery = options.searchQuery;
      }
      
      console.log('[Widget] Open page request params:', JSON.stringify(params));
      
      try {
        // Call the API
        const openPageData = await this.api.getOpenPageData(params);
        
        if (openPageData) {
          console.log('[Widget] Received open page data successfully');
          return openPageData;
        } else {
          console.warn('[Widget] Empty data received from API, using fallback');
          return fallbackData;
        }
      } catch (apiError) {
        console.error('[Widget] Error in API call:', apiError.message);
        console.log('[Widget] Using fallback data');
        return fallbackData;
      }
    } catch (error) {
      console.error('[Widget] Error fetching open page data:', error.message);
      return this._getFallbackData();
    }
  }
  
  /**
   * Get fallback data when API calls fail
   * @returns {Object} Fallback data
   * @private
   */
  _getFallbackData() {
    console.log('[Widget] Creating fallback data');
    return {
      blocks: [
        {
          name: "Sample Products",
          title: "Sample Products (Fallback)",
          products: [
            {
              id: "sample1",
              name: "Sample Product 1",
              picture: "https://via.placeholder.com/300",
              price: "99.99",
              price_format: "$99.99",
              link: "#"
            },
            {
              id: "sample2",
              name: "Sample Product 2",
              picture: "https://via.placeholder.com/300",
              price: "199.99",
              price_format: "$199.99",
              link: "#"
            }
          ]
        }
      ]
    };
  }
} 