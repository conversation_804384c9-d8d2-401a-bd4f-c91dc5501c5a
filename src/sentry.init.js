import * as Sentry from '@sentry/browser';

Sentry.init({
    dsn: 'https://<EMAIL>//2',
    // #if process.env.VERSION
    release: process.env.VERSION,
    // #endif
    environment: process.env.NODE_ENV,
    // #if process.env.NODE_ENV !== 'production'
    debug: true,
    // #endif
    defaultIntegrations: false,
    sampleRate: 0.01,
    beforeSend: (event) => {
        try {
            event.request = {
                headers: {
                    'User-Agent': window.navigator.userAgent,
                },
            };
        } catch (e) {}

        // #if process.env.NODE_ENV === 'develop'
        console.warn('Sen<PERSON> would like to log this', event);
        return null;
        // #endif

        return event;
    },
});

export * from '@sentry/browser';
