import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import GarderoboWidget from '../widgets/GarderoboWidget';
import HorizontalWidget from '../widgets/HorizontalWidget';

const DemoScreen = () => {
  const [apiKey, setApiKey] = useState('c14000e3b5d24026a6816874b6a177bb');
  const [pageType, setPageType] = useState('product');
  const [productId, setProductId] = useState('5257702');
  const [widgetInstance, setWidgetInstance] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);
  const [deviceInfo, setDeviceInfo] = useState({});
  const [storageItems, setStorageItems] = useState({});
  
  // Initialize device info on mount
  useEffect(() => {
    console.log('DemoScreen mounted');
    const { width, height } = Dimensions.get('window');
    setDeviceInfo({
      platform: Platform.OS,
      version: Platform.Version,
      dimensions: { width, height },
      isTablet: width > 768, // Simple tablet detection
    });
    
    // Listen for dimension changes
    const dimensionsListener = Dimensions.addEventListener('change', ({ window }) => {
      setDeviceInfo(prev => ({
        ...prev,
        dimensions: { width: window.width, height: window.height },
        isTablet: window.width > 768,
      }));
    });
    
    return () => {
      dimensionsListener.remove();
    };
  }, []);
  
  // Initialize widget
  const initializeWidget = async () => {
    try {
      setIsLoading(true);
      addLog('Initializing widget...');
      
      const widget = new GarderoboWidget(apiKey);
      setWidgetInstance(widget);
      
      // Register callbacks before initialization
      widget.widgetApi.registerCallback('onInitialized', () => {
        addLog('Widget initialized successfully');
        setIsInitialized(true);
        
        // Add debug log for widget data after initialization
        if (widget.widgetsHandler && widget.widgetsHandler.data) {
          const { horizontalwidgetsData } = widget.widgetsHandler.data;
          // console.log('Horizontal widgets data after initialization:', horizontalwidgetsData);
          
          // Log sample product to check image fields
          if (horizontalwidgetsData.length > 0 && 
              horizontalwidgetsData[0].products && 
              horizontalwidgetsData[0].products.length > 0) {
            const sampleProduct = horizontalwidgetsData[0].products[0];
            console.log('Sample product structure:', sampleProduct);
            console.log('Available image fields:', 
              Object.keys(sampleProduct).filter(key => 
                key.includes('image') || key.includes('picture') || key.includes('photo')
              )
            );

            console.log('Horizontal widgets data after initialization:', horizontalwidgetsData[0]);
          }
          
          addLog(`Found ${horizontalwidgetsData.length} horizontal widgets to display`);
        }
      });
      
      widget.widgetApi.registerCallback('productClick', (productId) => {
        addLog(`Product clicked: ${productId}`);
      });
      
      widget.widgetApi.registerCallback('addToCart', (productId) => {
        addLog(`Product added to cart: ${productId}`);
      });
      
      // Initialize the widget
      await widget.widgetApi.init(
        apiKey,
        pageType,
        {
          productId,
          langCode: 'en',
        }
      );
      
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      addLog(`Error initializing widget: ${error.message}`);
      console.error('Error initializing widget:', error);
    }
  };
  
  // Debug: Check AsyncStorage items
  const checkStorageItems = async () => {
    try {
      addLog('Checking AsyncStorage items...');
      
      // Get all keys
      const keys = await AsyncStorage.getAllKeys();
      addLog(`Found ${keys.length} items in AsyncStorage`);
      
      // Get all items
      const result = {};
      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        result[key] = value;
        addLog(`Key: ${key}, Value: ${value?.substring(0, 30)}${value?.length > 30 ? '...' : ''}`);
      }
      
      setStorageItems(result);
    } catch (error) {
      addLog(`Error checking storage: ${error.message}`);
    }
  };
  
  // Refresh widget data
  const refreshWidget = async () => {
    if (!widgetInstance || !isInitialized) {
      addLog('Widget is not initialized yet');
      return;
    }
    
    try {
      setIsLoading(true);
      addLog('Refreshing widget data...');
      
      await widgetInstance.widgetApi.refresh();
      
      // Add debug log to check widget data
      if (widgetInstance.widgetsHandler && widgetInstance.widgetsHandler.data) {
        const { horizontalwidgetsData } = widgetInstance.widgetsHandler.data;
        console.log('Horizontal widgets data:', horizontalwidgetsData);
        addLog(`Found ${horizontalwidgetsData.length} horizontal widgets to display`);
      }
      
      setIsLoading(false);
      addLog('Widget data refreshed');
    } catch (error) {
      setIsLoading(false);
      addLog(`Error refreshing widget: ${error.message}`);
      console.error('Error refreshing widget:', error);
    }
  };
  
  // Check widget status
  const checkWidgetStatus = () => {
    if (!widgetInstance) {
      addLog('Widget is not created yet');
      return;
    }
    
    const status = widgetInstance.widgetApi.isInitialized() ? 'Initialized' : 'Not initialized';
    addLog(`Widget status: ${status}`);
    
    if (widgetInstance.widgetApi.isInitialized()) {
      const options = widgetInstance.widgetApi.getOptions();
      addLog(`Page type: ${widgetInstance.widgetApi.getPageType()}`);
      addLog(`API key: ${widgetInstance.widgetApi.getApiKey()}`);
      addLog(`Options: ${JSON.stringify(options, null, 2)}`);
    }
  };
  
  // Add log message
  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [{ timestamp, message }, ...prev].slice(0, 20)); // Keep last 20 logs
  };
  
  // Clear logs
  const clearLogs = () => {
    setLogs([]);
  };
  
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Garderobo Widget Demo</Text>
        <Text style={styles.subtitle}>aaaaaReact Native SDK1111</Text>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Widget Configuration</Text>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>API Key:</Text>
          <TextInput
            style={styles.input}
            value={apiKey}
            onChangeText={setApiKey}
            placeholder="Enter API key"
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Page Type:</Text>
          <View style={styles.segmentedControl}>
            {['product'].map(type => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.segmentButton,
                  pageType === type && styles.segmentButtonActive
                ]}
                onPress={() => setPageType(type)}
              >
                <Text 
                  style={[
                    styles.segmentButtonText,
                    pageType === type && styles.segmentButtonTextActive
                  ]}
                >
                  {type}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        
        {pageType === 'product' && (
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Product ID:</Text>
            <TextInput
              style={styles.input}
              value={productId}
              onChangeText={setProductId}
              placeholder="Enter product ID"
            />
          </View>
        )}
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.button}
            onPress={initializeWidget}
            disabled={isLoading}
          >
            <Text style={styles.buttonText}>
              {isInitialized ? 'Reinitialize Widget' : 'Initialize Widget'}
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, !isInitialized && styles.buttonDisabled]}
            onPress={refreshWidget}
            disabled={!isInitialized || isLoading}
          >
            <Text style={styles.buttonText}>Refresh Widget</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, !widgetInstance && styles.buttonDisabled]}
            onPress={checkWidgetStatus}
            disabled={!widgetInstance || isLoading}
          >
            <Text style={styles.buttonText}>Check Status</Text>
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity
          style={[styles.button, styles.storageButton]}
          onPress={checkStorageItems}
        >
          <Text style={styles.buttonText}>Check AsyncStorage</Text>
        </TouchableOpacity>
        
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#3498db" />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        )}
      </View>
      
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Device Information</Text>
        </View>
        
        <View style={styles.deviceInfo}>
          <View style={styles.deviceInfoItem}>
            <Text style={styles.deviceInfoLabel}>Platform:</Text>
            <Text style={styles.deviceInfoValue}>{deviceInfo.platform}</Text>
          </View>
          
          <View style={styles.deviceInfoItem}>
            <Text style={styles.deviceInfoLabel}>Version:</Text>
            <Text style={styles.deviceInfoValue}>{deviceInfo.version}</Text>
          </View>
          
          <View style={styles.deviceInfoItem}>
            <Text style={styles.deviceInfoLabel}>Screen:</Text>
            <Text style={styles.deviceInfoValue}>
              {deviceInfo.dimensions ? `${deviceInfo.dimensions.width} × ${deviceInfo.dimensions.height}` : 'Unknown'}
            </Text>
          </View>
          
          <View style={styles.deviceInfoItem}>
            <Text style={styles.deviceInfoLabel}>Type:</Text>
            <Text style={styles.deviceInfoValue}>
              {deviceInfo.isTablet ? 'Tablet' : 'Phone'}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Event Logs</Text>
          <TouchableOpacity onPress={clearLogs} style={styles.clearButton}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.logContainer}>
          {logs.length === 0 ? (
            <Text style={styles.emptyLogText}>No logs yet</Text>
          ) : (
            logs.map((log, index) => (
              <View key={index} style={styles.logEntry}>
                <Text style={styles.logTimestamp}>{log.timestamp}</Text>
                <Text style={styles.logMessage}>{log.message}</Text>
              </View>
            ))
          )}
        </View>
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Woooooidget Content 111111</Text>
        <View style={[
          styles.widgetContainer,
          isInitialized && widgetInstance && widgetInstance.widgetsHandler ? 
            { alignItems: 'stretch', justifyContent: 'flex-start' } : 
            { alignItems: 'center', justifyContent: 'center' }
        ]}>
          {/* {console.log('[DemoScreen] Widget container render', {
            isInitialized,
            hasWidgetInstance: !!widgetInstance,
            hasWidgetsHandler: widgetInstance?.widgetsHandler ? true : false,
            blocksCount: widgetInstance?.widgetsHandler?.data?.horizontalwidgetsData?.length
          })} */}
          {isInitialized && widgetInstance && widgetInstance.widgetsHandler ? (
            <>
              <Text style={styles.debugText}>
                Widget handler initialized with {widgetInstance.widgetsHandler.data?.horizontalwidgetsData?.length || 0} blocks
              </Text>
              {widgetInstance.widgetsHandler.getHorizontalWidget()}
              {widgetInstance.widgetsHandler.data?.horizontalwidgetsData?.length > 0 && (
                <View style={{marginTop: 20}}>
                  <Text style={styles.debugText}>Direct HorizontalWidget with props:</Text>
                  <HorizontalWidget 
                    initialData={widgetInstance.widgetsHandler.data.horizontalwidgetsData}
                  />
                </View>
              )}
            </>
          ) : (
            <Text style={styles.placeholder}>
              {isInitialized
                ? 'Widget content will appear here once components are implemented'
                : 'Initialize the widget to see content'}
            </Text>
          )}
        </View>
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Garderobo Widget SDK • {new Date().getFullYear()}
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 20,
    backgroundColor: '#2c3e50',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#ecf0f1',
  },
  section: {
    marginVertical: 10,
    padding: 15,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 10,
  },
  inputContainer: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#34495e',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 10,
    fontSize: 16,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#3498db',
    borderRadius: 4,
    overflow: 'hidden',
  },
  segmentButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  segmentButtonActive: {
    backgroundColor: '#3498db',
  },
  segmentButtonText: {
    fontSize: 12,
    color: '#3498db',
  },
  segmentButtonTextActive: {
    color: '#fff',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  button: {
    flex: 1,
    backgroundColor: '#3498db',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 4,
    alignItems: 'center',
    marginHorizontal: 5,
  },
  buttonDisabled: {
    backgroundColor: '#bdc3c7',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 14,
  },
  loadingContainer: {
    alignItems: 'center',
    marginTop: 20,
  },
  loadingText: {
    marginTop: 10,
    color: '#7f8c8d',
  },
  deviceInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  deviceInfoItem: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    padding: 10,
    marginVertical: 5,
    borderRadius: 4,
  },
  deviceInfoLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 5,
  },
  deviceInfoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2c3e50',
  },
  logContainer: {
    backgroundColor: '#2c3e50',
    borderRadius: 4,
    padding: 10,
    maxHeight: 200,
  },
  emptyLogText: {
    color: '#bdc3c7',
    textAlign: 'center',
    paddingVertical: 20,
  },
  logEntry: {
    borderBottomWidth: 1,
    borderBottomColor: '#34495e',
    paddingVertical: 8,
  },
  logTimestamp: {
    fontSize: 12,
    color: '#95a5a6',
    marginBottom: 2,
  },
  logMessage: {
    fontSize: 14,
    color: '#ecf0f1',
  },
  clearButton: {
    backgroundColor: '#e74c3c',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 4,
  },
  clearButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  widgetContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
    borderRadius: 4,
    padding: 10,
    minHeight: 200,
    width: '100%'
  },
  placeholder: {
    color: '#95a5a6',
    textAlign: 'center',
  },
  footer: {
    padding: 20,
    alignItems: 'center',
  },
  footerText: {
    color: '#7f8c8d',
    fontSize: 12,
  },
  storageButton: {
    backgroundColor: '#2ecc71',
    marginTop: 10,
  },
  debugText: {
    color: '#2c3e50',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 10,
  },
});

export default DemoScreen; 