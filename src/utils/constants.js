/**
 * Storage keys for AsyncStorage
 */
export const STORAGE_KEYS = {
  SESSION_KEY: '_garderoboSessionKey',
  WIDGET_PARAMS: '_garderoboWidgetParams',
  WIDGET_PARAMS_LAST_UPDATE: '_garderoboWidgetParamsLastUpdate',
  LAST_ITEMS: '_garderoboLastItemss',
  LAST_ITEMS_IDS: '@garderobo_last_items_ids',
  AB_TEST: '_garderoboLastItemsIds',
  LANG_CODE: '_garderoboLangCode'
};

/**
 * Default widget options
 */
export const DEFAULT_OPTIONS = {
  apiBaseUrl: 'https://api.garderobo.ai/api/v3/widget/',
  statsUrl: 'https://stat.garderobo.ai/collect',
  langCode: 'en',
  // maxItems: 10,
  // showPrice: true,
  // showAddToCart: true,
  // showTitle: true,
  // enableAnalytics: true,
  // timeout: 10000 // API request timeout in ms
}; 