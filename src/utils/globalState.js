/**
 * Simple global state management for the widget
 */
class GlobalState {
  constructor() {
    this.state = {
      pageType: null,
      isMounted: false,
      isLoading: false,
      error: null,
      language: 'en',
      theme: 'light'
    };
    
    this.listeners = [];
  }
  
  /**
   * Set page type
   * @param {string} pageType - Page type
   */
  setPageType(pageType) {
    this.setState({ pageType });
  }
  
  /**
   * Set loading state
   * @param {boolean} isLoading - Loading state
   */
  setLoading(isLoading) {
    this.setState({ isLoading });
  }
  
  /**
   * Set error state
   * @param {Error|null} error - Error object or null
   */
  setError(error) {
    this.setState({ error });
  }
  
  /**
   * Set language
   * @param {string} language - Language code
   */
  setLanguage(language) {
    this.setState({ language });
  }
  
  /**
   * Set theme
   * @param {string} theme - Theme name
   */
  setTheme(theme) {
    this.setState({ theme });
  }
  
  /**
   * Set mounted state
   * @param {boolean} isMounted - Mounted state
   */
  setMounted(isMounted) {
    this.setState({ isMounted });
  }
  
  /**
   * Set state
   * @param {Object} newState - New state
   */
  setState(newState) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }
  
  /**
   * Get current state
   * @returns {Object} Current state
   */
  getState() {
    return { ...this.state };
  }
  
  /**
   * Subscribe to state changes
   * @param {Function} listener - Listener function
   * @returns {Function} Unsubscribe function
   */
  subscribe(listener) {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }
  
  /**
   * Notify all listeners
   */
  notifyListeners() {
    this.listeners.forEach(listener => {
      listener(this.state);
    });
  }
}

// Create and export singleton instance
export const globalState = new GlobalState(); 