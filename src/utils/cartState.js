/**
 * Cart state management for the widget
 */
class CartState {
  constructor() {
    this.items = [];
    this.listeners = [];
  }
  
  /**
   * Add a product to the cart
   * @param {string} productId - Product ID
   * @param {number} quantity - Quantity to add
   */
  addToCart(productId, quantity = 1) {
    // Check if product already exists in cart
    const existingItemIndex = this.items.findIndex(item => item.productId === productId);
    
    if (existingItemIndex >= 0) {
      // Update quantity if product exists
      this.items[existingItemIndex].quantity += quantity;
    } else {
      // Add new product to cart
      this.items.push({
        productId,
        quantity,
        addedAt: new Date().toISOString()
      });
    }
    
    this.notifyListeners();
  }
  
  /**
   * Remove a product from the cart
   * @param {string} productId - Product ID
   */
  removeFromCart(productId) {
    this.items = this.items.filter(item => item.productId !== productId);
    this.notifyListeners();
  }
  
  /**
   * Update product quantity in cart
   * @param {string} productId - Product ID
   * @param {number} quantity - New quantity
   */
  updateQuantity(productId, quantity) {
    if (quantity <= 0) {
      this.removeFromCart(productId);
      return;
    }
    
    const existingItemIndex = this.items.findIndex(item => item.productId === productId);
    
    if (existingItemIndex >= 0) {
      this.items[existingItemIndex].quantity = quantity;
      this.notifyListeners();
    }
  }
  
  /**
   * Clear the cart
   */
  clearCart() {
    this.items = [];
    this.notifyListeners();
  }
  
  /**
   * Get cart items
   * @returns {Array} Cart items
   */
  getItems() {
    return [...this.items];
  }
  
  /**
   * Get cart item count
   * @returns {number} Item count
   */
  getItemCount() {
    return this.items.reduce((total, item) => total + item.quantity, 0);
  }
  
  /**
   * Check if product is in cart
   * @param {string} productId - Product ID
   * @returns {boolean} Is in cart
   */
  isInCart(productId) {
    return this.items.some(item => item.productId === productId);
  }
  
  /**
   * Subscribe to cart changes
   * @param {Function} listener - Listener function
   * @returns {Function} Unsubscribe function
   */
  subscribe(listener) {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }
  
  /**
   * Notify all listeners
   */
  notifyListeners() {
    this.listeners.forEach(listener => {
      listener(this.items);
    });
  }
}

// Create and export singleton instance
export const cartState = new CartState(); 