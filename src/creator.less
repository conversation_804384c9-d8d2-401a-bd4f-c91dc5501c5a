@prefix: garderobo-creator;

.@{prefix} {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 14px;
    max-width: 1280px;
    margin: 0 auto;
    overflow: hidden;
    margin-bottom: 40px;
    clear: both;
    color: #333;
}

.@{prefix}-header {
    font-size: 18px;
    font-weight: 400;
    text-align: center;
    margin: 0 0 40px 0;
}

.@{prefix}-products-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 60px;
    margin-bottom: 30px;
    padding: 0 15px;
}

.@{prefix}-product {
    display: block;
    position: relative;
    box-sizing: border-box;
    border: 1px solid #ccc;
    color: #333;
    height: 180px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    margin-bottom: 5px;
    &--add-new {
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
    }
    &--new {
        padding: 20px;
    }
    &--main {
        border-color: tomato;
        border-width: 2px;
    }

    .@{prefix}-input-text {
        width: 100%;
        margin-bottom: 10px;
    }

    .@{prefix}-btn {
        width: 100%;
    }
}

.@{prefix}-product__name {
    margin-bottom: 5px;
}

.@{prefix}-product__name-link {
    text-decoration: none;
    color: inherit;
    &:hover {
        color: blue;
    }
}

.@{prefix}-product__price {
    font-size: 18px;
}

.@{prefix}-product__btn-left,
.@{prefix}-product__btn-right,
.@{prefix}-product__btn-delete,
.@{prefix}-product__btn-refresh {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    &:hover {
        background-color: rgba(220, 220, 220, 0.8);
    }
}

.@{prefix}-product__btn-delete {
    top: -15px;
    left: -15px;
    font-size: 30px;
}

.@{prefix}-product__btn-refresh {
    top: -15px;
    right: -15px;
    font-size: 30px;
    &::after {
        content: '\27F3';
        transform: translate(1px, -1px);
    }
}

.@{prefix}-product__btn-left,
.@{prefix}-product__btn-right {
    bottom: 5px;
    font-size: 20px;
    &::after {
        content: '\1433';
        transform: rotateY(45deg);
    }
}

.@{prefix}-product__btn-left {
    transform: rotate(-180deg);
    left: 5px;
}

.@{prefix}-product__btn-right {
    right: 5px;
}

.@{prefix}-product__btn-new {
    position: relative;
    width: 100px;
    height: 100px;
    background-size: 10px 10px;
    background-image: repeating-linear-gradient(45deg, rgb(221, 221, 221) 0px, rgb(221, 221, 221) 1px, rgb(255, 255, 255) 0px, rgb(255, 255, 255) 50%);
    &::after {
        content: '';
        position: absolute;
        width: 1px;
        background-color: #333;
        top: 0;
        bottom: 0;
    }
    &::before {
        content: '';
        position: absolute;
        height: 1px;
        background-color: #333;
        left: 0;
        right: 0;
    }
}

.@{prefix}-bottom-controls-wrapper {
    padding: 20px 0;
    box-shadow: 0 -2px 7px -5px;
    border-bottom: 1px solid #ddd;
}

.@{prefix}-bottom-controls {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    max-width: 700px;
    padding: 0 15px;
    margin: 0 auto;

    .@{prefix}-btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

.@{prefix}-btn {
    border: 1px solid #ddd;
    padding: 10px 20px;
    font-size: 14px;
    &:hover {
        background-color: #f5f5f5;
    }
}

.@{prefix}-input-text {
    border: 1px solid #ddd;
    padding: 10px 20px;
    font-size: 14px;
    box-sizing: border-box;
}

.@{prefix}-btn-link {
    font-size: 14px;
    width: 50%;
    margin-bottom: 20px;

    &:hover {
        color: blue;
    }
    &--prev {
        &::before {
            content: '< ';
        }
        
    }
    &--next {
        &::after {
            content: ' >';
        }
    }
}

@media screen and (min-width: 425px) {
    .@{prefix}-product {
        height: 300px;
    }
    .@{prefix}-bottom-controls{
        .@{prefix}-btn {
            width: calc(33.3% - 10px);
            margin: 0 5px 5px;
        }
    }
}

@media screen and (min-width: 768px) {
    .@{prefix}-products-container {
        grid-template-columns: 1fr 1fr 1fr;
    }
    .@{prefix}-bottom-controls {
        .@{prefix}-btn {
            width: 120px;
        }
    }
}

@media screen and (min-width: 1024px) {
    .@{prefix}-products-container {
        grid-template-columns: repeat(5, 1fr);
    }
}
