/**
 * API class for making requests to the Garderobo backend
 */

export default class Api {
  /**
   * Create a new API instance
   * @param {string} baseUrl - Base URL for the API
   * @param {string} apiKey - API key for authentication
   */
  constructor(baseUrl, apiKey) {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
    this.sessionKey = null;
    this.timeout = 10000; // Default timeout 10 seconds
    
    // Detect platform but don't use in headers (causing CORS issues)
    this.platform = require('react-native').Platform.OS;
  }
  
  /**
   * Set session key for authentication
   * @param {string} sessionKey - Session key
   */
  setSessionKey(sessionKey) {
    this.sessionKey = sessionKey;
  }
  
  /**
   * Set request timeout
   * @param {number} timeout - Timeout in milliseconds
   */
  setTimeout(timeout) {
    this.timeout = timeout;
  }
  
  /**
   * Make a GET request to the API
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} Response data
   */
  async get(endpoint, params = {}) {
    return this._request('GET', endpoint, params);
  }
  
  /**
   * Make a POST request to the API
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @returns {Promise<Object>} Response data
   */
  async post(endpoint, data = {}) {
    return this._request('POST', endpoint, data);
  }
  
  /**
   * Make an API request
   * @param {string} method - HTTP method
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data or parameters
   * @returns {Promise<Object>} Response data
   * @private
   */
  async _request(method, endpoint, data = {}) {
    try {
      // We'll handle iOS-specific cases but with a more compatible approach
      const url = this._buildUrl(endpoint);
      
      // Build request options
      const options = {
        method,
        headers: this._getHeaders(),
        timeout: this.timeout,
      };
      
      // Add body for POST requests
      if (method === 'POST') {
        options.body = JSON.stringify(data);
      }
      
      // Create query string for GET requests
      const queryParams = method === 'GET' ? this._createQueryString(data) : '';
      
      const fullUrl = `${url}${queryParams}`;
      console.log(`[API] ${method} request to: ${fullUrl}`);
      
      // Simple fetch approach that works on both platforms
      const response = await fetch(fullUrl, options);
      
      // Log response status
      console.log(`[API] Response status: ${response.status}`);
      
      // Check response status
      if (!response.ok) {
        console.error(`API error: ${response.status}`);
        throw new Error(`API error: ${response.status}`);
      }
      
      // Parse response as JSON
      const responseData = await response.json();
      return responseData;
    } catch (error) {
      console.error(`API error in ${method} ${endpoint}:`, error);
      throw error;
    }
  }
  
  /**
   * Build URL for the API request
   * @param {string} endpoint - API endpoint
   * @returns {string} Full URL
   * @private
   */
  _buildUrl(endpoint) {
    let url = this.baseUrl;
    
    // Ensure URL has trailing slash
    if (!url.endsWith('/')) {
      url += '/';
    }
    
    // Remove leading slash from endpoint
    if (endpoint.startsWith('/')) {
      endpoint = endpoint.substring(1);
    }
    
    return `${url}${endpoint}`;
  }
  
  /**
   * Get headers for the API request
   * @returns {Object} Headers
   * @private
   */
  _getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-API-KEY': this.apiKey
      // Removing custom headers that caused CORS issues
    };
    
    // Add authorization header if session key is available
    if (this.sessionKey) {
      headers['Authorization'] = `Bearer ${this.sessionKey}`;
    }
    
    return headers;
  }
  
  /**
   * Create query string from parameters
   * @param {Object} params - Query parameters
   * @returns {string} Query string
   * @private
   */
  _createQueryString(params) {
    if (!params || Object.keys(params).length === 0) {
      return '';
    }
    
    const queryString = Object.entries(params)
      .filter(([_, value]) => value !== null && value !== undefined)
      .map(([key, value]) => {
        if (Array.isArray(value)) {
          return value.map(item => `${key}[]=${encodeURIComponent(item)}`).join('&');
        }
        return `${key}=${encodeURIComponent(value)}`;
      })
      .join('&');
    
    return queryString ? `?${queryString}` : '';
  }
  
  /**
   * Create a new session
   * @returns {Promise<Object>} Session data
   */
  async createSession(data) {
    return this.post('start_session/', data);
  }
  
  /**
   * Get widget parameters
   * @returns {Promise<Object>} Widget parameters
   */
  async getWidgetParams(params) {
    return this.post('widget_params/', params);
  }
  
  /**
   * Get open page data
   * @param {Object} params - Request parameters
   * @returns {Promise<Object>} Open page data
   */
  async getOpenPageData(params) {
    return this.post('open_page/', params);
  }
  
  /**
   * Track product view
   * @param {string} productId - Product ID
   * @returns {Promise<Object>} Tracking response
   */
  async trackProductView(productId) {
    return this.post('track', {
      event: 'product_view',
      product_id: productId
    });
  }
  
  /**
   * Track product click
   * @param {string} productId - Product ID
   * @returns {Promise<Object>} Tracking response
   */
  async trackProductClick(productId) {
    return this.post('track', {
      event: 'product_click',
      product_id: productId
    });
  }
  
  /**
   * Track widget view
   * @param {string} widgetId - Widget ID
   * @returns {Promise<Object>} Tracking response
   */
  async trackWidgetView(widgetId) {
    return this.post('track', {
      event: 'widget_view',
      widget_id: widgetId
    });
  }
} 