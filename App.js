console.log('App.js is loading - check if this appears in logs');

import React, { useEffect } from 'react';
import { StatusBar, SafeAreaView, StyleSheet, Platform } from 'react-native';
import DemoScreen from './src/screens/DemoScreen';

export default function App() {
  // Platform-specific StatusBar styling
  useEffect(() => {
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor('#2c3e50');
    }
    StatusBar.setBarStyle('light-content');
    
    console.log('App component mounted');
  }, []);

  // Render the DemoScreen
  return (
    <SafeAreaView style={styles.container}>
      <DemoScreen />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
}); 