{"name": "widget", "version": "1.0.0", "description": "", "main": "widget.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "webpack --mode=none && npx babel src/react --out-dir dist --presets react-app/prod", "watch": "webpack --watch", "start": "node server.js", "babel": "npx babel --watch src/react --out-dir dist --presets react-app/prod", "start-dev": "webpack-dev-server --mode development", "go": "npm-run-all -p -r watch babel start"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "ISC", "devDependencies": {"@webpack-cli/serve": "^1.6.0", "babel-cli": "^6.26.0", "babel-core": "6.26.3", "babel-eslint": "10.0.1", "babel-loader": "7.1.5", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-polyfill": "6.26.0", "babel-preset-env": "^1.7.0", "css-loader": "^2.1.0", "dotenv-webpack": "^1.7.0", "es6-promise": "^4.2.8", "eslint": "^5.13.0", "file-loader": "^6.2.0", "less": "^3.9.0", "less-loader": "^4.1.0", "mini-css-extract-plugin": "^0.5.0", "uglifyjs-webpack-plugin": "^2.2.0", "webpack": "^4.29.3", "webpack-cli": "^4.4.0", "webpack-conditional-loader": "^1.0.12"}, "dependencies": {"@sentry/browser": "^5.9.1", "babel-preset-react-app": "^3.1.2", "circular-json": "^0.5.9", "dotenv": "^10.0.0", "npm-run-all": "^4.1.5", "serve-handler": "^6.1.3", "uuid": "^8.1.0", "webpack-dev-server": "^4.3.1"}}