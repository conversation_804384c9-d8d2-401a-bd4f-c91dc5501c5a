<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Product Collage</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .collage-container {
            position: relative;
            width: 1000px;
            height: 1000px;
            border: 1px solid #ccc;
            margin: 0 auto;
            background-color: #f9f9f9;
        }
        
        .product-image {
            position: absolute;
            background-size: contain;
            background-repeat: no-repeat;
        }
    </style>
</head>
<body>
    <h1>Simple Product Collage (1000x1000)</h1>
    <div class="collage-container" id="collageContainer"></div>

    <script>
        // Function to position products in the collage
        function createCollage() {
            const container = document.getElementById('collageContainer');
            
            // Product data from the onDataLoaded event
            const products = [
                {
                    "id": 4095344,
                    "name": "Куртка изо льна и хлопка",
                    "picture": "https://media.garderobo.ru/loverepublic/crop/6a604938137f8d51086a204b93076521.png?v=20250205",
                    "collage_data": {
                        "left": 45,
                        "top": 20,
                        "width": 45,
                        "height": 13.17654328601291,
                        "zIndex": 2,
                        "backgroundPositionX": "left",
                        "backgroundPositionY": "top"
                    }
                },
                {
                    "id": 4141159,
                    "name": "Асимметричный топ из хлопка",
                    "picture": "https://media.garderobo.ru/loverepublic/crop/090ba20af487684ef6ea377e38c1d4f7.png?v=20250205",
                    "collage_data": {
                        "left": 32.91170634920635,
                        "top": 3,
                        "width": 29.1765873015873,
                        "height": 26.37340017384082,
                        "zIndex": 2,
                        "backgroundPositionX": "center",
                        "backgroundPositionY": "top"
                    }
                },
                {
                    "id": 4060117,
                    "name": "Брюки из вискозы",
                    "picture": "https://media.garderobo.ru/loverepublic/crop/48dfd827b9db8751135dd88cf05c1adc.png?v=20250205",
                    "collage_data": {
                        "left": 17.259459459459457,
                        "top": 33,
                        "width": 30,
                        "height": 67,
                        "zIndex": 1,
                        "backgroundPositionX": "center",
                        "backgroundPositionY": "top"
                    }
                },
                {
                    "id": 4046312,
                    "name": "Сумка из натуральной замши",
                    "picture": "https://media.garderobo.ru/loverepublic/crop/23b0a0973e052dfb431d7ab2aec570e2.png?v=20250205",
                    "collage_data": {
                        "left": 75,
                        "top": 65,
                        "width": 25,
                        "height": 35,
                        "zIndex": 5,
                        "backgroundPositionX": "center",
                        "backgroundPositionY": "bottom"
                    }
                },
                {
                    "id": 4033296,
                    "name": "Мюли из натуральной кожи",
                    "picture": "https://media.garderobo.ru/loverepublic/crop/f9c74a3dd96e26fa88c6dfea26f3168c.png?v=20250205",
                    "collage_data": {
                        "left": 46,
                        "top": 70,
                        "width": 18,
                        "height": 30,
                        "zIndex": 5,
                        "backgroundPositionX": "left",
                        "backgroundPositionY": "bottom"
                    }
                },
                {
                    "id": 3983471,
                    "name": "Солнцезащитные очки",
                    "picture": "https://media.garderobo.ru/loverepublic/crop/bec561f42d84ab18dd2e6ae063204164.png?v=20250205",
                    "collage_data": {
                        "left": 5,
                        "top": 7,
                        "width": 20,
                        "height": 10,
                        "zIndex": 4,
                        "backgroundPositionX": "right",
                        "backgroundPositionY": "center"
                    }
                },
                {
                    "id": 3978055,
                    "name": "Ремень из натуральной кожи",
                    "picture": "https://media.garderobo.ru/loverepublic/crop/f0dc99943b39af99ce059132560d6ada.png?v=20250205",
                    "collage_data": {
                        "left": 36,
                        "top": 62,
                        "width": 27,
                        "height": 13,
                        "zIndex": 5,
                        "backgroundPositionX": "center",
                        "backgroundPositionY": "top"
                    }
                },
                {
                    "id": 4012964,
                    "name": "Серьги-кольца",
                    "picture": "https://media.garderobo.ru/loverepublic/crop/998230aa5acdd938bdf0bad89818336e.png?v=20250205",
                    "collage_data": {
                        "left": 70,
                        "top": 3,
                        "width": 20,
                        "height": 14,
                        "zIndex": 5,
                        "backgroundPositionX": "right",
                        "backgroundPositionY": "center"
                    }
                }
            ];
            
            // Create and position each product image
            products.forEach(product => {
                if (product.collage_data) {
                    const imgElement = document.createElement('div');
                    imgElement.className = 'product-image';
                    
                    // Set background image
                    imgElement.style.backgroundImage = `url(${product.picture})`;
                    
                    // Position and size based on collage_data (percentage of container)
                    imgElement.style.left = `${product.collage_data.left}%`;
                    imgElement.style.top = `${product.collage_data.top}%`;
                    imgElement.style.width = `${product.collage_data.width}%`;
                    imgElement.style.height = `${product.collage_data.height}%`;
                    imgElement.style.zIndex = product.collage_data.zIndex || 1;
                    
                    // Set background position
                    imgElement.style.backgroundPositionX = product.collage_data.backgroundPositionX || 'center';
                    imgElement.style.backgroundPositionY = product.collage_data.backgroundPositionY || 'center';
                    
                    // Add to container
                    container.appendChild(imgElement);
                }
            });
        }
        
        // Initialize when the page loads
        document.addEventListener('DOMContentLoaded', createCollage);
    </script>
</body>
</html>
