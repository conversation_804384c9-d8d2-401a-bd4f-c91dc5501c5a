<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>inline page</title>
    <link rel="stylesheet" href="/dist/widget.css" />
    <link rel="stylesheet" href="/custom/puma_2.css" />

    
    <style>
        body {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
    </style>
</head>
<body>

<section id="widgets"></section>

<script>

    let session = "afd690137556448cb039461fab05909b";
    localStorage.setItem("_garderoboSessionKey", session);

    let widgetScript = document.createElement('script');
    let apiKey = '5a53ab150b6446d28060e166cffbdc12';

    var cartProductIds = [];

    widgetScript.onload = function () {
      _garderoboWidget.then(function (widget) {
        widget.init(apiKey, { lang_code: 'en' });
        widget.initProduct(document.getElementById('widgets'), { product_id: "4067983207181"});
        widget.registerCallback.addToCartEvent(function(ymlId, productInfo) { 
          console.log('productInfo ', productInfo);
          console.log('ymlId ', ymlId);

          cartProductIds.push(ymlId);
          widget.setCartProducts(cartProductIds);
        });
        widget.registerCallback.addToFavorites(function(productId, state) { 
          console.log('productId ', productId, state);
        });
      });
    };

    widgetScript.src = '../../dist/widget.js';
    document.head.appendChild(widgetScript);

</script>
</body>
</html>