<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <title>Title</title>

  <style>
    iframe {

    }

    body {
      margin: 0;
    }
  </style>
</head>
<body>

<script>
  function adjustIframeStyles() {
    const iframe = document.querySelector('#visaver-iframe');
    if (iframe.offsetWidth > 1024) {
      iframe.style.aspectRatio = '1 / 0.7';
      iframe.style.maxHeight = '1000px';
      iframe.style.margin = '0 auto';
    } else {
      iframe.style.margin = '0 15px';
    }
  }
  window.addEventListener('DOMContentLoaded', () => {
    const iframe = document.querySelector('#visaver-iframe');
    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        if (entry.target === iframe) {
          adjustIframeStyles();
        }
      }
    });
    window.addEventListener('message', function (event) {
      const iframe = document.querySelector('#visaver-iframe');
      if (iframe && event.source === iframe.contentWindow) {
        if (event.data && event.data.frameHeight) {
          iframe.style.height = event.data.frameHeight + 'px';
        }
      }
    });
    resizeObserver.observe(iframe);
    adjustIframeStyles();
  });
</script>
<iframe id="visaver-iframe" style="border: none; width: calc(100vw - 30px); overflow-y: hidden;" allowfullscreen height="auto" src='https://visaver.com/embed/videos/9f61c01d-171a-437d-b2b7-80d7b11fb82c?t=0&vendor=external&theme=light&lang=ru&show_test=0'></iframe>

<p>ывжалдождыова джфлыо важдлфыо важдлофы джлао фыждвао ждфылова ждлфыо </p>
<p>ывжалдождыова джфлыо важдлфыо важдлофы джлао фыждвао ждфылова ждлфыо </p>
<p>ывжалдождыова джфлыо важдлфыо важдлофы джлао фыждвао ждфылова ждлфыо </p>
<p>ывжалдождыова джфлыо важдлфыо важдлофы джлао фыждвао ждфылова ждлфыо </p>
<p>ывжалдождыова джфлыо важдлфыо важдлофы джлао фыждвао ждфылова ждлфыо </p>
<p>ывжалдождыова джфлыо важдлфыо важдлофы джлао фыждвао ждфылова ждлфыо </p>
<p>ывжалдождыова джфлыо важдлфыо важдлофы джлао фыждвао ждфылова ждлфыо </p>
<p>ывжалдождыова джфлыо важдлфыо важдлофы джлао фыждвао ждфылова ждлфыо </p>
<p>ывжалдождыова джфлыо важдлфыо важдлофы джлао фыждвао ждфылова ждлфыо </p>

</body>
</html>