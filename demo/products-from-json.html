<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Горизонтальное размещение продуктов из JSON</title>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            width: 1000px;
            height: 1000px;
            border: 1px solid #ccc;
            overflow-x: auto;
            white-space: nowrap;
            padding: 10px;
        }
        
        .product {
            display: inline-block;
            margin-right: 15px;
            vertical-align: top;
            text-align: center;
        }
        
        .product img {
            /* Оригинальный размер изображения без изменений */
            border: 1px solid #eee;
        }
        
        .product-info {
            margin-top: 5px;
            font-size: 12px;
            white-space: normal;
            max-width: 200px;
        }
        
        .product-name {
            font-weight: bold;
        }
        
        .product-price {
            color: #e44;
        }
    </style>
</head>
<body>
    <h1>Горизонтальное размещение продуктов из JSON</h1>
    <div class="container" id="productsContainer"></div>

    <script>
        // Функция для отображения продуктов из JSON данных
        function displayProductsFromJson() {
            const container = document.getElementById('productsContainer');
            
            // Данные из вашего JSON
            const data = {
                "products": [
                    {
                        "id": 4095344,
                        "price": 9999,
                        "brand": "LOVE REPUBLIC",
                        "name": "Куртка изо льна и хлопка",
                        "picture": "https://media.garderobo.ru/loverepublic/crop/6a604938137f8d51086a204b93076521.png?v=20250205"
                    },
                    {
                        "id": 4141159,
                        "price": 2599,
                        "brand": "LOVE REPUBLIC",
                        "name": "Асимметричный топ из хлопка",
                        "picture": "https://media.garderobo.ru/loverepublic/crop/090ba20af487684ef6ea377e38c1d4f7.png?v=20250205"
                    },
                    {
                        "id": 4060117,
                        "price": 4599,
                        "brand": "LOVE REPUBLIC",
                        "name": "Брюки из вискозы",
                        "picture": "https://media.garderobo.ru/loverepublic/crop/48dfd827b9db8751135dd88cf05c1adc.png?v=20250205"
                    },
                    {
                        "id": 4046312,
                        "price": 16999,
                        "brand": "LOVE REPUBLIC",
                        "name": "Сумка из натуральной замши",
                        "picture": "https://media.garderobo.ru/loverepublic/crop/23b0a0973e052dfb431d7ab2aec570e2.png?v=20250205"
                    },
                    {
                        "id": 4033296,
                        "price": 10999,
                        "brand": "LOVE REPUBLIC",
                        "name": "Мюли из натуральной кожи",
                        "picture": "https://media.garderobo.ru/loverepublic/crop/f9c74a3dd96e26fa88c6dfea26f3168c.png?v=20250205"
                    },
                    {
                        "id": 3983471,
                        "price": 2599,
                        "brand": "LOVE REPUBLIC",
                        "name": "Солнцезащитные очки",
                        "picture": "https://media.garderobo.ru/loverepublic/crop/bec561f42d84ab18dd2e6ae063204164.png?v=20250205"
                    },
                    {
                        "id": 4060385,
                        "price": 5999,
                        "brand": "LOVE REPUBLIC",
                        "name": "Платок из 100% шелка",
                        "picture": "https://media.garderobo.ru/loverepublic/crop/a96f657d342382f0da181cf65faf2be9.png?v=20250205"
                    },
                    {
                        "id": 3978055,
                        "price": 3999,
                        "brand": "LOVE REPUBLIC",
                        "name": "Ремень из натуральной кожи",
                        "picture": "https://media.garderobo.ru/loverepublic/crop/f0dc99943b39af99ce059132560d6ada.png?v=20250205"
                    },
                    {
                        "id": 4012964,
                        "price": 1999,
                        "brand": "LOVE REPUBLIC",
                        "name": "Серьги-кольца",
                        "picture": "https://media.garderobo.ru/loverepublic/crop/998230aa5acdd938bdf0bad89818336e.png?v=20250205"
                    }
                ]
            };
            
            // Отображаем каждый продукт
            data.products.forEach(product => {
                const productElement = document.createElement('div');
                productElement.className = 'product';
                
                // Создаем изображение
                const img = document.createElement('img');
                img.src = product.picture;
                img.alt = product.name;
                
                // Создаем информацию о продукте
                const infoElement = document.createElement('div');
                infoElement.className = 'product-info';
                
                const nameElement = document.createElement('div');
                nameElement.className = 'product-name';
                nameElement.textContent = product.name;
                
                const priceElement = document.createElement('div');
                priceElement.className = 'product-price';
                priceElement.textContent = `${product.price} ₽`;
                
                // Добавляем элементы в контейнер
                infoElement.appendChild(nameElement);
                infoElement.appendChild(priceElement);
                
                productElement.appendChild(img);
                productElement.appendChild(infoElement);
                
                container.appendChild(productElement);
            });
        }
        
        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', displayProductsFromJson);
    </script>
</body>
</html>
