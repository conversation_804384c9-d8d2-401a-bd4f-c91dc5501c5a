<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Search</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        #results {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .image-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        img {
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 10px;
        }
    </style>
</head>
<body>
<h1>Image Search</h1>
<div>
    <input type="text" id="query" placeholder="Enter search query">
    <button onclick="searchImages()">Search</button>
</div>
<div id="results"></div>

<script>
    async function searchImages() {
        const query = document.getElementById('query').value;
        const resultsContainer = document.getElementById('results');
        resultsContainer.innerHTML = ''; // Clear previous results

        try {
            // Call the API
            const response = await fetch(`http://0.0.0.0:8002/api/v1/multimodal-search-v2?query=${encodeURIComponent(query)}`);
            const data = await response.json();

            if (data.status === 'success' && data.matched_images.length > 0) {
                const basePath = 'http://0.0.0.0:8002/images';

                // Add images to the results container
                data.matched_images.forEach(imagePath => {
                    const localImagePath = imagePath.replace('./frames', basePath);
                    console.log(localImagePath);

                    const imageContainer = document.createElement('div');
                    imageContainer.className = 'image-container';

                    const img = document.createElement('img');
                    img.src = localImagePath;
                    img.alt = 'Image';

                    const caption = document.createElement('div');
                    caption.textContent = localImagePath;

                    imageContainer.appendChild(img);
                    //imageContainer.appendChild(caption);

                    resultsContainer.appendChild(imageContainer);
                });
            } else {
                resultsContainer.innerHTML = '<p>No images found.</p>';
            }
        } catch (error) {
            console.error('Error fetching images:', error);
            resultsContainer.innerHTML = '<p>Error fetching images. Please try again later.</p>';
        }
    }
</script>
</body>
</html>
