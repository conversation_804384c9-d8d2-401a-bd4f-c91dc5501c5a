
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="/dist/widget.css" />
    <!--<link rel="stylesheet" href="/dist/platform.css" />-->
    <link rel="stylesheet" href="/custom/amazingred2.css" />

    <style>
        #garderobo-platform {
            max-width: 1450px;
            margin: auto;
        }

        * {
            box-sizing: border-box;
        }
    </style>
</head>
<body>
<div id="garderobo-platform">

</div>

<div id="garderobo-popular">

</div>

<script>

    // let session = '2e6661e0b896469da6f857f784e980bf';
    // localStorage.setItem('_garderoboSessionKey', session);

    try {
        var widgetScript = document.createElement('script');
        var cartProductIds = [];

        widgetScript.onload = function () {
            _garderoboWidget.then(function(widget) {
                //   widget.init('0a73cabc9fa24483a6c4a90ce620bf5a', {lang_code: 'ru'});
                //   widget.initPlatform(document.querySelector('#garderobo'), {tag_name: 'Main Page', is_slider: true});

                widget.init('2045b3b345264e1eb4e97fd7234e3ef6', {lang_code: 'ru'});
                widget.initMain(document.querySelector('#garderobo-popular'));
                widget.initPlatform(document.querySelector('#garderobo-platform'), {tag_name: ['Мужчины','Женщины'], platform_slider: true});

                widget.registerCallback.addToCartEvent(function(ymlId, productInfo) {
                    console.log('addToCartEvent productInfo ', productInfo);
                    console.log('addToCartEvent ymlId ', ymlId);
                    // widget.setCartProducts(ymlId);
                    cartProductIds.push(ymlId);
                    widget.setCartProducts(cartProductIds);

                    console.log('cartProductIds ', cartProductIds);

                });
                widget.registerCallback.addToFavorites(function(productId, state) {
                    console.log('addToFavorites productId ', productId, state);
                });
            });
        };

        widgetScript.src = '../../dist/widget.js';
        document.head.appendChild(widgetScript);
    } catch (e) {
        console.log('garderobo: ', e)
    }
</script>
</body>
</html>