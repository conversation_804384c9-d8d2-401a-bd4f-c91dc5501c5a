<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YULIAWAVE</title>
    <link rel="stylesheet" href="/dist/widget.css" />
    <link rel="stylesheet" href="/custom/vipavenue.css" />

    <style>
        #garderobo {
            max-width: 1450px;
            margin: auto;
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        .garderobo-grid-look__platform-product {
            border : 1px dotted #f00;
        }
    </style>
</head>
<body>

<section class="collage" id="garderobo"></section>

<script>
    try {
        var widgetScript = document.createElement('script');
        var cartProductIds = [];

        widgetScript.onload = function () {
            _garderoboWidget.then(function(widget) {
                widget.init('0a73cabc9fa24483a6c4a90ce620bf5a', {lang_code: 'ru', is_platform: true});
                // widget.initProduct(document.getElementById('garderobo'), {product_id: '2080890'});
                widget.initPlatform(document.getElementById('garderobo'), {tag_name: 'Женщины', is_slider: 1});

                widget.registerCallback.addToCartEvent(function(ymlId, productInfo) {
                    console.log('addToCartEvent productInfo ', productInfo);
                    console.log('addToCartEvent ymlId ', ymlId);
                    // widget.setCartProducts(ymlId);
                    cartProductIds.push(ymlId);
                    widget.setCartProducts(cartProductIds, '/cart');
                });
                widget.registerCallback.addToFavorites(function(productId, state) {
                    console.log('addToFavorites productId ', productId, state);
                });
                widget.registerCallback.subscribeToProduct(function (productId) {
                    console.log('subscribeToProduct ', productId);
                });
            });
        };

        widgetScript.src = '../../dist/widget.js';
        document.head.appendChild(widgetScript);
    } catch (e) {
        console.log('garderobo: ', e)
    }
</script>
</body>
</html>