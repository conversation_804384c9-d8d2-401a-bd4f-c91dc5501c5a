<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="/dist/widget.css" />
    <link rel="stylesheet" href="/custom/loverepublic.css" />

    <style>
        #garderobo {
            max-width: 1450px;
            margin: auto;
        }
    </style>
</head>
<body>
    <section id="garderobo">
     
    </section>


    <div class="modatech-look-widgets modatech-internal-widget">
        <div class="modatech-look-widget-container">
            <img style="width: 100%;" src="https://platform-static.modatech.ru/loverepublic/medium/c9d3f2d6dc874286a04fb783cfa0feca.webp" data-id="1" data-external-id="131154" height="100%" />
            <button class="gw-view-look-btn"></button>
        </div>
        <div class="modatech-look-widget-container">
            <img style="width: 100%;" src="https://platform-static.modatech.ru/loverepublic/medium/c9d3f2d6dc874286a04fb783cfa0feca.webp" data-id="1" data-external-id="131154" height="100%" />
            <button class="gw-view-look-btn"></button>
        </div>
        <div class="modatech-look-widget-container">
            <img style="width: 100%;" src="https://platform-static.modatech.ru/loverepublic/medium/c9d3f2d6dc874286a04fb783cfa0feca.webp" data-id="1" data-external-id="131154" height="100%" />
            <button class="gw-view-look-btn"></button>
        </div>
    </div>    

    <script>
        try {
          var widgetScript = document.createElement('script');
          var cartProductIds = [];
          
          widgetScript.onload = function () {    
            _garderoboWidget.then(function(widget) {
            //   widget.init('0a73cabc9fa24483a6c4a90ce620bf5a', {lang_code: 'ru'});
            //   widget.initPlatform(document.querySelector('#garderobo'), {tag_name: 'Main Page', is_slider: true});

                widget.init('47d2ff3e24684e58abfcbd3dda60c3e8', {lang_code: 'ru', is_platform: true});
                widget.initInline(document.querySelector('#garderobo'));
                widget.registerCallback.addToCartEvent(function(ymlId, productInfo) { 
                    console.log('addToCartEvent productInfo ', productInfo);
                    console.log('addToCartEvent ymlId ', ymlId);
                    // widget.setCartProducts(ymlId);
                    cartProductIds.push(ymlId);
                    widget.setCartProducts(cartProductIds);

                    console.log('cartProductIds ', cartProductIds);
                    
                });
                widget.registerCallback.addToFavorites(function(productId, state) { 
                    console.log('addToFavorites productId ', productId, state);
                });
            });
          };
       
          widgetScript.src = '../../dist/widget.js';
          document.head.appendChild(widgetScript);
        } catch (e) {
          console.log('garderobo: ', e)
        }
      </script>
</body>
</html>