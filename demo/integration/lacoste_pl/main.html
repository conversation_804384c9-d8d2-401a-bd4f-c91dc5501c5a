<script>
    (function() {

        function getCookie(name) {
            var cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                var cookies = document.cookie.split(';');
                for (var i = 0; i < cookies.length; i++) {
                    var cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        function getUpdatedLacosteCartItemsForEmarsys(serverResponseData) {
            var emarsysCartItems = [];

            if (window.dataLayer) {
                var lastEcommerceState = null;
                for (var i = window.dataLayer.length - 1; i >= 0; i--) {
                    var dlObject = window.dataLayer[i];
                    if (dlObject && dlObject.ecommerce) {
                        if (dlObject.ecommerce.items) {
                            lastEcommerceState = dlObject.ecommerce.items;
                            break;
                        } else if (dlObject.ecommerce.checkout && dlObject.ecommerce.checkout.products) {
                            lastEcommerceState = dlObject.ecommerce.checkout.products;
                            break;
                        }
                    }
                }

                if (lastEcommerceState && lastEcommerceState.length > 0) {
                    lastEcommerceState.forEach(function(item) {
                        var itemId = item.item_id || item.id || item.sku;
                        var itemPrice = item.price;
                        var itemQuantity = item.quantity;

                        if (itemId !== undefined && itemPrice !== undefined && itemQuantity !== undefined) {
                            emarsysCartItems.push({
                                item: String(itemId),
                                price: parseFloat(itemPrice),
                                quantity: parseInt(itemQuantity, 10)
                            });
                        }
                    });
                }
            }

            if (emarsysCartItems.length > 0) {
                return emarsysCartItems;
            } else {
                return [];
            }
        }

        function AddToBasketViaGTM(productId) {
            var csrfToken = getCookie('csrftoken');
            if (!csrfToken) {
                console.error('GTM: CSRF token not found.');
                return;
            }

            var url = 'https://www.lacoste.pl/baskets/basket/';
            var requestBody = {
                product: parseInt(productId, 10),
                quantity: 1
            };

            var headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Referer': window.location.href
            };

            fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(requestBody),
                credentials: 'include'
            })
                .then(function(response) {
                    if (!response.ok) {
                        return response.text().then(function(text) {
                            throw new Error('Server error: ' + response.status + ' ' + response.statusText + '. Body: ' + text);
                        });
                    }
                    var contentType = response.headers.get("content-type");
                    if (contentType && contentType.indexOf("application/json") !== -1) {
                        return response.json();
                    } else {
                        return response.text();
                    }
                })
                .then(function(data) {
                    if (typeof ScarabQueue !== 'undefined') {
                        var currentCartItemsForEmarsys = getUpdatedLacosteCartItemsForEmarsys(data);
                        if (currentCartItemsForEmarsys) {
                            ScarabQueue.push(['cart', currentCartItemsForEmarsys]);
                        }
                    }

                   //update UI

                    console.log('GTM: Product addition process complete. Implement cart UI update if needed.');
                    console.log('Product added to basket successfully!', data);

                    // Обновление данных корзины для Emarsys
                    var updatedCartItems = getUpdatedLacosteCartItemsForEmarsys(data);
                    if (updatedCartItems.length > 0) {
                        console.log('Готовые данные корзины для Emarsys:', updatedCartItems);
                    }
                })
                .catch(function(error) {
                    console.error('GTM: Error adding product to basket:', error);
                    console.error('Error adding product to basket:', error);
                });
        }

        window.AddToBasketViaGTM = AddToBasketViaGTM;

        function getLanguageCode() {
            try {
                var storageLanguage = localStorage.getItem('language');
                if (storageLanguage)
                    return storageLanguage;
            } catch (e) {
                console.error('Error accessing localStorage:', e);
            }

            return 'pl';
        }

        var garderoboWidgetScript = document.createElement('script');
        garderoboWidgetScript.onload = function () {
            _garderoboWidget.then(function(widget) {
                widget.init('b22f337d87b24090a050fe6f0bc67945', {lang_code: getLanguageCode()});
                loadWidgetData();
            });
        };

        garderoboWidgetScript.src = 'https://widget.garderobo.ai/widget.js';
        document.head.appendChild(garderoboWidgetScript);
    })();
</script>

