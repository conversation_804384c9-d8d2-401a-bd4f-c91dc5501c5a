<script>
    function getSkuId() {
        console.log('Get SKU');

        var addToBasketButton = document.querySelector('.js-add-basket');
        if (addToBasketButton && addToBasketButton.getAttribute('data-ga')) {
            var dataGa = addToBasketButton.getAttribute('data-ga');
            var productIdMatch = dataGa.match(/product_id: (\d+)/);
            if (productIdMatch && productIdMatch[1]) {
                console.log('Found product_id from data-ga:', productIdMatch[1]);
                return productIdMatch[1];
            }
        }

        var dlValue = {{DLV - Item Sku}};
        console.log('Using dataLayer SKU value:', dlValue);
        return dlValue;
    }

    function loadWidgetData() {
        _garderoboWidget.then(function(widget) {
            widget.initProduct(document.getElementById('pdp-upper-footer'), {"product_id": getSkuId(), scroll_button: {position: document.querySelector('.product-detail-swiper'), caption: 'Wear With', offset: -150}});
        });
    }
</script>
