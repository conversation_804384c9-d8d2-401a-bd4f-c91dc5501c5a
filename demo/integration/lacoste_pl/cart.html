<script>
    function getSkuIds() {
        var skuIds = [];

        try {
            if (window.cartcontent && Array.isArray(window.cartcontent)) {
                window.cartcontent.forEach(function(item) {
                    if (item && item.item_sku)
                        skuIds.push(item.item_sku);
                });
            } else {
                console.warn('Cart content not found or not an array');
            }
        } catch (e) {
            console.error('Error processing cart content:', e);
        }
        return skuIds;
    }

    function loadWidgetData() {
        _garderoboWidget.then(function(widget) {
            widget.initCart(document.getElementById('basket-upper-footer'), {"yml_ids": getSkuIds()});
        });
    }
</script>
