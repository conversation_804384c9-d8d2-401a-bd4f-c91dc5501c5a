<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Простое горизонтальное размещение продуктов</title>
    <style>
        body {
            margin: 20px;
        }

        .container {
            width: 1000px;
            height: 1000px;
            border: 1px solid #ccc;
            overflow-x: auto;
            white-space: nowrap;
        }

        img {
            display: inline-block;
            border: 1px dotted #f00;
            /* Никаких изменений размера - оригинальный размер 1:1 */
        }

        div {
            border: 1px dotted #00f;
        }
    </style>
</head>
<body>
    <h1>Простое горизонтальное размещение продуктов</h1>
    <div class="container" id="productsContainer"></div>

    <script>
        // Данные продуктов из первого образа
        const products =
            [
                {
                    "id": 4060221,
                    "yml_id": "282172",
                    "group_id": "282154",
                    "yml_ids": [
                        "282172",
                        "282170",
                        "282167",
                        "282163",
                        "282160",
                        "282157"
                    ],
                    "price": 7999,
                    "old_price": null,
                    "brand": "LOVE REPUBLIC",
                    "name": "Джинсовая куртка",
                    "wareId": 1764787,
                    "picture": "https://media.garderobo.ru/loverepublic/crop/a0f2ac00cbcb3f96a71aebebaa80b19f.png?v=20250205",
                    "link": "https://loverepublic.ru/catalog/odezhda/verhnyaya-odezhda/kurtki/282154/",
                    "score": 100,
                    "is_main_product": 1,
                    "pictures": [
                        "https://media.garderobo.ru/loverepublic/medium/a0f2ac00cbcb3f96a71aebebaa80b19f.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/6bcb7d2f5e1a1ec2964a7f09f29494dc.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/b3aa5393cf733cd88a7f570e20049a76.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/f49cf5ad59d49ffd8ca480458f69704c.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/9e8d8c90b857859078e99a87a015c151.jpg"
                    ],
                    "sizes": [
                        {
                            "name": "40",
                            "is_available": true,
                            "yml_id": "282157"
                        },
                        {
                            "name": "42",
                            "is_available": true,
                            "yml_id": "282160"
                        },
                        {
                            "name": "44",
                            "is_available": true,
                            "yml_id": "282163"
                        },
                        {
                            "name": "46",
                            "is_available": true,
                            "yml_id": "282167"
                        },
                        {
                            "name": "48",
                            "is_available": true,
                            "yml_id": "282170"
                        },
                        {
                            "name": "50",
                            "is_available": true,
                            "yml_id": "282172"
                        }
                    ],
                    "category_group": "layer-3_jacket",
                    "params": {
                        "dimensions": {
                            "girth_bottom": 104,
                            "length_back": 54.4,
                            "girth_chest": 120,
                            "sleeve_length": 58,
                            "sleeve_girth": 55,
                            "length": 55.5,
                            "neck_girth": 46.2
                        }
                    },
                    "offers_props_by_yml_id": {
                        "282157": {
                            "barcode": "4680704684636"
                        },
                        "282160": {
                            "barcode": "4680704684643"
                        },
                        "282163": {
                            "barcode": "4680704684650"
                        },
                        "282167": {
                            "barcode": "4680704684667"
                        },
                        "282170": {
                            "barcode": "4680704684674"
                        },
                        "282172": {
                            "barcode": "4680704684681"
                        }
                    },
                    "crop_wh": [
                        278,
                        321
                    ],
                    "waist_length": 152,
                    "debug": [],
                    "collage_type": "top_1",
                    "main_category_type": "layer-3_1",
                    "collage_data": {
                        "left": 45,
                        "top": 20,
                        "width": 45,
                        "template_width": 21,
                        "template_left": 57,
                        "height": 48.3,
                        "zIndex": 3,
                        "backgroundPositionX": "left",
                        "backgroundPositionY": "top"
                    }
                },
                {
                    "id": 4054872,
                    "yml_id": "282615",
                    "group_id": "282610",
                    "yml_ids": [
                        "282615",
                        "282614",
                        "282613",
                        "282612",
                        "282611"
                    ],
                    "price": 6999,
                    "old_price": null,
                    "brand": "LOVE REPUBLIC",
                    "name": "Платье",
                    "wareId": 1763314,
                    "picture": "https://media.garderobo.ru/loverepublic/crop/ece07f4741bd0a5f63461f31a14fc1d2.png?v=20250205",
                    "link": "https://loverepublic.ru/catalog/odezhda/platya/282610/",
                    "score": 100,
                    "pictures": [
                        "https://media.garderobo.ru/loverepublic/medium/ece07f4741bd0a5f63461f31a14fc1d2.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/754eb58c653d67b51c0d709d47e068e2.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/9176d1e60c2f0e29b152b55af98230bb.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/218c8951e5af3eea325677b41888455d.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/5bc005520f7fca661ba3ecd5df904889.jpg"
                    ],
                    "sizes": [
                        {
                            "name": "40",
                            "is_available": true,
                            "yml_id": "282611"
                        },
                        {
                            "name": "42",
                            "is_available": true,
                            "yml_id": "282612"
                        },
                        {
                            "name": "44",
                            "is_available": true,
                            "yml_id": "282613"
                        },
                        {
                            "name": "46",
                            "is_available": true,
                            "yml_id": "282614"
                        },
                        {
                            "name": "48",
                            "is_available": true,
                            "yml_id": "282615"
                        }
                    ],
                    "category_group": "layer-full_dress",
                    "params": {
                        "dimensions": {
                            "girth_bottom": 458,
                            "length_back": 118,
                            "girth_chest": 82,
                            "sleeve_length": 45,
                            "sleeve_girth": 85,
                            "length": 136.5,
                            "length_to_waist": 29.5,
                            "neck_girth": 56.4
                        }
                    },
                    "offers_props_by_yml_id": {
                        "282611": {
                            "barcode": "4680704705027"
                        },
                        "282612": {
                            "barcode": "4680704705034"
                        },
                        "282613": {
                            "barcode": "4680704705041"
                        },
                        "282614": {
                            "barcode": "4680704705058"
                        },
                        "282615": {
                            "barcode": "4680704705065"
                        }
                    },
                    "crop_wh": [
                        268,
                        370
                    ],
                    "waist_length": 191,
                    "debug": [],
                    "collage_type": "layer-full_1",
                    "main_category_type": "layer-full_1",
                    "collage_data": {
                        "left": 10.04324324324324,
                        "top": 5,
                        "width": 65.91351351351352,
                        "template_width": 26,
                        "template_left": 30,
                        "height": 91,
                        "zIndex": 2,
                        "backgroundPositionX": "center",
                        "backgroundPositionY": "top"
                    }
                },
                {
                    "id": 3932022,
                    "yml_id": "274958",
                    "group_id": "274957",
                    "yml_ids": [
                        "274958"
                    ],
                    "price": 2599,
                    "old_price": 4999,
                    "brand": "LOVE REPUBLIC",
                    "name": "Сумка-торба из экокожи",
                    "wareId": 1716365,
                    "picture": "https://media.garderobo.ru/loverepublic/crop/1db35d6ec8e48c0fe46583b1dd84332c.png?v=20250205",
                    "link": "https://loverepublic.ru/catalog/sumki/274957/",
                    "score": 100,
                    "pictures": [
                        "https://media.garderobo.ru/loverepublic/medium/1db35d6ec8e48c0fe46583b1dd84332c.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/731f247838b3d639025a10da70bc50c7.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/5c3e12e9eca9dfc94ddbc2824595855b.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/9b05f3bfc7a9063fb89ba13375edba89.jpg"
                    ],
                    "sizes": [
                        {
                            "name": "23*8*19 см",
                            "is_available": true,
                            "yml_id": "274958"
                        }
                    ],
                    "category_group": "bag",
                    "offers_props_by_yml_id": {
                        "274958": {
                            "barcode": "4680704239065"
                        }
                    },
                    "crop_wh": [
                        252,
                        320
                    ],
                    "debug": [],
                    "collage_type": "bag_1",
                    "main_category_type": "bag_1",
                    "collage_data": {
                        "left": 15,
                        "top": 65,
                        "width": 25,
                        "template_width": 25,
                        "template_left": 15,
                        "height": 35,
                        "zIndex": 5,
                        "backgroundPositionX": "center",
                        "backgroundPositionY": "bottom"
                    }
                },
                {
                    "id": 4017503,
                    "yml_id": "283404",
                    "group_id": "283389",
                    "yml_ids": [
                        "283404",
                        "283402",
                        "283399",
                        "283394",
                        "283392"
                    ],
                    "price": 9999,
                    "old_price": null,
                    "brand": "LOVE REPUBLIC",
                    "name": "Босоножки из натуральной замши",
                    "wareId": 1752369,
                    "picture": "https://media.garderobo.ru/loverepublic/crop/9b27ec131fcb4383f296649cc1b0021e.png?v=20250205",
                    "link": "https://loverepublic.ru/catalog/shoes/283389/",
                    "score": 100,
                    "pictures": [
                        "https://media.garderobo.ru/loverepublic/medium/7b87b4d0a8bc1bfef86dfa3df8cbdd36.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/d801a6090040389577d75e0d6bb416a5.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/948f946c7763fc4eae713b6ea1b93752.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/9b27ec131fcb4383f296649cc1b0021e.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/c19fdf3308cf4e11a55dae5dc559aa6d.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/cd96a4d65bf8d915baf9bb275d6dc8d3.jpg"
                    ],
                    "sizes": [
                        {
                            "name": "36",
                            "is_available": true,
                            "yml_id": "283392"
                        },
                        {
                            "name": "37",
                            "is_available": true,
                            "yml_id": "283394"
                        },
                        {
                            "name": "38",
                            "is_available": true,
                            "yml_id": "283399"
                        },
                        {
                            "name": "39",
                            "is_available": true,
                            "yml_id": "283402"
                        },
                        {
                            "name": "40",
                            "is_available": true,
                            "yml_id": "283404"
                        }
                    ],
                    "category_group": "shoes_sandals",
                    "offers_props_by_yml_id": {
                        "283392": {
                            "barcode": "4680704754681"
                        },
                        "283394": {
                            "barcode": "4680704754698"
                        },
                        "283399": {
                            "barcode": "4680704754704"
                        },
                        "283402": {
                            "barcode": "4680704754711"
                        },
                        "283404": {
                            "barcode": "4680704754728"
                        }
                    },
                    "crop_wh": [
                        266,
                        186
                    ],
                    "debug": [],
                    "collage_type": "shoes_sandals_1",
                    "main_category_type": "shoes_1",
                    "collage_data": {
                        "left": 60,
                        "top": 72,
                        "width": 18,
                        "template_width": 18,
                        "template_left": 60,
                        "height": 28,
                        "zIndex": 5,
                        "backgroundPositionX": "left",
                        "backgroundPositionY": "bottom"
                    }
                },
                {
                    "id": 3983441,
                    "yml_id": "276036",
                    "group_id": "276035",
                    "yml_ids": [
                        "276036"
                    ],
                    "price": 2599,
                    "old_price": null,
                    "brand": "LOVE REPUBLIC",
                    "name": "Очки солнцезащитные с футляром",
                    "wareId": 1743216,
                    "picture": "https://media.garderobo.ru/loverepublic/crop/3423980c6e663154e7823321c3e77f15.png?v=20250205",
                    "link": "https://loverepublic.ru/catalog/aksessuary/ochki/276035/",
                    "score": 100,
                    "pictures": [
                        "https://media.garderobo.ru/loverepublic/medium/3423980c6e663154e7823321c3e77f15.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/aebd582736d2147e59a42daa9e32095a.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/81a0b3e3153f5b9e7215d566b5ffce4b.jpg"
                    ],
                    "sizes": [
                        {
                            "name": "ОДИН РАЗМЕР",
                            "is_available": true,
                            "yml_id": "276036"
                        }
                    ],
                    "category_group": "accessory_glasses",
                    "offers_props_by_yml_id": {
                        "276036": {
                            "barcode": "4680704312119"
                        }
                    },
                    "crop_wh": [
                        224,
                        86
                    ],
                    "debug": [],
                    "collage_type": "accessory_glasses_1",
                    "main_category_type": "accessory_1",
                    "collage_data": {
                        "left": 7,
                        "top": 2,
                        "width": 20,
                        "template_width": 20,
                        "template_left": 7,
                        "height": 22,
                        "zIndex": 4,
                        "backgroundPositionX": "right",
                        "backgroundPositionY": "center"
                    }
                },
                {
                    "id": 3964521,
                    "yml_id": "275688",
                    "group_id": "275686",
                    "yml_ids": [
                        "275688"
                    ],
                    "price": 3599,
                    "old_price": null,
                    "brand": "LOVE REPUBLIC",
                    "name": "Набор браслетов",
                    "wareId": 1736148,
                    "picture": "https://media.garderobo.ru/loverepublic/crop/7491b979de207c7002249e826576db80.png?v=20250205",
                    "link": "https://loverepublic.ru/catalog/ukrasheniya/braslety/275686/",
                    "score": 100,
                    "pictures": [
                        "https://media.garderobo.ru/loverepublic/medium/7491b979de207c7002249e826576db80.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/11a4b442d77d38ad422cf1083b94ed1e.jpg",
                        "https://media.garderobo.ru/loverepublic/medium/46726f29d0b2fdee141ec3decc910e03.jpg"
                    ],
                    "sizes": [
                        {
                            "name": "ОДИН РАЗМЕР",
                            "is_available": true,
                            "yml_id": "275688"
                        }
                    ],
                    "category_group": "accessory_bracelet",
                    "offers_props_by_yml_id": {
                        "275688": {
                            "barcode": "4680704284379"
                        }
                    },
                    "crop_wh": [
                        214,
                        166
                    ],
                    "waist_length": 177,
                    "debug": [],
                    "collage_type": "accessory_bracelet_1",
                    "main_category_type": "accessory_2",
                    "collage_data": {
                        "left": 8.954802259887007,
                        "top": 45,
                        "width": 12.090395480225988,
                        "template_width": 10,
                        "template_left": 10,
                        "height": 15,
                        "zIndex": 5,
                        "backgroundPositionX": "center",
                        "backgroundPositionY": "center"
                    }
                },
                {
                    "id": 4012998,
                    "yml_id": "280483",
                    "group_id": "280482",
                    "yml_ids": [
                        "280483"
                    ],
                    "price": 1999,
                    "old_price": null,
                    "brand": "LOVE REPUBLIC",
                    "name": "Витые серьги",
                    "wareId": 1750959,
                    "picture": "https://media.garderobo.ru/loverepublic/crop/3f1918dc072659b343b574fc07982d90.png?v=20250205",
                    "link": "https://loverepublic.ru/catalog/ukrasheniya/sergi/280482/",
                    "score": 99.98,
                    "pictures": [
                        "https://media.garderobo.ru/loverepublic/medium/3f1918dc072659b343b574fc07982d90.jpg"
                    ],
                    "sizes": [
                        {
                            "name": "ОДИН РАЗМЕР",
                            "is_available": true,
                            "yml_id": "280483"
                        }
                    ],
                    "category_group": "accessory_earring",
                    "offers_props_by_yml_id": {
                        "280483": {
                            "barcode": "4680704608533"
                        }
                    },
                    "crop_wh": [
                        226,
                        93
                    ],
                    "debug": [],
                    "collage_type": "accessory_earring_1",
                    "main_category_type": "accessory_3",
                    "collage_data": {
                        "left": 10,
                        "top": 20,
                        "width": 10,
                        "template_width": 10,
                        "template_left": 10,
                        "height": 22,
                        "zIndex": 5,
                        "backgroundPositionX": "right",
                        "backgroundPositionY": "center"
                    }
                }
            ]

        // Функция для отображения изображений продуктов
        function displayImages() {
            const container = document.getElementById('productsContainer');

            scale_factors = [];
            products.forEach(product => {
                const div = document.createElement('div');
                div.style.width = product.crop_wh[0] + 'px';
                div.style.height = product.crop_wh[1] + 'px';

                const img = document.createElement('img');
                img.src = product.picture;
                img.alt = product.name;
                img.title = `${product.waist_length}`;

                if (product.params && product.params.dimensions) {
                    img.title = JSON.stringify(product.params.dimensions);

                    if (product.category_group == 'layer-1_tshirt') {
                        product.waist_length = product.params.dimensions.girth_chest * 0.79 * 1.88;
                    }

                    let scaleFactor = 0;
                    if (product.params.dimensions.waist_girth)
                        scaleFactor = product.waist_length / (product.params.dimensions.waist_girth / 2);
                    //if (product.params.dimensions.girth_chest)
                    //    scaleFactor = product.waist_length / (product.params.dimensions.girth_chest / 2);

                    scale_factors.push(scaleFactor);
                    //scaleFactor = 3.81;
                    //0.79





                    if (scaleFactor) {
                        let productLengthInPx = scaleFactor * product.params.dimensions.length;
                        console.log('productLengthInPx', product.name, productLengthInPx);

                        let finallySizeKoeff = productLengthInPx / product.crop_wh[1];
                        console.log('finallySizeKoeff', product.name, finallySizeKoeff);

                        img.height = product.crop_wh[1] * finallySizeKoeff;
                        img.style.border = '1px dotted #00f';

                        img.title = img.title + '     DEBUG:' + product.waist_length + ';' +  scaleFactor + ';' +  productLengthInPx + ';' + finallySizeKoeff + ';'
                    }


                }

                div.appendChild(img);
                container.appendChild(div);
                console.log(scale_factors);
            });
        }

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', displayImages);
    </script>
</body>
</html>
