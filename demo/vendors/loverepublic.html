<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Feed example</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-5XGX6WQ2');</script>
    <!-- End Google Tag Manager -->
    <style>
        .gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px; /* Расстояние между картинками */
        }
        .gallery img {
            max-width: 200px;
            height: auto;
            border-radius: 8px; /* Опционально: скругление углов */
            border: 1px dotted #000;
        }
    </style>
</head>

<body style="overflow: auto;">
<div class="gallery" id="gallery"></div>

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-5XGX6WQ2"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="http://fonts.cdnfonts.com/css/slick" rel="stylesheet" />


    <link rel="stylesheet" href="../../dist/widget.css" />
    <!-- <link rel="stylesheet" href="../../custom/lacoste_tr.css" />  -->
    <link rel="stylesheet" href="../../custom/loverepublic.css" />
    <!--<link rel="stylesheet" href="../../custom/inline.css" />-->
    <style>
        html {
            /* padding: 20px; */
            /*font-family: Formular, sans-serif;*/
           font-family: "Open Sans", Arial, sans-serif;
        }

        body {
            margin: 0;
        }

        .garderobo-main-block2 {width: 90%}

        .garderobo-grid-look__product_grid_v_3 {
            border: 1px dotted #f00;
            opacity: 1;
        }

        .product_with_dimensions {
            border: 1px solid #0f0;
            opacity: 1;
        }

        .garderobo-grid-look__product2 {
            border: 1px dotted #000;
        }

        #garderobo {
            width: 100%;
        }

        .garderobo-widget-look-container2 {
            padding: 0 !important;
            border: 1px dotted #f00;
            overflow: hidden;
        }

    </style>
    <div id="modatech-shop-the-model"></div>

    <div style="height: 1000px"></div>

    <div id="garderobo-additional-recommended" style="width: 100%;"></div>
    <div>
        <div id="garderobo-platform"></div>
        <div id="garderobo" dir="ltr">
        </div>
    </div>
    <button id="btn-init-2">Add To Second Place</button>
    <script src="../../dist/widget.js"></script>

    <script>
        let session = "9bff9644fe7e4b32a01ee7cf2009f027"; //a3bc156a7b0c49f6bb89b1fa886c8e41

        localStorage.setItem("_garderoboSessionKey", session);

       // localStorage.removeItem("_garderoboWidgetParams");
       // localStorage.removeItem("_garderoboWidgetParamsLastUpdate");

        const urlParams = new URLSearchParams(window.location.search);
        var product_id = urlParams.get('product_id');
        var all_data;
        var cartProducts = [];

        if (!product_id)
            product_id = 277633;

        _garderoboWidget.then(function (widget) {

            widget.init('47d2ff3e24684e58abfcbd3dda60c3e8', { lang_code: 'ru', price_tier: "", city: "Санкт-Петербург" }); //c14000e3b5d24026a6816874b6a177bb

            //widget.initProduct(document.getElementById('garderobo'), {
            //    product_id: product_id
            //});
            //widget.initCustom(document.getElementById('garderobo-widget'), {
            //    yml_id: 2385597360545, "code": "child_pdp", "page_type": "custom" //2385598001317
            //});

            widget.registerCallback.addToCartEvent(function (productId) {
                console.log("------productId", productId);
            });

            widget.registerCallback.linkClicked(function (url) {
                console.log('Link clicked: ' + url);
            });

            initProduct(product_id);
        }, 100);

        function initProduct(product_id) {
            _garderoboWidget.then(function (widget) {
                widget.initProduct(document.getElementById('garderobo'), {
                    group_id: product_id, product_id: product_id, scroll_button: {position: document.querySelector('.product-gallery__outer'), caption: 'How To Wear', offset: 0, mainElement: 'html'}
                    //product_id: '3424989', scroll_button: {position: document.querySelector('.product-detail-carousel'), caption: 'GÖRÜNÜMÜNÜZÜ TAMAMLAYIN'}
                });

                /*widget.initCustom(document.getElementById('garderobo'), {
                    yml_id: product_id,
                    code: "child_pdp",
                    scroll_button: {
                        position: document.querySelector('.product-gallery__outer'),
                        caption: 'How To Wear',
                        offset: 0,
                        mainElement: 'html'
                    }
                });*/

                //widget.initCategory(document.getElementById('garderobo'), {category_id: product_id});

                //widget.initMain(document.getElementById('garderobo'));

                //widget.initCart(document.getElementById('garderobo'), {});

                widget.registerCallback.onDataLoaded(function(data) {
                    let images = [];
                    for (var i = 0; i < data.blocks.length; i++) {
                        if (data.blocks[i].look_img) {
                            images.push(data.blocks[i].look_img);
                        }
                    }
                    const gallery = document.getElementById('gallery');

                    images.forEach(imageUrl => {
                        const imgElement = document.createElement('img');
                        imgElement.src = imageUrl;
                        imgElement.alt = "Картинка";
                        gallery.appendChild(imgElement);
                    });

                    all_data = data;

                    //widget.initPlatform(document.getElementById('garderobo-platform'), {product_id: data.product_id});

                    /*const garderoboElement = document.getElementById('garderobo');
                    let findedMainProduct = false;
                    for (let i=0; i < data.blocks.length; i++) {
                        for (let j=0; j < data.blocks[i].products.length; j++) {
                            let product = data.blocks[i].products[j];
                            if (product['is_main_product'] && !findedMainProduct) {
                                findedMainProduct = true;
                                const img = document.createElement('img');
                                img.src = product.pictures[0];
                                img.alt = 'Main product';
                                img.style.display = 'block'; // Чтобы изображение было на отдельной строке, если необходимо

                                // Вставить изображение перед элементом
                                garderoboElement.parentNode.insertBefore(img, garderoboElement);

                                break;
                            }
                        }
                    }*/

                    setInterval(function() {
                        registerLinksCallback();
                    }, 1000);
                });

                widget.setFavoritesProducts(['269939']);

                let sizes = [];
                widget.registerCallback.addToCartEvent(function(sizeId, data) {
                    sizes.push(sizeId);
                    widget.setCartProducts(sizes, '/cart/');
                });

                widget.registerCallback.openPopup(function() {
                    console.log('Open popup');
                    widget.setFavoritesProducts([264031]);
                    widget.setCartProducts([265949], '/cart/');
                });

                let favorites = [];
                widget.registerCallback.addToFavorites(function(sizeId, state) {
                    console.log(sizeId, state);
                    if (state) {
                        if (!favorites.includes(sizeId)) {
                            favorites.push(sizeId); // Добавляем, только если такого элемента еще нет
                        }
                    } else {
                        const index = favorites.indexOf(sizeId);
                        if (index !== -1) {
                            favorites.splice(index, 1); // Удаляем первый найденный элемент
                        }
                    }
                    console.log(favorites);
                    widget.setFavoritesProducts(favorites);
                });

                widget.registerCallback.subscribeToProduct(function(productId) {
                     console.log(productId);
                });

                /*if (urlParams.get('product_id') || (apiKey != 'c0737cf450fb42d685853399f9699fe3'))
                    widget.initProduct(document.getElementById('garderobo'), {
                        product_id: product_id, city: "Москва"
                    });
                else
                    widget.initCustom(document.getElementById('garderobo'), {
                        "code": "merchandise", "page_type": "custom", "filters": {"look_tag": "zhenschinyi-41-162"}
                    });*/

                //widget.initCart(document.getElementById('garderobo'), {"yml_ids": [2385597847305,2385597639757,2385597494509,2385597068090,2385597509364]});
                //widget.initFavorites(document.getElementById('garderobo'), {"product_ids": [2385597847305,2385597639757,2385597494509,2385597068090,2385597509364]});
                //widget.initCategory(document.getElementById('garderobo'), {"category_id": 3042});
            });
        }
    </script>
    <script>
        function registerLinksCallback() {
            const links = document.querySelectorAll('a');

            // Добавляем обработчик события 'click' для каждой ссылки
            links.forEach(link => {
                if (!link.classList.contains('processed')) {
                    link.classList.add('processed');
                    var href = link.getAttribute('href');
                    try {
                        var url = new URL(href);
                        var pathWithoutParams = url.origin + url.pathname;
                        var queryParams = url.search.replace('?', '');

                        for (var i = 0; i < all_data.blocks.length; i++) {
                            for (var j = 0; j < all_data.blocks[i].products.length; j++) {
                                let product = all_data.blocks[i].products[j];
                                if ((product.link.hasst == pathWithoutParams) || (product.link.search(pathWithoutParams) == 0)) {
                                    link.setAttribute('href', 'http://localhost:3000/demo/vendors/loverepublic?product_id=' + product.group_id + '&' + queryParams);
                                }
                            }
                        }
                    } catch {
                      }
                }
            });
        }
    </script>

    ====================<br />
    ====================<br />
    <div class="garderobo-main-block2" id="modatech-widgets" dir="ltr" style="width: 80%;"></div>
    <div id="add_to_cart">Add To Cart</div>
    <div id="purchase">Purchase</div>
    <div id="garderobo_position_2"></div>
    <button onclick="exportToImage()">Export to Image</button>

    <script>
        function exportToImage() {
            setTimeout(function() {
                _garderoboWidget.then(function (widget) {
                    //widget.init(apiKey, {lang_code: '', customWidgetsCssPath: 'https://widget.garderobo.ai/widget.css'});
                });

                //initProduct(4774285);
            }, 3000);
        }

        const button = document.getElementById('add_to_cart');
        button.addEventListener('click', function() {
            //initProduct(9032631963);
            _garderoboWidget.then(function(widget) {
                widget.addToCart({"product_id": product_id});
                //widget.addToCart({"product_id": 2385597889916});
            });
        });

        const buttonPurchase = document.getElementById('purchase');
        buttonPurchase.addEventListener('click', function() {
            //initProduct(9032631963);
            _garderoboWidget.then(function(widget) {
                //widget.registerPurchase({ products_data: [{"product_id": product_id}]});
                widget.registerPurchase({ products_data: [{"product_id": product_id, "count": 2}, {"product_id": 2385597847251, "count": 1}]});
            });
        });

        document.querySelector('#btn-init-2').addEventListener('click', function() {
            alert('aaa');
            _garderoboWidget.then(function (widget) {
                widget.initCustom(document.getElementById('modatech-widgets-2'), {
                    code: "product_cart_popup", yml_id: product_id
                });
            });
        });

    </script>
</body>

</html>