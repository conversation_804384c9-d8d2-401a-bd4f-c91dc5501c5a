<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Feed example</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <link rel="stylesheet" href="../../dist/widget.css" />
    <link rel="stylesheet" href="../../custom/platform.css" />

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">

</head>
<style>
    a {
        text-decoration: none;
        color: #ffa2e5;
        font-weight: 500;
    }

    a:hover {
        text-decoration: underline;
    }

    .div1 {
        width: 70vw;
        height: 70vh;
        border: 1px solid #f00;
        display: flex;
        justify-content: center;
        padding: 24px;
    }

    .div2 {
        background-color: #f00;
        flex: 0 0 100px;
        aspect-ratio: 1/1;
    }

    #menu a {
        margin-right: 48px;
        color: #000;
    }

    #menu {
        background: #f0f0f0;
        margin: 0;
        padding: 20px;
        margin-bottom: 25px;
    }
</style>

<body style="max-width: 1300px; margin: 0 auto; padding-bottom: 100px;">
<ul id="menu">
    <a href="/articles/">Articles</a>
    <a href="/bloggers/">Bloggers</a>
    <a href="/favorites-looks/">Favorites Looks</a>
    <a href="/subscriptions/">Subscriptions</a>
</ul>

<div id="modatech-main-container">
</div>
</body>
<script src="../../dist/widget.js"></script>
<!--<script src="https://testwidget.garderobo.ai/widget.js"></script>-->
<script>
    var apiKey = '0a73cabc9fa24483a6c4a90ce620bf5a';
    //var apiKey = '69a661e8591843c0a754b4f9684ffa95';

    function loadUri(uri) {
        //const url = 'http://0.0.0.0:8100/api/content' + uri;
        const url = 'https://platform.modatech.ru/api/content' + uri;

        if (uri == '/favorites-looks/') {
            _garderoboWidget.then(function (widget) {
                widget.init(apiKey, {user_id: '222', is_platform: true, lang_code: 'ru'});
                widget.initPlatform(document.getElementById('modatech-main-container'), {page_type: "favorites_looks"});
            });

            return true;
        } else if (uri == '/subscriptions/') {
            _garderoboWidget.then(function (widget) {
                widget.init(apiKey, {user_id: '222', is_platform: true, lang_code: 'ru'});
                widget.initPlatform(document.getElementById('modatech-main-container'), {page_type: "subscriptions"});

                setTimeout(function() {
                    $('#modatech-main-container a, .modatech-articles-list-item a').click(function (e) {
                        e.preventDefault();
                        loadUri($(this).attr('href'));
                    });
                }, 1000);
            });

            return true;
        }

        fetch(url, {
            method: 'GET',
            headers: {
                'x-api-key': apiKey,
            },
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.text(); // Преобразуем тело ответа в текст
            })
            .then(data => {
                // Устанавливаем текстовое содержимое тела ответа в элемент <body>
                document.getElementById('modatech-main-container').innerHTML = data;

                _garderoboWidget.then(function (widget) {
                    //widget.initPlatform(document.getElementById('modatech-main-container'), {page_type: "favorites_looks"});
                    widget.init(apiKey, {user_id: '222', is_platform: true, lang_code: 'ru'});
                    widget.initPlatform();
                    widget.registerCallback.addToCartEvent(function (size, product) {
                        setTimeout(function() {
                            widget.setCartProducts([size], '/cart/');
                        }, 1000);
                        console.log('CART', size, product);
                    });

                    widget.registerCallback.addToFavorites(function(product, state) {
                        console.log("FAVORITES", product, state);
                    });

                    widget.setCartProducts(['2385598044116', '2385598044123', '1449177', '1531241'], '/cart/');
                    //widget.setFavoritesProducts(['1032222', '1237206', '1307459']);

                    setTimeout(function() {
                        $('#modatech-main-container a, .modatech-articles-list-item a').click(function (e) {
                            e.preventDefault();
                            loadUri($(this).attr('href'));
                        });
                    }, 1000);
                })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            });
    }

    //loadUri('/articles/empowering-e-commerce-turning-online-shopping-into');
    //loadUri('/articles/triumf-krasnogo-siianie-i-lakomye-tsveta-v-chem-vstretit-novyi-god/');
    //loadUri('/articles/triumf-krasnogo-siianie-i-lakomye-tsveta-v-chem-vstretit-novyi-god/');
    loadUri('/articles/');

    $('a').click(function(e) {
        e.preventDefault();
        loadUri($(this).attr('href'));
    });
</script>

</html>