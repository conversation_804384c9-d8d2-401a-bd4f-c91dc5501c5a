var dcConst = {
    widgetContainerClass: 'dc-widget-container',
    widgetFeedContainerClass: 'dc-widget-feed',
    widgetItemClass: 'dc-widget-feed-item',
    widgetImageClass: 'dc-widget-product-image',
    widgetHeaderClass: 'dc-widget-product-header',
    widgetFooterClass: 'dc-widget-product-footer',
    widgetNameClass: 'dc-widget-product-name',
    widgetPriceClass: 'dc-widget-product-price',
    widgetOldPriceClass: 'dc-widget-product-old-price',
    widgetSessionKey: '_dcSessionKey',
    widgetLastItemsKey: '_dcLastItems'
};

var dcWidget = function(config) {
    var self = this;

    self.key = null;

    switch(config.env) {
        case 'test':
            self.apiBase = 'https://testapi.garderobo.ai/api/v3/widget/';
        break;
        case 'dev':
            self.apiBase = 'http://192.168.1.52:8001/api/v3/widget/';
        break;
        default: case 'prod':
            self.apiBase = 'https://api.garderobo.ai/api/v3/widget/';
        break;
    }


    self.page = null;

    self.sessionKey = window.localStorage.getItem(dcConst.widgetSessionKey);

    self._style = null;

    self.getSessionPromise = function() {
        return new Promise(function(resolve, reject) {
            if(!self.sessionKey) self.fetch('start_session/', 'post', data)
                .then(function(data) {
                    resolve(data.session);
                }).catch(function() {
                    reject();
                });
            else resolve(self.sessionKey);
        });
    };

    self.init = function(key, options) {
        self.key = key;

        if(!options) options = {};
        var data = {};

        if(options.itemsCount) self.itemsCount = options.itemsCount;
        if(options.email) data.email = options.email;
        if(options.user_id) data.user_id = options.user_id;
        if(options.phone) data.phone = options.phone;

        // @TODO: Log unauthorized access
        self.sessionPromise = new Promise(function(resolve, reject) {
            if(!self.sessionKey) self.fetch('start_session/', 'post', data)
                .then(function(data) {
                    resolve(data.session);
                }).catch(function() {
                    reject();
                });
            else resolve(self.sessionKey);
        });

        window._dcSession = self.sessionPromise;

        self.sessionPromise.then(function(key) {
            self.sessionKey = key;
            window.localStorage.setItem(dcConst.widgetSessionKey, self.sessionKey);
        });

        self._style = document.createElement('link');
        self._style.rel = 'stylesheet';
        self._style.href = 'https://widgettest.dresscode.ai/widget.css'; // @TODO: Add key

        return self;
    };

    self.initPage = function(containerEl, type, options) {
        if(!options) options = {};
        options.type = type;

        if(!self.page) {
            containerEl.insertBefore(self._style, containerEl.parent);
            self.page = new dcPage(containerEl, type, self);
            self.page.init(options);

            self.page.draw();
        }
        else {
            self.page.redraw(options);
        }

        return self;
    };

    self.initCategory = function(containerEl, options) {
        return self.initPage(containerEl, 'category', options);
    };

    self.initProduct = function(containerEl, options) {
        return self.initPage(containerEl, 'product', options);
    };

    self.initCart = function(containerEl, options) {
        return self.initPage(containerEl, 'cart', options);
    };

    self.initMain = function(containerEl, options) {
        return self.initPage(containerEl, 'main', options);
    };

    self.addToCart = function(product_id) {
        if(!self.page) throw Error('You must initialize the page first');

        self.productAction(product_id, 'cart', { page_type: self.page.type });
    };

    self.cartHandler = function(options) {
        return function() {
            self.addToCart(options.product_id);
        }
    };

    self.attachCartHandler = function(cartEl, options) {
        cartEl.removeEventListener('click', self.cartHandler(options));
        cartEl.addEventListener('click', self.cartHandler(options));
    };

    self.followVendorProductPage = function(options) {
        if(!self.page) throw Error('You must initialize the page first');

        if(!options.hasOwnProperty('product_id')) throw Error('You must provide product_id to track user transitions');

        self.productAction(options.product_id, 'follow', { page_type: self.page.type });
    };

    self.registerPurchase = function(options) {
        if(!self.page) throw Error('You must initialize the page first');

        if(options.hasOwnProperty('product_ids')) for(var id in options.product_ids)
            self.productAction(id, 'bought', { page_type: self.page.type });
        else throw Error('You must provide product_ids to track purchases');
    };

    self.productAction = function(product_id, action, data) {
        data = data ? data : {};
        data.yml_id = product_id;
        data.action = action;

        self.fetch('product_action/', 'post', data,'json').catch(function () { });
    };

    self.updateSession = function(options) {
        if(!options) options = {};
        var data = {};

        if(options.email) data.email = options.email;
        if(options.user_id) data.user_id = options.user_id;
        if(options.phone) data.phone = options.phone;

        self.fetch('update_session/', 'post', data).catch(function () { });
    };

    self.stopSession = function() {
        self.sessionKey = null;
        window.localStorage.removeItem(dcConst.widgetSessionKey);
    };

    self.fetch = function(uri, type, data, dataType) {
        return new Promise(function(resolve, reject) {
            type = type ? type : 'get';
            dataType = dataType ? dataType : 'json';
            var contentType = dataType === 'json' ? 'application/json' : null;

            var req = new XMLHttpRequest();

            var statechange = function() {
                if(this.readyState == 4) {
                    if(this.status == 200) resolve(
                        dataType === 'json'
                            ? JSON.parse(this.responseText)
                            : this.responseText
                    );
                    else reject(this);
                }
            };

            var fail = function() {
                // @TODO: Learn what to do :/
                reject(this);
            };

            req.addEventListener('readystatechange', statechange);
            req.addEventListener('error', fail);
            req.addEventListener('abort', fail);

            var fData = new FormData;

            if(type == 'get') {
                var res = [];

                for (key in data)
                    res.push(encodeURIComponent(key) + '=' + encodeURIComponent(data[key]));

                uri += '?' + res.join('&');
            }

            req.open(type, self.apiBase + uri, true);

            if(self.sessionKey) req.setRequestHeader('Authorization', 'Bearer ' + self.sessionKey);
            if(contentType) req.setRequestHeader('Content-Type', contentType);
            if(self.key) req.setRequestHeader('X-Api-Key', self.key);

            req.send(JSON.stringify(data));
        });
    };
};

var dcPage = function(containerEl, type, widget) {
    var self = this;

    self._widget = widget;
    self.containerEl = containerEl;
    self.feedContainerEls = {};
    self.itemsCount = widget.itemsCount;
    self.type = type;

    self.init = function(options) {
        switch(self.type) {
            case 'category':
                if(options.hasOwnProperty('category_id'))
                    options.yml_id = options.category_id;
                else throw Error('You must provide category_id to track this page as category page');
            break;
            case 'product':
                var lastItems = window.localStorage.getItem(dcConst.widgetLastItemsKey);
                lastItems = lastItems ? JSON.parse(lastItems) : {};

                if(options.hasOwnProperty('product_id')) {
                    if (lastItems.hasOwnProperty(options.product_id)) {
                        options.block_type = lastItems[options.product_id];
                    }
                    options.yml_id = options.product_id;
                }
                else  throw Error('You must provide product_id to track this page as product page');
            break;
        }

        self.redraw(options).then(function(blocks) {
            blocks.map(function(block) {
                var scrollListener = function() {
                    var isInViewport = block.el.getBoundingClientRect().top < window.innerHeight && block.el.getBoundingClientRect().top + block.el.clientHeight > 0;
                    if(isInViewport) {
                        self._widget.fetch('show_block/', 'post', {
                            page_type: self.type,
                            block_type: block.type,
                            yml_ids: block.yml_ids
                        });
                        window.removeEventListener("scroll", scrollListener);
                    }
                };
                window.addEventListener('scroll', scrollListener);
                scrollListener();
            })
        });
    };

    self.render = function() {
        self.widgetContainer = document.createElement('div');
        self.widgetContainer.classList.add(dcConst.widgetContainerClass);

        return self.widgetContainer;
    };

    self.drawFeedItems = function(name, type, items) {
        if(!self.feedContainerEls[type]) {
            self.feedContainerEls[type] = document.createElement('div');
            self.feedContainerEls[type].classList.add(dcConst.widgetFeedContainerClass);
            self.widgetContainer.appendChild(self.feedContainerEls[type]);
            var containerHeader = document.createElement('header');
            containerHeader.innerHTML = name;
            self.feedContainerEls[type].appendChild(containerHeader);
        }

        Array.prototype.map.call(self.feedContainerEls[type].children, function(child) {
            if(child.classList.contains(dcConst.widgetItemClass)) self.feedContainerEls[type].removeChild(child);
        });

        self.renderFeedItems(type, items).map(function (item) {
            self.feedContainerEls[type].appendChild(item);
        });

        return self.feedContainerEls[type];
    };

    self.renderFeedItems = function(type, items) {
        return items.map(function (item) { return self.prepareFeedItem(type, item); });
    };

    self.prepareFeedItem = function(type, item) {
        var feedItemRender = self.renderFeedItem(item);

        if(item.link) feedItemRender.control.addEventListener('click', function() {
            var lastItems = window.localStorage.getItem(dcConst.widgetLastItemsKey);
            lastItems = lastItems ? JSON.parse(lastItems) : {};

            lastItems[item.yml_id] = type;
            window.localStorage.setItem(dcConst.widgetLastItemsKey, JSON.stringify(lastItems));

            window.location.href = item.link;
        });

        return feedItemRender.el;
    };

    self.renderFeedItem = function(item) {
        var feedItem = document.createElement('div');
        feedItem.classList.add(dcConst.widgetItemClass);

        var productHeader = document.createElement('div');
        productHeader.classList.add(dcConst.widgetHeaderClass);

        var productImg = document.createElement('div');
        productImg.classList.add(dcConst.widgetImageClass);
        productImg.style = 'background-image: url(' + item.pictures[0]+ ')';

        var productFooter = document.createElement('div');
        productFooter.classList.add(dcConst.widgetFooterClass);

        var productName = document.createElement('div');
        productName.classList.add(dcConst.widgetNameClass);
        productName.innerHTML = item.name; // @TODO: Name here should be

        var priceContainer = document.createElement('div');

        if(item.old_price) {
            var productOldPrice = document.createElement('div');
            productOldPrice.classList.add(dcConst.widgetOldPriceClass);
            productOldPrice.innerHTML = item.old_price.toLocaleString() + ' руб.';
            priceContainer.append(productOldPrice);
        }

        var productPrice = document.createElement('div');
        productPrice.classList.add(dcConst.widgetPriceClass);
        productPrice.innerHTML = item.price.toLocaleString() + ' руб.';
        priceContainer.append(productPrice);

        productFooter.appendChild(productName);
        productFooter.appendChild(priceContainer);

        feedItem.appendChild(productHeader);
        feedItem.appendChild(productImg);
        feedItem.appendChild(productFooter);

        return { el: feedItem, control: productImg };
    };

    self.fetchFeed = function(options) {
        var uri = 'open_page/';
        var data = {
            page_type: options.type
        };

        if(options.yml_id)
            data.yml_id = options.yml_id;

        if(options.block_type)
            data.block_type = options.block_type;

        return new Promise(function(resolve, reject) {
            self._widget.sessionPromise.then(function() {
                resolve(self._widget.fetch(uri, 'post', data));
            }).catch(function () { reject(); });
        });
    };

    self.draw = function() {
        containerEl.appendChild(self.render());
    };

    self.redraw = function(options) {
        return new Promise(function(resolve, reject) {
            self.fetchFeed(options).then(function (data) {
                var resolveData = [];

                if (data && data.blocks) {
                    for (bKey in data.blocks) {
                        var block = data.blocks[bKey];

                        resolveData.push({
                            type: block.type,
                            el: self.drawFeedItems(block.name, block.type, block.products),
                            yml_ids: block.products.map(function(product) { return product.yml_id })
                        });
                    }
                }

                resolve(resolveData);
            }).catch(function () {
                reject();
            });
        });
    };

    return self;
};