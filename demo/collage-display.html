<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Collage Display</title>
    <style>
        .collage-container {
            position: relative;
            width: 1000px;
            height: 1000px;
            border: 1px solid #ccc;
            margin: 0 auto;
            background-color: #f9f9f9;
            overflow: hidden;
        }
        
        .product-image {
            position: absolute;
            background-size: contain;
            background-repeat: no-repeat;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <h1>Product Collage Display</h1>
    <div class="collage-container" id="collageContainer"></div>

    <script>
        // Product data will be loaded here
        const productData = {
            products: [
                // Your product data will be inserted here
            ]
        };

        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('collageContainer');
            
            // Function to create and position product images
            function createProductCollage(products) {
                products.forEach(product => {
                    if (product.collage_data) {
                        const imgElement = document.createElement('div');
                        imgElement.className = 'product-image';
                        
                        // Set background image
                        imgElement.style.backgroundImage = `url(${product.picture})`;
                        
                        // Position and size based on collage_data
                        imgElement.style.left = `${product.collage_data.left}%`;
                        imgElement.style.top = `${product.collage_data.top}%`;
                        imgElement.style.width = `${product.collage_data.width}%`;
                        imgElement.style.height = `${product.collage_data.height}%`;
                        imgElement.style.zIndex = product.collage_data.zIndex || 1;
                        
                        // Set background position
                        imgElement.style.backgroundPosition = `${product.collage_data.backgroundPositionX || 'center'} ${product.collage_data.backgroundPositionY || 'center'}`;
                        
                        // Add title for hover
                        imgElement.title = `${product.brand} - ${product.name} - ${product.price}₽`;
                        
                        container.appendChild(imgElement);
                    }
                });
            }
            
            // Load the product data
            fetch('product-data.json')
                .then(response => response.json())
                .then(data => {
                    createProductCollage(data.products);
                })
                .catch(error => {
                    console.error('Error loading product data:', error);
                    // Fallback to use the embedded data if fetch fails
                    createProductCollage(productData.products);
                });
        });
    </script>
</body>
</html>
