(function() {
    if(
        document
        && window
        && !window.dcWidgetIsLoaded
        && document.body
    ) {
        window._dcWidget = new Promise(function (resolve, reject) {
            var url;
            var loadConfig = new Promise(function(resolve, reject) {
                var interval = setTimeout(function () {
                    if(!window._dcWidgetConfig) window._dcWidgetConfig = {};

                    if(window._dcWidgetConfig.hasOwnProperty('env') && ['prod', 'test', 'dev'].indexOf(window._dcWidgetConfig.env) === -1) {
                        window._dcWidgetConfig.env = 'prod';
                    }
                    resolve(window._dcWidgetConfig);
                    clearInterval(interval);
                }, 50);
            });
            loadConfig.then(function(config) {
                switch (config.env) {
                    case 'test':
                        url = '//widgettest.garderobo.ai/widget.js';
                    break;
                    case 'dev':
                        url = '../dist/widget.js';
                    break;
                    default: case 'prod':
                        url = '//widget.garderobo.ai/widget.js';
                    break;
                }

                var script = document.createElement('script');
                script.src = url;

                script.addEventListener('load', function() {
                    window.dcWidgetIsLoaded = true;
                });

                document.body.appendChild(script);

                var interval = setInterval(function () {
                    if (window.dcWidgetIsLoaded) {
                        resolve(new dcWidget(config));
                        clearInterval(interval);
                    }
                }, 1000);
            });
        });
    }
})();