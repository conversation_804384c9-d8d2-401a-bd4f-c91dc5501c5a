<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<body>
<div>
    <script src="https://unpkg.com/@vkid/sdk@<3.0.0/dist-sdk/umd/index.js"></script>
    <script type="text/javascript">
        function generateCodeVerifier(length = 64) {
            const allowedChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
            let verifier = '';
            const randomValues = new Uint8Array(length);
            window.crypto.getRandomValues(randomValues);
            for (let i = 0; i < length; i++) {
                verifier += allowedChars.charAt(randomValues[i] % allowedChars.length);
            }
            return verifier;
        }

        const codeVerifier = generateCodeVerifier();
        localStorage.setItem('vk_code_verifier', codeVerifier);

        if ('VKIDSDK' in window) {
            const VKID = window.VKIDSDK;

            VKID.Config.init({
                app: 53145750,
                codeVerifier: codeVerifier,
                redirectUrl: 'https://test.visaver.online/auth/vk/callback',
                responseMode: VKID.ConfigResponseMode.Callback,
                source: VKID.ConfigSource.LOWCODE,
                scope: 'email', // Заполните нужными доступами по необходимости
            });

            const oAuth = new VKID.OAuthList();

            oAuth.render({
                container: document.currentScript.parentElement,
                oauthList: [
                    'vkid',
                    'ok_ru',
                    'mail_ru'
                ]
            })
                .on(VKID.WidgetEvents.ERROR, vkidOnError)
                .on(VKID.OAuthListInternalEvents.LOGIN_SUCCESS, function (payload) {
                    const code = payload.code;
                    const deviceId = payload.device_id;

                    VKID.Auth.exchangeCode(code, deviceId)
                        .then(vkidOnSuccess)
                        .catch(vkidOnError);
                });

            function vkidOnSuccess(data) {
                // Обработка полученного результата
            }

            function vkidOnError(error) {
                // Обработка ошибки
            }
        }
    </script>
</div>
</body>
</html>