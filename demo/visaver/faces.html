<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <title>Face Recognition Video Processor</title>
  <style>
    * {
      box-sizing: border-box;
    }
    body {
      font-family: Arial, sans-serif;
      background: #f5f5f5;
      margin: 0;
      padding: 20px;
    }
    /* Контейнер на всю ширину */
    .container {
      width: 100%;
      margin: 0 auto;
      background: #fff;
      padding: 20px;
    }
    h1, h2 {
      color: #333;
    }
    .section {
      margin-bottom: 40px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input[type="text"], select {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    button {
      padding: 10px 15px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #0056b3;
    }
    .clear-btn {
      background-color: red;
    }
    .video-list {
      margin-top: 20px;
    }
    .video-item {
      border-bottom: 1px solid #ddd;
      padding: 10px 0;
    }
    /* Сетка для аннотированных кадров (3 на строку) */
    .frame-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    .frame-container img {
      width: calc(33.333% - 10px);
      border: 1px solid #ccc;
      border-radius: 4px;
      height: auto;
    }
  </style>
</head>
<body>
<div class="container">
  <h1>Face Recognition Video Processor</h1>

  <!-- Раздел для отправки видео на обработку -->
  <div class="section" id="process-video-section">
    <h2>Отправить видео на обработку</h2>
    <label for="videoUrl">Ссылка на видео:</label>
    <input type="text" id="videoUrl" placeholder="Введите URL видео">
    <button id="processVideoBtn">Обработать видео</button>
    <p id="processVideoMessage"></p>
  </div>

  <!-- Раздел для получения списка обработанных видео -->
  <div class="section" id="list-videos-section">
    <h2>Обработанные видео</h2>
    <button id="loadVideosBtn">Загрузить список видео</button>
    <div id="videosList" class="video-list"></div>
  </div>

  <!-- Раздел для поиска лица в видео с прогрессом -->
  <div class="section" id="search-face-section">
    <h2>Поиск лица в видео</h2>
    <label for="videoSelect">Выберите видео:</label>
    <select id="videoSelect">
      <option value="">-- Выберите видео --</option>
    </select>
    <label for="faceUrl">Ссылка на фотографию лица:</label>
    <input type="text" id="faceUrl" placeholder="Введите URL фотографии">
    <button id="searchFaceBtn">Найти лицо</button>
    <p id="searchFaceMessage"></p>
    <div id="framesContainer" class="frame-container"></div>
  </div>

  <!-- Раздел для очистки базы данных -->
  <div class="section" id="clear-database-section">
    <h2>Очистка базы данных</h2>
    <button id="clearDatabaseBtn" class="clear-btn">Очистить базу данных</button>
    <p id="clearDatabaseMessage"></p>
  </div>
</div>

<script>
  // Базовый URL для API
  const BASE_URL = "http://0.0.0.0:8000";

  // Отправка видео на обработку
  document.getElementById("processVideoBtn").addEventListener("click", async function() {
    const videoUrl = document.getElementById("videoUrl").value;
    const messageElem = document.getElementById("processVideoMessage");
    messageElem.textContent = "Обработка видео...";

    try {
      const response = await fetch(BASE_URL + "/videos/process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ video_url: videoUrl })
      });
      const data = await response.json();
      if (response.ok) {
        messageElem.textContent = "Видео отправлено на обработку. Public ID: " + data.public_id;
        loadVideos();
      } else {
        messageElem.textContent = "Ошибка: " + data.detail;
      }
    } catch (error) {
      messageElem.textContent = "Ошибка: " + error;
    }
  });

  // Функция для загрузки списка видео
  async function loadVideos() {
    const videosListElem = document.getElementById("videosList");
    const videoSelectElem = document.getElementById("videoSelect");
    videosListElem.innerHTML = "Загрузка...";
    videoSelectElem.innerHTML = '<option value="">-- Выберите видео --</option>';

    try {
      const response = await fetch(BASE_URL + "/videos");
      const videos = await response.json();
      if (videos.length === 0) {
        videosListElem.innerHTML = "Нет обработанных видео.";
        return;
      }
      videosListElem.innerHTML = "";
      videos.forEach(video => {
        const div = document.createElement("div");
        div.className = "video-item";
        div.innerHTML = `<strong>${video.title}</strong> (Public ID: ${video.public_id})`;
        videosListElem.appendChild(div);

        // Добавляем видео в выпадающий список для поиска
        const option = document.createElement("option");
        option.value = video.public_id;
        option.textContent = `${video.title} (${video.public_id})`;
        videoSelectElem.appendChild(option);
      });
    } catch (error) {
      videosListElem.innerHTML = "Ошибка загрузки видео: " + error;
    }
  }

  // Загружаем список видео при загрузке страницы
  window.addEventListener("load", loadVideos);

  // Очистка базы данных
  document.getElementById("clearDatabaseBtn").addEventListener("click", async function() {
    const messageElem = document.getElementById("clearDatabaseMessage");
    messageElem.textContent = "Очистка базы данных...";
    try {
      const response = await fetch(BASE_URL + "/videos/clear", {
        method: "DELETE"
      });
      const data = await response.json();
      if (response.ok) {
        messageElem.textContent = data.message;
        loadVideos();
      } else {
        messageElem.textContent = "Ошибка: " + data.detail;
      }
    } catch (error) {
      messageElem.textContent = "Ошибка: " + error;
    }
  });

  // Поиск лица с прогрессом (SSE с использованием EventSource)
  document.getElementById("searchFaceBtn").addEventListener("click", function() {
    const videoSelect = document.getElementById("videoSelect");
    const public_id = videoSelect.value;
    const faceUrl = document.getElementById("faceUrl").value;
    const messageElem = document.getElementById("searchFaceMessage");
    const framesContainer = document.getElementById("framesContainer");
    framesContainer.innerHTML = "";
    messageElem.textContent = "Запуск поиска...";

    if (!public_id || !faceUrl) {
      messageElem.textContent = "Выберите видео и введите URL фотографии.";
      return;
    }

    // Формируем URL SSE. Здесь передаем face_url как query-параметр.
    const sseUrl = `${BASE_URL}/videos/${public_id}/search-face-progress?face_url=${encodeURIComponent(faceUrl)}`;

    const eventSource = new EventSource(sseUrl);

    eventSource.onmessage = function(event) {
      console.log("Получено сообщение:", event.data);
      // Если сообщение начинается с '{', считаем, что это JSON с результатами.
      if (event.data.trim().startsWith("{")) {
        const result = JSON.parse(event.data);
        messageElem.textContent = `Обработка завершена. Найдено ${result.matching_frames.length} совпадений.`;
        result.matching_frames.forEach(framePath => {
          const img = document.createElement("img");
          // Дополняем относительный путь базовым URL.
          img.src = `${BASE_URL}/${framePath}`;
          framesContainer.appendChild(img);
        });
        eventSource.close();
      } else {
        // Обновляем текст сообщения с информацией о прогрессе.
        messageElem.textContent = event.data;
      }
    };

    eventSource.onerror = function(error) {
      console.error("Ошибка EventSource:", error);
      messageElem.textContent = "Ошибка при получении данных.";
      eventSource.close();
    };
  });
</script>
</body>
</html>
