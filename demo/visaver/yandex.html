<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <title>Авторизация через Яндекс</title>
    <style>
        #auth-button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            background-color: #ffcc00;
            border: none;
            border-radius: 4px;
        }
        #auth-button:hover {
            background-color: #ffbb00;
        }
    </style>
</head>
<body>
<div id="auth-container"></div>

<script>
    // Настройки для авторизации
    const clientId = 'f26f7d63701b4885a8549abf00c01de8'; // замените на ваш client_id
    const redirectUri = 'https://test.visaver.online/auth/yandex/callback'; // ваш Redirect URI
    const authUrl = 'https://oauth.yandex.ru/authorize';

    // Формируем URL для авторизации
    const params = new URLSearchParams({
        response_type: 'code',
        client_id: clientId,
        redirect_uri: redirectUri
    });
    const fullAuthUrl = `${authUrl}?${params.toString()}`;

    // Создаём кнопку авторизации
    const button = document.createElement('button');
    button.id = 'auth-button';
    button.textContent = 'Войти через Яндекс';

    // Обработчик клика по кнопке
    button.addEventListener('click', () => {
        window.location.href = fullAuthUrl;
    });

    // Добавляем кнопку в контейнер
    document.getElementById('auth-container').appendChild(button);
</script>
</body>
</html>
