<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Поиск товаров</title>
  <style>
    #search-container {
      margin-bottom: 20px;
    }
    #search-input {
      width: 300px;
      padding: 10px;
      font-size: 16px;
    }
    #search-button {
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
    }
    #results {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    .product-card {
      width: 200px;
      text-align: center;
    }
    .product-image {
      width: 200px;
      height: 200px;
      object-fit: cover;
    }
    .product-info {
      margin-top: 10px;
      font-size: 14px;
    }
    .loader {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
<div id="search-container">
  <input type="text" id="search-input" value="Кроссовки зим жен carnaby" placeholder="Введите запрос...">
  <button id="search-button">Искать</button>
</div>
<div id="results"></div>

<script>
  let groups = {
    "Кроссовки": "shoes_sneakers",
    "Платье": "layer-full_dress",
    "Рубашка": "layer-2_shirt",
    "Перчатки": ["gloves_various", "gloves"],
    "Футболка": "layer-1_tshirt",
    "Поло": "layer-1_tshirt",
    "Кофта": "layer-2_sweater",
    "Лонгслив": ["layer-2_sweater", "layer-1_tshirt"],
    "Брюки": ["bottom_pants", "bottom_sportpants"],
    "Юбка": "bottom_skirt",
    "Куртка": ["layer-3_jacket", "layer-3_sportjacket"],
    "Обувь": "shoes",
    "Сумка": "bag",
    "Сандалии": ["shoes_sandals", "shoes_flipflops"],
    "Шорты": "bottom_shorts",
    "Ботинки": "shoes_boots",
    "Пальто": "layer-3_coat",
    "Головной убор": ["hat_bucket", "hat", "hat_cap"],
    "Шарф": "accessory_scarf",
    "Джинсы": "bottom_jeans",
    "Аксессуар для волос": "accessory_head",
    "Жилет": "layer-3_vest",
    "Носки": "socks",
    "Аксессуар": "accessory",
    "Купальник": "swimwear_suit",
    "Леггинсы": "bottom_leggings",
    "Рюкзак": "bag_backpack",
    "Тренч": "layer-3_trench",
    "Плавки": "swimwear_trunks",
    "Спортивный костюм": "layer-full_sportsuit",
    "Шлепанцы": ["shoes_sandals", "shoes_flipflops"],
    "Ремень": "belt",
    "Нижнее белье": "underwear"
  };

  let seasons = {
    "лето": "Весна-Лето",
    "весна": "Весна-Лето",
    "осень": "Осень-Зима",
    "зима": "Осень-Зима"
  };

  let genders = {
    "муж": ["Муж.", "Унисекс"],
    "жен": ["Жен.", "Унисекс"],
    "мальчики": ["Детский, Подростки Мальчики"],
    "девочки": ["Детский", "Подростки Девочки"],
    "дети": ["Детский, Подростки Мальчики, Подростки Девочки"],
  };

  var prompt = 'Ты умный поисковый движок, умеющий предугадывать запросы пользователя. Пользователь ищет: "%search_line%".\n' +
          'Попробуй предугадать набор параметров, которые ему интересны. Ищи даже по куску введенного в поиске слова, если встретишь последовательное совпадение хотя бы 2х букв. Параметры для анализа (выбирай только из списка, не дописывай сам ничего!):\n' +
          'gender (муж, жен, мальчики, девочки, дети)\n' +
          'cloth_type (Кроссовки, Платье, Рубашка, Кофта, Лонгслив, Бомбер, Перчатки, Футболка, Поло, Свитер, Брюки, Юбка, Куртка, Обувь, Сумка, Сандалии, Шорты, Ботинки, Пальто, Головной убор, Шарф, Джинсы, Аксессуар для волос, Жилет, Носки, Аксессуар, Купальник, Леггинсы, Рюкзак, Тренч, Кепка, Плавки, Спортивный костюм, Шлепанцы, Ремень, Нижнее белье)\n' +
          'color (белый, черный, красный, синий, зеленый, желтый, серый, розовый, фиолетовый, оранжевый, коричневый, бежевый, голубой)\n' +
          'season (лето, зима, весна, осень, демисезон)\n' +
          'full_cloth_type - полное название предугаданного типа одежды в свободной форме\n' +
          'model - сюда в виде массива давай добавим все слова, которые могли бы относиться к модели, артикулу, бренду, коллаборации, любые цифры и т.д.\n' +
          'Если значение параметра невозможно определить, укажи "undefined".\n' +
          'Все части фразы поиска, которые не участвуют в найденных параметрах, верни массивом в параметре not_recognized.\n' +
          'Учитывай, что пользователь может использовать сокращения или части слов (например, "юб" = "юбка", "зе" = "зеленый"). Также учитывай синонимы, например "штаны"="брюки" и т.д.\n' +
          'Возвращай ответ только в json';

  async function sendChatRequest(prompt) {
    const apiUrl = `https://api.deepseek.com/chat/completions`;
    const apiKey = `sk-9ca4c445bc4c4173aa54153ce704b41b`;

    const payload = {
      model: "deepseek-chat",
      messages: [
        { role: "user", content: prompt }
      ],
      max_completion_tokens: 64000,
      response_format: {
        type: "json_object"
      }
    };

    const headers = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${apiKey}`
    };

    try {
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      } else {
        console.error(`Error ${response.status}: ${await response.text()}`);
        throw new Error(`Error ${response.status}: ${await response.text()}`);
      }
    } catch (error) {
      console.error("Request failed:", error);
      throw error;
    }
  }

  async function loadJSON() {
    try {
      const response = await fetch('lacoste.json');
      if (!response.ok) {
        throw new Error('Ошибка загрузки файла');
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Ошибка:', error);
    }
  }

  function mapper(category, value) {
    let result = null;

    switch (category) {
      case "groups":
        result = groups[value];
        break;
      case "seasons":
        result = seasons[value];
        break;
      case "genders":
        result = genders[value];
        break;
      default:
        result = "undefined";
    }

    return result;
  }

  function postprocessResponse(data) {
    if (data.gender) {
      data.gender = mapper("genders", data.gender);
    }

    if (data.cloth_type) {
      data.cloth_type = mapper("groups", data.cloth_type);
    }

    if (data.season) {
      data.season = mapper("seasons", data.season);
    }

    return data;
  }

  function showLoader() {
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = '<div class="loader"></div>';
  }

  function hideLoader() {
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = '';
  }

  function search(query) {
    console.log('1');
    showLoader(); // Показываем лоадер

    sendChatRequest(prompt.replace('%search_line%', query))
            .then(response => {
              console.log('2');
              const parsedResponse = JSON.parse(response['choices'][0]['message']['content']);
              const processedData = postprocessResponse(parsedResponse);
              filterAndDisplayProducts(query, processedData);
            })
            .catch(error => {
              console.error("Ошибка поиска:", error);
              hideLoader(); // Скрываем лоадер в случае ошибки
            });
  }

  function filterProducts(products, searchParams) {
    // Функция для замены ё на е
    const normalizeText = (text) => {
      return text.toLowerCase().replace(/ё/g, 'е');
    };

    if (searchParams.model && searchParams.model.length > 0) {
      searchParams.model = searchParams.model.filter(word => {
        const normalizedWord = normalizeText(word); // Нормализуем слово
        return products.some(product => {
          // Проверяем, подходит ли товар по другим фильтрам
          const matchesGender = !searchParams.gender || searchParams.gender === "undefined" ||
                  (Array.isArray(searchParams.gender)
                          ? searchParams.gender.includes(product.gender)
                          : product.gender === searchParams.gender);

          const matchesClothType = !searchParams.cloth_type || searchParams.cloth_type === "undefined" ||
                  (Array.isArray(searchParams.cloth_type)
                          ? searchParams.cloth_type.includes(product.group)
                          : product.group === searchParams.cloth_type);

          const matchesSeason = !searchParams.season || searchParams.season === "undefined" ||
                  (Array.isArray(searchParams.season)
                          ? searchParams.season.includes(product.season)
                          : product.season === searchParams.season);

          // Нормализуем название товара
          const normalizedProductName = normalizeText(product.name);

          // Если товар подходит по другим фильтрам, проверяем, содержит ли он текущее слово
          return matchesGender && matchesClothType && matchesSeason &&
                  normalizedProductName.includes(normalizedWord);
        });
      });
    }

    // Фильтрация товаров
    return products.filter(product => {
      // Фильтр по gender
      const matchesGender = !searchParams.gender || searchParams.gender === "undefined" ||
              (Array.isArray(searchParams.gender)
                      ? searchParams.gender.includes(product.gender)
                      : product.gender === searchParams.gender);

      // Фильтр по cloth_type
      const matchesClothType = !searchParams.cloth_type || searchParams.cloth_type === "undefined" ||
              (Array.isArray(searchParams.cloth_type)
                      ? searchParams.cloth_type.includes(product.group)
                      : product.group === searchParams.cloth_type);

      // Фильтр по season
      const matchesSeason = !searchParams.season || searchParams.season === "undefined" ||
              (Array.isArray(searchParams.season)
                      ? searchParams.season.includes(product.season)
                      : product.season === searchParams.season);

      const matchesNotRecognized = !searchParams.model || searchParams.model.length === 0 ||
              searchParams.model.every(word => {
                const normalizedWord = normalizeText(word); // Нормализуем слово
                const normalizedProductName = normalizeText(product.name); // Нормализуем название товара
                return normalizedProductName.includes(normalizedWord);
              });

      // Возвращаем true, если все фильтры выполнены
      return matchesGender && matchesClothType && matchesSeason && matchesNotRecognized;
    });
  }

  async function filterAndDisplayProducts(query, searchParams) {
    console.log('3');
    const products = await loadJSON();
    const resultsContainer = document.getElementById('results');
    resultsContainer.innerHTML = '';

    if (!products) return;

    const filteredProducts = filterProducts(products, searchParams);
    console.log('4');

    const debug = document.createElement('div');
    debug.innerText = JSON.stringify(searchParams);
    debug.style.width = '100%';
    debug.style.marginBottom = '30px';
    resultsContainer.appendChild(debug);

    console.log('5');
    const vectorResponse = await sendRequestToVectorHub(query, searchParams, filteredProducts);
    if (!vectorResponse) {
      console.error('Ошибка при запросе к VectorHub');
      return;
    }

    const vectoredProducts = getProductsByIds(filteredProducts, vectorResponse.product_ids);
    const sortedProducts = sortProductsByScores(vectoredProducts, vectorResponse);
    console.log('6');

    counter = 0;
    sortedProducts.forEach(product => {
      const productCard = document.createElement('div');
      productCard.classList.add('product-card');

      const img = document.createElement('img');
      img.src = product.img;
      img.alt = product.name;
      img.classList.add('product-image');

      const productInfo = document.createElement('div');
      productInfo.classList.add('product-info');
      productInfo.innerHTML = `
        <div>${product.name}</div>
        <div>Пол: ${product.gender}</div>
        <div>Сезон: ${product.season}</div>
      `;

      productCard.appendChild(img);
      productCard.appendChild(productInfo);
      resultsContainer.appendChild(productCard);

      counter++;
      if (counter > 100)
        return true;
    });
  }

  async function sendRequestToVectorHub(query, searchParams, filteredProducts) {
    // Добавляем color и full_cloth_type к запросу, если они есть
    if (searchParams['color'] && searchParams['color'] !== 'undefined')
      query += ' ' + searchParams['color'];
    if (searchParams['full_cloth_type'] && searchParams['full_cloth_type'] !== 'undefined')
      query += ' ' + searchParams['full_cloth_type'];

    const url = 'http://127.0.0.1:5000/search';
    const product_ids = filteredProducts.map(item => item.id.toString()); // Преобразуем id в строки
    const params = {
      query: query,
      product_ids: product_ids.join(',')
    };

    const queryString = new URLSearchParams(params).toString();
    const fullUrl = `${url}?${queryString}`;

    try {
      // Выполняем запрос синхронно с помощью await
      const response = await fetch(fullUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Парсим ответ в JSON
      const data = await response.json();
      console.log('Response data:', data);

      // Возвращаем данные для дальнейшей обработки
      return data;
    } catch (error) {
      console.error('Error:', error); // Обрабатываем ошибки
      return null; // Возвращаем null в случае ошибки
    }
  }

  // Функция для получения данных из JSON по id
  function getProductsByIds(products, ids) {
    const productMap = new Map(products.map(product => [product.id.toString(), product]));
    return ids.map(id => productMap.get(id)).filter(product => product !== undefined);
  }

  // Функция для сортировки товаров по scores
  function sortProductsByScores(products, responseData) {
    // Создаем карту для быстрого доступа к scores по product_id
    const scoreMap = new Map();
    responseData.product_ids.forEach((id, index) => {
      scoreMap.set(id, responseData.scores[index]);
    });

    // Сортируем товары по scores
    return products.slice().sort((a, b) => {
      const scoreA = scoreMap.get(a.id.toString()) || 0;
      const scoreB = scoreMap.get(b.id.toString()) || 0;
      return scoreB - scoreA; // Сортировка по убыванию
    });
  }


  document.getElementById('search-button').addEventListener('click', () => {
    const query = document.getElementById('search-input').value.trim();
    if (query) {
      search(query);
    }
  });

  document.getElementById('search-input').addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      const query = document.getElementById('search-input').value.trim();
      if (query) {
        search(query);
      }
    }
  });
</script>
</body>
</html>