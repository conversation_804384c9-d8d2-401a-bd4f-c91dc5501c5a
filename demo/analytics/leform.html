<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>test</title>
</head>
<body>

</body>
<script>
    let data = {
        "event_name": "add_to_cart",
        "fetch_field": "yml_id",
        "fetch_id": "120918",
        "version": 7,
        "session_key": "30d66249bde34596a8a587f4d4dfbc67",
        "device_type": "Desktop",
        "price": 1000,
        "old_price": 2000,
        "stat_url": "https://stat.modatech.ru/",
        "api_key": "f9d303e83bd841a6a5bbd4c45f34497a"
    }

    var formData = new URLSearchParams();
    for (var key in data) {
        formData.append(key, data[key]);
    }

    const requestOptions = {
        method: 'POST',
        body: formData,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    };

    const response = fetch(data['stat_url'], requestOptions)
        .then(response => response.json()) // if the response is in JSON format
        .then(data => console.log(data))
        .catch(error => console.error('Error:', error));
</script>
</html>
