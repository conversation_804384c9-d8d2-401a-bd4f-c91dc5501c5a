<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>test</title>
</head>
<body>

</body>
<script>
    let data = {
        "event_name": "add_to_cart",
        "fetch_field": "yml_id",
        "fetch_id": "4486950",
        "version": 7,
        "session_key": "441e37cd5c7143e096880340f9ca0161",
        "device_type": "Desktop",
        "price": 1000,
        "old_price": 2000,
        //"stat_url": "https://stat.garderobo.ai/",
        "stat_url": "https://us-central1-gar1-346511.cloudfunctions.net/analytics-endpoint",
        "api_key": "2045b3b345264e1eb4e97fd7234e3ef6",
        "event_id": "d32af6f6a334fa0a92e4f292cca61fa8"
    }

    var formData = new URLSearchParams();
    for (var key in data) {
        formData.append(key, data[key]);
    }


    const requestOptions = {
        method: 'POST',
        body: formData,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    };

    for (var i = 0; i < 3; i++) {
        var formData = new URLSearchParams();
        for (var key in data) {
            formData.append(key, data[key]);
        }

        var success = navigator.sendBeacon(data['stat_url'], formData);
    }
</script>
</html>
