<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>test</title>
</head>
<body>

</body>
<script>
    function garderoboGetCartDiscount(ecommerce) {
        var items_full_price = 0;
        for (var i = 0; i < ecommerce.items.length; i++) {
            items_full_price += ecommerce.items[i].price;
        }

        var discount_percent = ((items_full_price - ecommerce.value) / items_full_price) * 100;
        console.log(discount_percent);
    }

    var garderobo_ecommerce =  {
        "transaction_id": "2812680902113894",
        "value": 331.65,
        "shipping": 69.9,
        "tax": 10,
        "currency": "TRY",
        "items": [
            {
                "item_sku": "3614319",
                "item_model": "RA2100",
                "item_id": "RA2100.00L",
                "item_name": "Lacoste Erkek Çizgili Lacivert Çorap",
                "affiliation": "Lacoste",
                "coupon": "",
                "discount": 0,
                "in_stock": "Yes",
                "index": 0,
                "is_gift": "no",
                "gtin": "8001036143190",
                "item_brand": "Lacoste",
                "item_category": "Kadın",
                "item_category2": "Giyim",
                "item_category3": "Çorap",
                "item_list_id": "Giyim",
                "item_list_name": "Success Page",
                "item_variant": "40 - 43",
                "price": 349,
                "quantity": 1,
                "basecode": "RA2100"
            }
        ]
    };

    garderoboGetCartDiscount(garderobo_ecommerce);

    var garderobo_products = garderobo_ecommerce.items;
    var garderobo_transaction_id = garderobo_ecommerce.transaction_id;
    for (var i=0; i < garderobo_products.length; i++) {
        var garderobo_data = {
            "event_name": "purchase",
            "fetch_field": "yml_id",
            "fetch_id": garderobo_products[i].item_sku,
            "version": 7,
            "timestamp": Date.now(),
            "session_key": localStorage.getItem("_garderoboSessionKey"),
            "device_type": 'Desctop',
            "transaction_id": garderobo_transaction_id,
            "stat_url": "https://us-central1-gar1-346511.cloudfunctions.net/analytics-endpoint",
            "api_key": "c14000e3b5d24026a6816874b6a177bb"
        };

        var garderobo_quantity = 1;
        if (garderobo_products[i].quantity)
            garderobo_quantity = garderobo_products[i].quantity;

        for (var j=0; j < garderobo_quantity; j++) {
            var garderobo_formData = new URLSearchParams();
            for (var garderobo_key in garderobo_data) {
                garderobo_formData.append(garderobo_key, garderobo_data[garderobo_key]);
            }

            const requestOptions = {
                method: 'POST',
                body: garderobo_formData,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
            };

            const response = fetch(garderobo_data['stat_url'], requestOptions)
                .then(response => response.json())
                .then(data => console.log(garderobo_data))
                .catch(error => console.error('Error:', error));
        }
    }
</script>
</html>
