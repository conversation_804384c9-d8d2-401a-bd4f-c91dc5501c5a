<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>test</title>
</head>
<body>

</body>
<script>
    let data = {
        "event_name": "add_to_cart",
        "fetch_field": "sku_id",
        "fetch_id": "42118395691191",
        "version": 7,
        "session_key": "********************************",
        "device_type": "Desktop",
        "price": 1000,
        "old_price": 2000,
        //"stat_url": "https://stat.garderobo.ai/",
        "api_key": "47d2ff3e24684e58abfcbd3dda60c3e8"
    }

    var formData = new URLSearchParams();
    for (var key in data) {
        formData.append(key, data[key]);
    }

    const requestOptions = {
        method: 'POST',
        body: formData,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
    };

    const response = fetch(data['stat_url'], requestOptions)
        .then(response => response.json()) // if the response is in JSON format
        .then(data => console.log(data))
        .catch(error => console.error('Error:', error));
</script>
</html>
