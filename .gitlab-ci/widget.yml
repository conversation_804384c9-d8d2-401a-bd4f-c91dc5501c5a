image: cr.yandex/crp93o2iiq8vcbj06jit/gitlab-ci-runner:latest

stages:
  - build
  - deploy

build:
  stage: build
  artifacts:
    paths:
      - dist
      - custom.tar.gz
    expire_in: 3 days
  before_script:
    - export ENV="ENV_${TARGET}" && echo "${!ENV}" > "$(pwd)/.env"
  script:
    - npm install --save-dev
    - npm run-script build
    - sentry-cli releases files "$CI_COMMIT_SHA" upload-sourcemaps dist/

deploy:
  stage: deploy
  environment:
    name: $TARGET
    url: $URL
  before_script:
    - echo "${GOOGLE_STORAGE_KEY}" > "$(pwd)/gc_keys.json"
    - gcloud auth activate-service-account <EMAIL> --key-file=./gc_keys.json --quiet
  script:
    - |+
      set -e
      aws --endpoint-url=https://s3.ru-1.storage.selcloud.ru s3 sync dist/ s3://$SELECTEL_BUCKET_NAME
      aws --endpoint-url=https://s3.ru-1.storage.selcloud.ru s3 sync custom/ s3://$SELECTEL_BUCKET_NAME/custom
      if [[ -n "$GOOGLE_BUCKET_NAME" ]]; then
        gsutil -m -q cp -r ./dist/* "gs://$GOOGLE_BUCKET_NAME/"
        gsutil -m -q cp -r ./custom/* "gs://$GOOGLE_BUCKET_NAME/custom/"
      fi
    - sentry-cli releases deploys "$CI_COMMIT_SHA" new -e $TARGET
  after_script:
    - gcloud <NAME_EMAIL> --quiet
