require('dotenv').config();
const Dotenv = require('dotenv-webpack');
const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
require('es6-promise/auto');

module.exports = {
    devtool: 'source-map',
    entry: ['babel-polyfill', './src/widget.js'],
    output: {
        path: __dirname + '/dist',
        filename: 'widget.js',
    },
    plugins: [
        new Dotenv(),
        new MiniCssExtractPlugin({
            filename: 'widget.css',
        }),
    ],
    // devServer: {
    //     port: 8082,
    //     historyApiFallback: true,
    //     hot: true,
    //     open: ['demo/example_local.html'],
    // },
    optimization: {
        minimize: process.env.NODE_ENV === 'production',
        minimizer: [
            new UglifyJsPlugin({
                test: /\.js(\?.*)?$/i,
                sourceMap: true,
                uglifyOptions: {
                    mangle: { toplevel: true },
                    compress: { drop_console: true, toplevel: true },
                    output: {
                        ast: true,
                        code: true,
                    },
                },
            }),
        ],
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                include: path.resolve(__dirname, 'src'),
                use: [
                    {
                        loader: 'babel-loader',
                        options: {
                            presets: ['babel-preset-env'],
                        },
                    },
                    'webpack-conditional-loader',
                ],
            },
            {
                test: /\.less$/,
                use: [MiniCssExtractPlugin.loader, 'css-loader', 'less-loader'],
            },
            // {
            //     test: /\.(png|jp(e*)g|svg|gif)$/,
            //     use: [
            //         {
            //             loader: 'file-loader',
            //             options: {
            //                 name: 'images/[hash]-[name].[ext]',
            //             },
            //         },
            //     ],
            // },
        ],
    },
};
