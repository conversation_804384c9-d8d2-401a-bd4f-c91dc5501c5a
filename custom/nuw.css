.garderobo-widget-container {
    --dark: #141414;
    --gray: #858585;
    --gray-light: #e5e5e5;
    --alert: #ff0000;
    font-family: 'Vremena-grotesk-book', serif;
}

.garderobo-widget-container .garderobo-widget-feed {
    padding-inline: 10px;
    margin-bottom: 40px;
}

.garderobo-widget-add-all-product-wrap {
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    flex-flow: row;
    align-items: flex-start;
    margin-bottom: 40px;
    position: relative;
}

.garderobo-widget-feed-container-new .garderobo-widget-brand-name {
    display: none;
}

.garderobo-widget-brand-name {
    display: block !important;
    margin-bottom: 5px;
    font-size: 1rem;
}

.garderobo-widget-feed-container-new .garderobo-widget-feed-item:last-child {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 20px;
    font-family: inherit;
    color: #231f20;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 20px;
    text-transform: none;
}
.garderobo-widget-popup-content .garderobo-widget-feed header {
    display: none;
}

.garderobo-widget-control-left,
.garderobo-widget-control-right {
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}

@media only screen and (min-width: 768px) and (max-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 1.25rem;
        font-family: inherit;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 1.25rem;
        font-family: inherit;
    }
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-feed-items {
    gap: 20px;
    -webkit-scroll-snap-type: x mandatory;
    -moz-scroll-snap-type: x mandatory;
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory;
    -webkit-scroll-behavior: smooth;
    -moz-scroll-behavior: smooth;
    -ms-scroll-behavior: smooth;
    scroll-behavior: smooth;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    flex: 0 0 calc((100% / 6) - (5 * 20px / 6));
    width: auto !important;
    -webkit-scroll-snap-align: start;
    -moz-scroll-snap-align: start;
    -ms-scroll-snap-align: start;
    scroll-snap-align: start;
    -webkit-scroll-snap-stop: normal;
    -moz-scroll-snap-stop: normal;
    -ms-scroll-snap-stop: normal;
    scroll-snap-stop: normal;
    margin-bottom: 0 !important;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    height: auto;
    flex-direction: column;
    border: 1px solid transparent;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    width: 100%;
    height: 100%;
}
.garderobo-widget-popup .garderobo-widget-feed-item-look {
    height: 100%;
}

.garderobo-widget-popup-content .garderobo-widget-feed-items {
    height: 100%;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    width: 100%;
    padding-top: 132%;
    background-repeat: no-repeat;
    background-size: cover;
    margin-bottom: 16px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-product-image {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    font-size: 0.9rem;
    line-height: 1.2;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    width: 90%;
    margin-bottom: 12px;
    font-size: 1rem;
    font-family: inherit;
    line-height: 1.2;
    order: 1;
    white-space: normal;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.garderobo-widget-brand-and-price {
    order: 2;
}

.garderobo-widget-sizes-and-cart-btn {
    order: 3;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn {
    font: 1.2rem 'FFDIN Medium', 'FFDIN', Arial, sans-serif;
    color: #fff;
    background-color: var(--alert);
    border: none;
    letter-spacing: 0.03em;
    padding: 1.3rem 1.6rem 1.4rem;
    text-transform: uppercase;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn:hover {
    background-color: #c00;
    color: #fff;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    order: 2;
    display: flex;
    align-items: baseline;
    justify-content: flex-start;
    width: 100%;
    margin-bottom: 0.75rem;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container .garderobo-widget-product-price {
    font-family: 'rubl bold', 'FFDIN Medium';
    font-size: 1.5rem;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    position: relative;
    display: flex;
    margin-right: 8px;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1;
    color: #fe0000;
    text-transform: uppercase;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price::before {
    position: absolute;
    transform: translateY(-50%);
    top: 50%;
    content: '';
    border-bottom: solid 1px black;
    backface-visibility: hidden;
    height: 0;
    width: calc(100% - 14px);
    text-transform: uppercase;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price:after {
    content: "/";
    margin-left: 8px;
    font-weight: 700;
    color: #000;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    display: flex;
    color: #000;
    font-family: "Vremena-grotesk-medium", serif;
    font-weight: 700;
    font-size: 1rem;
    text-transform: uppercase;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    speak-as: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
    opacity: .5;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:hover,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:hover {
    opacity: 1;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    color: #616667;
    width: 16px;
    height: 16px;
    content: '';
    display: block;
    position: absolute;
    top: 17px;
    left: 17px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    left: 19px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-left-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(14.000000, 14.000000) rotate(-180.000000) translate(-14.000000, -14.000000) translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group' transform='translate(0.000000, -0.000000)'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    left: 15px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-right-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-product-sale-badge {
    display: none;
    position: absolute;
    top: 15px;
    left: 10px;
    padding: 0.4rem 0.5rem 0.5rem;
    font-size: 1.2rem;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    color: #fff;
    background-color: var(--alert);
}

.garderobo-widget-product-sale-badge::after {
    content: '';
    display: block;
    position: absolute;
    top: 2.1rem;
    left: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 1rem 1rem 0;
    border-color: transparen;
    border-right-color: var(--alert);
}

.garderobo-widget-product-control-dislike {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-sizes {
    background-color: #fff;
    border: solid 0.1rem #eee;
    box-shadow: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-sizes:hover {
    border-color: #eee;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-all-product-wrap {
    font-size: 1.5rem;
}

#btn-special-look {
    font-size: 12px;
}

@media screen and (min-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        flex: 0 0 calc((100% / 3) - (2 * 20px / 3));
    }

    .garderobo-widget-container .garderobo-widget-feed {
        padding-inline: 10px;
    }
}

@media screen and (min-width: 992px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed {
        padding-inline: 10px;
    }
}

@media screen and (min-width: 992px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        flex: 0 0 calc((100% / 4) - (3 * 20px / 4));
    }
}

@media screen and (min-width: 1140px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        flex: 0 0 calc((100% / 6) - (5 * 20px / 6));
    }

    .garderobo-widget-container .garderobo-widget-feed {
        padding-inline: 10px;
    }
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 22%;
    left: 37%;
}

.garderobo-widget-look-product--socks {
    display: none;
}

.garderobo-widget-popup-list-item-img {
    position: relative !important;
}

.garderobo-widget-popup-list-item-text-title {
    font-weight: 600;
    margin-top: 0;
}

.garderobo-widget-popup-list-item-swap-container-item::before {
    display: none;
}

.garderobo-widget-look-product--custom-left-top {
    z-index: 10;
    bottom: 45%;
}

.garderobo-widget-look-product--custom-right-top {
    bottom: 45%;
    background-position-y: bottom;
}

.garderobo-widget-look-product--custom-bottom {
    top: 45%;
    height: 50%;
}

.garderobo-widget-look-product--gloves {
    display: none;
}

.garderobo-widget-look-product--bag {
    width: 18%;
}

.garderobo-widget-look-product--center-bottom {
    width: 30% !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 30% !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--wear {
    height: 50%;
    width: 30%;
}

.garderobo-widget-look-product--right-top-3 {
    left: auto !important;
    right: 0 !important;
    top: 31% !important;
    z-index: 10 !important;
}

.garderobo-widget-look-product--hat {
    width: 16%;
}

.garderobo-widget-popup-container .garderobo-widget-sizes {
    margin: 0;
    padding: 10px;
    width: 100%;
    height: 56px;
    font-size: 17px;
}

.garderobo-widget-popup-list-item-text-cart-btn, .garderobo-widget-popup-content-product-add-to-cart-btn {
    position: relative;
    height: 56px;
    width: 100%;
    padding: 0 10px;
    margin-top: 10px;
    font-size: 0;
    background-color: #000;
    border-width: 1px;
    border-color: #231f20;
} 

.garderobo-widget-popup-content-product-add-to-cart-btn {
    margin-top: 0;
}

.garderobo-widget-popup-list-item-text-cart-btn:before,
.garderobo-widget-popup-content-product-add-to-cart-btn::before {
    content: "+ добавить в корзину";
    font-size: 17px;
    font-weight: 400;
    color: #fff;
    text-transform: lowercase;
}

.garderobo-widget-popup-list-item-text-cart-btn-link-state::before {
    content: "Перейти в корзину";
    text-transform: lowercase;
}

.garderobo-widget-look__btn-buy {
    height: 44px;
    padding: 0 10px;
    background-color: #000;
    border-color: #231f20;
}

.garderobo-widget-popup-list-item-text-cart-btn:not(.garderobo-widget-popup-list-item-text-cart-btn--disabled):hover,
.garderobo-widget-popup-content-product-add-to-cart-btn:not(.garderobo-widget-popup-content-product-add-to-cart-btn-disabled):hover {
    background-color: #14FF26;
    border-color: #fbfd02;
}

.garderobo-widget-popup-list-item-text-cart-btn:not(.garderobo-widget-popup-list-item-text-cart-btn--disabled):hover:before,
.garderobo-widget-popup-content-product-add-to-cart-btn:not(.garderobo-widget-popup-content-product-add-to-cart-btn-disabled):hover::before {
    color: #000;
}

.garderobo-widget-popup-content-product-add-to-cart-btn-disabled {
    opacity: 0.4;
}

.garderobo-widget-popup-list-item-text-brand {
    display: block;
    margin: 0;
} 

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 0;
    margin: auto;
    aspect-ratio: 1 / 1;
    max-width: 100%;
    /* width: 100%; */
}

.garderobo-widget-popup__btn-close {
    padding: 8px;
    right: 20px;
}

.garderobo-widget-popup-content {
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
    padding: 0;
    border-right: none;
}

.garderobo-widget-popup .garderobo-widget-feed {
    border: none;
    align-self: unset;
    margin-top: 0;
    margin-right: 0;
    height: 100%;
    flex: 1;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    flex: 0 0 calc((100% / 3) - (2 * 20px / 3));
    cursor: pointer;
    margin-bottom: 80px !important;
    padding: 0 !important;
}

.garderobo-widget-container .garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1 / 1;
    box-sizing: border-box;
    /* width: 100%; */
    max-height: 100%;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-look-container {
    position: relative;
}

.garderobo-widget-container-for-popup .garderobo-widget-feed-item-look .garderobo-widget-feed-items {
    -webkit-scroll-snap-type: x mandatory;
    -moz-scroll-snap-type: x mandatory;
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory;
    -webkit-scroll-behavior: smooth;
    -moz-scroll-behavior: smooth;
    -ms-scroll-behavior: smooth;
    scroll-behavior: smooth;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {    
    flex: 0 0 100%;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}
.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    height: unset !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-bottom: 0 !important;
}

.garderobo-widget-popup-list-header {
    display: none;
}

.garderobo-widget-popup-list-content {
    height: calc(100% - 24px);
    margin: 0;
    border: none;
    padding: 0 40px;
    margin-left: 20px !important;
    border-left: 1px solid #e0e0e0 !important;
}

.garderobo-widget-popup-list-item:first-child {
    border-top: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 40px 0;
    width: unset;
    gap: 24px;
    border-top: 1px solid #e0e0e0;
    margin-bottom: 10px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
    border-bottom: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 300px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
}

.garderobo-widget-popup-list-item-like-button, .garderobo-widget-popup-list-swap-item-like-button {
    display: none;
}

.garderobo-widget-popup-list-item-text-brand {
    display: block !important;
    margin: 0 !important;
    font-weight: 500 !important;
    text-transform: uppercase;
    font-size: 20px;
    line-height: 28px;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
    letter-spacing: 1px;
    font-family: 'Vremena-grotesk-book', serif;
    text-transform: initial;
}

.garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 24px;
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-popup-list-item-swap-button {
    display: block !important;
    border: 1px solid #BABBC1;
    width: 100%;
    height: 56px;
    padding: 12px 0;
    font-weight: 400;
    margin-top: 10px;
    text-transform: lowercase;
    font-size: 17px;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

/* SWAP CONTAINER */
.garderobo-widget-popup-list-item-swap-container {
    padding-bottom: 8px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 170px;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    display: none;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: relative;
    height: 80%;
    width: 100%;
    border: none !important;
    justify-content: start;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
    padding: 10px;
}

.garderobo-widget-popup-list-item-swap-item-img {
    max-height: 160px;
}

.garderobo-widget-popup-list-item-name {
    width: 100%;
    margin: 0 !important;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 300 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    text-align: center;
    margin-top: 10px !important;
    display: block !important;
    max-height: 65px;
    height: 70px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    margin-left: -1px;
    margin-top: -1px;
    padding: 2px 4px;
    font-size: 12px;
    line-height: 14px;
    border: 1px solid #ccc;
}

.garderobo-widget-popup-list-item-swap-item-price {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    height: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-look__btn-buy {
    height: 40px;
    padding: 0 16px;
    text-transform: uppercase;
    color: #fff;
    background-color: #000;
    border-color:  #231f20;
    padding-top: 4px;
}

.garderobo-widget-look__btn-buy:hover {
    color: #000;
    background-color: #14FF26;
    border-color: #fbfd02;
}


.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-grid-look__product-icon {
    display: block !important;
    width: 28px;
    height: 28px;
    background-color: #000;
    border: 2px solid #fff;
    border-radius: 50%;
    animation: vibrate 1s infinite;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
}

.garderobo-grid-look__product_img {
    position: relative;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-grid-look__product-icon-plus {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    height: 100%;
    transform: translateY(1px);
}

@keyframes vibrate {
    0% {
      box-shadow: 0 0 0 0 rgba(135, 129, 129, 0.7);
    }

    50% {
      box-shadow: 0 0 0 0.6rem rgba(0, 0, 0, 0);
    }

    100% {
      box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    }
}

/*----------------  PRODUCT POPUP ---------------------*/
.garderobo-widget-popup-content-pictures {
    height: calc(100% - 60px);
    width: 37%;
    overflow-y: auto;
    padding-right: 14px;
}

.garderobo-widget-popup-content-pictures::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.garderobo-widget-popup-content-pictures::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #DADFE9;
}

.garderobo-widget-popup-content-pictures-item {
    max-width: 230px;
    margin-bottom: 10px;
}

.garderobo-widget-popup-content-pictures-item:last-child {
    margin-bottom: 0;
}

.garderobo-widget-popup-content-pictures-item img {
    width: 100%;
}

.garderobo-product-popup-container {
    justify-content: flex-end;
}

.garderobo-product-popup-container .garderobo-widget-popup-list-item-like-button {
    position: unset;
}

.garderobo-product-popup {
    padding: 28px !important;
}

.garderobo-product-popup .garderobo-widget-popup-content {
    border: none;
    justify-content: flex-start;
    gap: 20px;
}

.garderobo-product-popup .garderobo-product-popup-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 22px;
}

.garderobo-product-popup .garderobo-product-popup-header .garderobo-widget-popup__btn-close {
    position: relative;
    right: 0;
    top: 0;
}

.garderobo-product-popup-header-title {
    font-size: 24px;
    line-height: 30px;
    font-weight: 500;
}


.garderobo-widget-popup-content {
    justify-content: flex-start;
}

.garderobo-widget-popup-content-product-info {
    height: calc(100% - 60px);
    width: 60%;
}

.garderobo-widget-popup-content-product-brand {
    font-family: Vremena-grotesk-book, sans-serif;
    color: #6a6a6a;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 4px;
    text-decoration: underline;
    text-transform: unset;
    margin-top: 0;
}

.garderobo-widget-popup-content-product-name {
    font-family: Vremena-grotesk-book, sans-serif;
    font-size: 28px;
    font-weight: 400;
    letter-spacing: -.02em;
    line-height: 60px;
    margin: 0;
    text-transform: none;
}

.garderobo-widget-popup-content-product-price {
    display: flex;
    font-size: 28px;
    font-weight: 400;
    line-height: 40px;
    letter-spacing: -.02em;
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 5px;
}

.garderobo-widget-popup-content-product-price-new {
}

.garderobo-widget-popup-content-product-price-old {
    color: rgba(0, 0, 0, .6);
    position: relative;
}

.garderobo-widget-popup-content-product-price-old::before {
    background: url("https://nuw.store/images/detail/strikethrough.svg") 100% / contain no-repeat;
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
}

.garderobo-widget-popup-content-product-sizes-block {
    margin-top: 24px;
    margin-bottom: 24px;

    span {
        display: block;
        margin-bottom: 10px;
    }
}

.garderobo-widget-popup-content-product-sizes {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.garderobo-widget-popup-content-product-sizes-item {
    min-width: 40px;
    height: 32px;
    border: 2px solid #ccc;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 16px;
    padding: 3px 10px 0 10px;
    line-height: 1;
}

.garderobo-widget-popup-content-product-sizes-item-selected {
    border: 2px solid #000;
}

.garderobo-widget-popup-content-product-add-to-cart-error-msg {
    margin-top: 8px;
    color: #ff3333;
}

.garderobo-widget-popup-content-product-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.garderobo-widget-popup-content-product-buttons .garderobo-widget-popup-list-item-like-button {
    width: 28px;
    height: 25px;
}

.garderobo-widget-popup-content-product-info {
    height: calc(100% - 60px);
    width: 60%;
}

@media (max-width: 576px) {
    .garderobo-product-popup {
        padding: 20px;
    }

    .garderobo-widget-popup-content-pictures {
        display: flex;
        width: 100%;
        padding: 0;
        overflow-x: auto;
        overflow-y: hidden;
        gap: 10px;
    }
    
    .garderobo-widget-popup-content-pictures-item {
        min-width: 130px;
    }

    .garderobo-widget-popup-content-product-info {
        width: 100%;
    }

    .garderobo-widget-popup {
        max-height: calc(100vh - 20px) !important;
        width: auto !important;
        top: 100%;
        transition: top 0.7s ease;
        position: fixed !important;;
    }

    .garderobo-widget-popup-container--opened .garderobo-widget-popup {
        top: 20px;
        position: relative !important;
    }

    .garderobo-widget-popup-container {
        top: 100% !important;
        display: unset !important;
        z-index: 99999 !important;
    }

    .garderobo-widget-popup-container.garderobo-widget-popup-container--opened {
        top: 0 !important;
    }
}


/* DESKTOP */
@media screen and (min-width: 769px) {
    .garderobo-widget-popup {
        height: 80vh !important;
        padding: 20px;
        max-width: 1300px;
        max-height: 600px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 50%;
        padding-top: 20px;
        height: calc(100% + 20px);
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-content {
        margin-left: 0 !important;
    }
} 

@media screen and (min-width: 1024px) {
    .garderobo-product-popup {
        height: 100% !important;
        max-width: 520px;
        max-height: 100vh;
        position: fixed;
        top: 0;
        right: 0;
        height: 100%;
        transform: translateX(100%);
        transition: transform 0.2s ease-in-out;
    }

    .garderobo-product-popup {
        max-width: 688px;
    }

    .garderobo-widget-popup-container--opened .garderobo-product-popup {
        transform: translateX(0);
    }

    .garderobo-product-popup-container {
        top: 100% !important;
        display: flex !important;
        z-index: 99999 !important;
        justify-content: flex-end;
    }

    .garderobo-product-popup-container.garderobo-widget-popup-container--opened {
        top: 0 !important;
    }
}

/* MOBILE */
@media screen and (max-width: 768px) {
    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: 140px !important;
    }

    .garderobo-widget-sizes {
        font-size: 14px !important;
    }

    .garderobo-widget-popup-list-content {
        margin: 0 20px 20px;
        border-left: none !important;
        padding: 0;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
        flex: none;
        height: 400px;
    }

    .garderobo-widget-popup-list-item-text-cart-btn,
    .garderobo-widget-popup-content-product-add-to-cart-btn {
        height: 44px;
    }

    .garderobo-widget-popup-list-item-text-cart-btn:before,
    .garderobo-widget-popup-content-product-add-to-cart-btn:before {
        font-size: 12px;
    }

    .garderobo-widget-popup-list-item-swap-button,
    .garderobo-widget-sizes {
        height: 44px !important;
        font-size: 12px !important;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        flex: 0 0 calc((100% / 2) - (20px / 2));
    }

    .garderobo-product-popup {
        padding: 20px;
    }
}

@media screen and (max-width: 550px) {
    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        flex: 0 0 100%;
    }
}

@media only screen and (min-width: 550px) and (max-width: 1023px)  {
    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        flex: 0 0 calc((100% / 2) - (20px / 2));
    }
}

@media screen and (max-width: 425px) {
    .garderobo-widget-feed-items {
        gap: 1px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        flex: 0 0 calc((100% / 2));
    }
}

.garderobo-looks-simple-popup {
    max-width: 800px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-img-v2 {
    object-fit: contain;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-img-wrapper a {
    padding: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-pic {
    padding: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-container {
    width: 100%;
    max-width: 100%;
}

.garderobo-scroll-button {
    background: url('https://media.garderobo.ru/nuw/scroll_button.svg') no-repeat;
    color: transparent;
    border-radius: unset;
    z-index: 900;
    left: 20px;
    top: 20px;
    width: 65px;
    height: 65px;
}

.garderobo-scroll-button .garderobo-btn-special-look-arrow {
    display: none;
}

.garderobo-scroll-button:hover {
    background-color: transparent;
}

.garderobo-widget-look-container div[data-category-collage="accessory_jevellery"] {
    display: none;
}