/* LUXXY */

.garderobo-widget-container {
    font-family: 'Montserrat', sans-serif;
    --dark: #141414;
    --gray: #858585;
    --gray-light: #e5e5e5;
    --alert: #dc3545;
}
.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 1.1rem;
    color: var(--dark);
    white-space: nowrap;
    text-align: center;
    margin-bottom: 40px;
    text-transform: uppercase;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    height: auto;
    flex-direction: column;
    width: calc(50% - 2rem);
    margin: 0 5px 20px 5px;
}

.garderobo-widget-container
    .garderobo-widget-feed
    .garderobo-widget-feed-container
    .garderobo-widget-feed-items
    .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user {
    width: calc(100% - 10px);
    margin: 0 5px 20px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    align-items: flex-start;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 225px;
    width: 100%;
    max-width: 240px;
    background-repeat: no-repeat;
    background-size: contain;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    font-size: 0.9rem;
    line-height: 1.2;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    padding-top: 10px;
    text-align: left;
    margin-bottom: 5px;
    font-size: 0.9rem;
    line-height: 1.2;
    color: var(--dark);
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    margin-top: 0.25rem;
    align-items: center;
    order: 5;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-like {
    color: var(--dark);
}
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-like:before {
    content: '\f004';
    font-family: 'font awesome 5 pro';
    font-weight: 300;
    font-size: 1.33333em;
    line-height: 0.75em;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-dislike {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    width: 100%;
    margin-bottom: 0.75rem;
    order: 2;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    font-size: 1.2rem;
    font-weight: 500;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    text-decoration: line-through;
    color: var(--gray);
    font-size: 0.9rem;
    margin-left: 3.5px;
    order: 2;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: -30px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: -30px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    color: #616667;
    width: 24px;
    height: 24px;
    content: '';
    display: block;
    position: absolute;
    top: 12px;
    left: 12px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-left-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(14.000000, 14.000000) rotate(-180.000000) translate(-14.000000, -14.000000) translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group' transform='translate(0.000000, -0.000000)'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-right-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--gray);
    line-height: 1.2;
    order: 3;
}

.garderobo-widget-product-sale-badge {
    display: inline-block;
    position: absolute;
    top: 0.5rem;
    padding: 0.4em 0.4em 0.35em 0.4em;
    font-size: 75%;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    color: #fff;
    background-color: var(--alert);
}

.garderobo-widget-sizes-list {
    display: block;
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    margin-top: 0.25rem;
    order: 4;
}

.garderobo-widget-sizes-list-item {
    display: flex;
    justify-content: center !important;
    align-items: center !important;
    min-width: 25px;
    padding: 0 4px;
    color: var(--gray);
    border: 1px solid #dee2e6;
    font-size: 0.9rem;
    margin: 0 0.25rem 0.25rem 0;
}

.garderobo-widget-add-to-cart-no-size-btn {
    display: inline-block;
    font-weight: 400;
    color: var(--dark);
    text-align: center;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent;
    border: 1px solid var(--dark);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    text-decoration: none;
    margin-right: 1rem;
}
.garderobo-widget-add-to-cart-no-size-btn:hover {
    color: #fff;
    background-color: var(--dark);
    border-color: var(--dark);
}

.garderobo-widget-product-control-dislike {
    display: none;
}

/** with avatar */
.garderobo-widget-feed-items.garderobo-widget-feed-items--luxxy-user {
    padding-top: 10px;
}
.garderobo-widget-container .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user {
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.1), 0px 4px 10px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    overflow: hidden;
}
.garderobo-widget-container .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user .garderobo-widget-product-footer {
    box-sizing: border-box;
    padding: 25px 20px 40px;
}
.garderobo-widget-container .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user .garderobo-widget-product-footer .garderobo-widget-product-name {
    padding-top: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
}
.garderobo-widget-container
    .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user
    .garderobo-widget-product-footer
    .garderobo-widget-product-price-container {
    margin-bottom: 0;
}
.garderobo-widget-container .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user .garderobo-widget-sizes-list {
    order: 1;
    margin-bottom: 15px;
}
.garderobo-widget-container .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user .garderobo-widget-product-sale-badge,
.garderobo-widget-container .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user .garderobo-widget-product-control-container,
.garderobo-widget-container .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: none;
}
.garderobo-widget-container
    .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user
    .garderobo-widget-product-footer
    .garderobo-widget-product-price-container {
    align-items: center;
}

.garderobo-widget-container .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user .garderobo-widget-product-image {
    height: auto;
    width: 100%;
    padding-top: 110%;
    max-width: unset;
    background-size: contain;
}
.garderobo-widget-avatar-block {
    padding-top: 36px;
    margin: 0 18px 16px;
    display: flex;
    width: calc(100% - 2 * 18px);
    color: #000;
    text-decoration: none;
}
.garderobo-widget-avatar-data-block {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: calc(100% - 58px - 10px);
}
.garderobo-widget-avatar-pic-container {
    width: 58px;
    height: 58px;
    background: linear-gradient(45deg, #0066dd 0%, #0c9393 51.5%, #00e3cc 100%);
    border-radius: 50%;
    padding: 4px;
    box-sizing: border-box;
    margin-right: 12px;
    position: relative;
}
.garderobo-widget-avatar-pic {
    background-color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-repeat: no-repeat;
    background-size: 100%;
}

.garderobo-widget-avatar-pic-star {
    position: absolute;
    top: -15px;
    right: -41px;
}

.garderobo-widget-avatar-name {
    margin-bottom: 0px;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
}

.garderobo-widget-avatar-stars {
    display: flex;
    align-items: center;
}

.garderobo-widget-avatar-stars-text {
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-left: 5px;
}

@media screen and (min-width: 540px) {
    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user {
        width: calc(50% - 10px);
        margin: 0 5px 20px;
    }
}

@media screen and (min-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33.33%;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item:nth-child(n) {
        width: calc(33.33% - 2 * 20px / 3);
        margin: 0 20px 20px 0;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item:nth-child(3n) {
        margin-right: 0;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user:nth-child(n) {
        width: calc(33.33% - 2 * 30px / 3);
        margin: 0 10px 20px;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user:nth-child(3n) {
        margin-right: 10px;
    }
}

@media screen and (min-width: 992px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 25%;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item:nth-child(n) {
        width: calc(25% - 3 * 20px / 4);
        margin: 0 20px 20px 0;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item:nth-child(4n) {
        margin-right: 0;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user:nth-child(n) {
        width: calc(25% - 3 * 27px / 4);
        margin: 0 10px 20px;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user:nth-child(4n) {
        margin-right: 10px;
    }
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item:nth-child(n) {
        width: calc(25% - 20px);
        margin: 0 10px 20px 10px;
    }
}

@media screen and (min-width: 1140px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 20%;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item:nth-child(n) {
        width: calc(20% - 20px);
    }
}

@media screen and (min-width: 1280px) {
    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user {
        width: 20%;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container--mobile
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item.garderobo-widget-feed-item--luxxy-user:nth-child(n) {
        width: calc(20% - 20px);
    }
}
