.garderobo-widget-container {
    font-family: Montserrat, Helvetica, sans-serif;
}

.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 20px;
    flex: 1;
    align-self: center;
    height: 100%;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 24px;
    font-weight: 600;
    color: #000;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 10px;
    display: flex;
    background: unset;
    align-items: center;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
    align-items: center;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    text-indent: -99999px;
    display: block;
    width: 13px;
    height: 22px;
    transform: translate(0, -50%);
    cursor: pointer;
    color: transparent;
    border: 0;
    outline: 0;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAsBAMAAABMNS/cAAAAMFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABHcEx/v/MDAAAAEHRSTlP/+1dHd19n94T1o09tQAMAS5BP0wAAAKRJREFUGNNjeKX/Hwg+MCwQhdCBjPFg+pMAG5j+r8iQD6Y/CfCA6f8bGfzB9OcCFjD935zBH0x/nsABpv/fZGhApmHiUHUwfVBzYOZC7YHZC3MHzF0wd4LI/1oPwPRXgQVgOpAxAER/YxACyycw6oPoPwxCYPUOjPtB9BeGYrB+B3Z7EP2DYTLYvAbO+yg0TBymDq4PZg7cXJg9cHth7oC7C+hOAJlK4MmSzflFAAAAAElFTkSuQmCC);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    height: 100%;
}

.garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    width: 32%;
    padding-bottom: 33.33%;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}

/* STANDART LIST */
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 25%;
    padding: 0;
    margin: 0;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    width: 100%;
    aspect-ratio: 1/1;
    padding: 20px 10px 0;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-y: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: auto;
    flex-direction: column;
    padding: 10px 10px 20px 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: none;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    margin: 5px 0 !important;
    line-height: 16px;
    font-size: 12px;
    color: #241f1f;
    text-align: center;
    border: none;
    text-transform: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item:hover {
    border: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    font-weight: bold;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price, .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    padding: 0 4px;
    color: #241f1f;
    font-size: 12px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    font-size: 10px;
    padding: 0 2px 0 5px;
    order: 2;
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
}

.garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1 / 1;
    width: 86%;
    max-width: 100%;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 0;
    width: initial;
    height: 100%;
    max-width: 400px;
    height: auto;
}

/* COLLAGES POPUP */
.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    height: 100%;
    margin: 0;
    border: none;
    border-right: 1px solid #E0E0E0;
    align-self: unset;
}

.garderobo-widget-popup__btn-close {
    padding: 8px;
    right: 20px;
}

.garderobo-widget-popup-content {
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
    padding: 0;
    border-right: none;
}

.garderobo-widget-popup .garderobo-widget-feed {
    border: none;
    align-self: unset;
    margin-top: 0;
    margin-right: 0;
    height: 100%;
    flex: 1;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    height: unset !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-bottom: 0 !important;
}

.garderobo-widget-popup-list-header {
    display: none;
}

.garderobo-widget-popup-list-content {
    height: calc(100% - 24px);
    margin: 0;
    border: none;
    padding: 0 40px;
    margin-left: 20px !important;
    border-left: 1px solid #e0e0e0 !important;
}

.garderobo-widget-popup-list-item:first-child {
    border-top: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 40px 0;
    width: unset;
    gap: 24px;
    border-top: 1px solid #e0e0e0;
    margin-bottom: 10px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
    border-bottom: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 300px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-content .garderobo-widget-feed-header {
    display: none !important;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
}

.garderobo-widget-popup-list-item-like-button, .garderobo-widget-popup-list-swap-item-like-button {
    width: 20px;
    height: 17px;
    position: absolute;
    right: 0;
    top: 16px;
    cursor: pointer;
    background: url(https://platform-static.modatech.ru/like-bordered.svg) no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
    z-index: 1;
}

.garderobo-widget-popup-list-item-text-brand {
    display: none;
    margin: 0 !important;
    font-weight: 500 !important;
    text-transform: uppercase;
    font-size: 20px;
    line-height: 28px;
}

.garderobo-widget-popup-list-item-text-title {
    font-family: Montserrat;
    font-size: 18px;
    font-weight: 400;
    line-height: 21.94px;
    text-align: left;
    margin: 0;
    width: 90%;
    color: #292929;
}

.garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 24px;
    padding-top: 0;
    font-size: 15px;
    font-weight: 600;
    color: #292929;
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-sizes {
    width: 100%;
    font-weight: 400;
    height: 40px;
    position: relative;
    margin-bottom: 10px;
    font-family: Montserrat !important;
    font-size: 12px;
    line-height: 16px;
    text-align: left;
    text-transform: uppercase;
    padding: 6px 10px 6px 34px !important;
    position: relative;
    color: #002D18;
    border-color: #002D18;
}

.garderobo-widget-sizes {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-color: transparent;
    padding: 8px 20px 8px 8px; /* Регулируем отступы справа */
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40'%3E%3Cpath d='m24.4 20.4-7.9 10c-.1.2-.3.2-.5.2-.1 0-.3 0-.4-.1-.3-.2-.3-.6-.1-.9L23 20l-7.5-9.6c-.2-.3-.2-.7.1-.9s.7-.2.9.1l7.9 10c.2.2.2.6 0 .8'/%3E%3C/svg%3E");
    background-position: right 10px center;
    background-repeat: no-repeat;
    background-size: 10%;
}


.garderobo-widget-popup-actions-controls button {
    width: 100%;
    padding: 12px 0;
    text-transform: uppercase;
    font-weight: 400;
    margin: 0;
    text-align: center;
    cursor: pointer;
    border-radius: 0;
    font-family: Montserrat !important;
    font-size: 12px;
    line-height: 16px;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    background-color: #105A33;
    border-color: transparent;
    color: #fff;
    margin-bottom: 10px !important;
    height: 45px;
    transition: all .35s;
}

.garderobo-widget-popup-list-item-text-cart-btn:hover {
    background-color: #004927 !important;
}

.garderobo-widget-popup-action-buttons button {
    height: 40px;
    padding: 0;
}

.garderobo-widget-popup-list-item-text-cart-btn.garderobo-widget-popup-list-item-text-cart-btn--disabled {
    background-color: #002D18;
    opacity: unset;
}

.garderobo-widget-popup-list-item-swap-button {
    display: block !important;
    border: 1px solid #002D18;
    background-color: #295937;
    color: #fff;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

/* SWAP CONTAINER */

.garderobo-widget-popup-list-item-swap-container {
    padding-bottom: 8px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 170px;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    display: none;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: relative;
    height: 80%;
    width: 100%;
    border: none !important;
    justify-content: start;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
    padding: 10px;
}

.garderobo-widget-popup-list-item-swap-item-img {
    max-height: 160px;
}

.garderobo-widget-popup-list-item-name {
    width: 100%;
    margin: 0 !important;
    padding: 0;
    min-height: unset;
    font-size: 10px;
    font-weight: 300 !important;
    line-height: 14px;
    letter-spacing: 0.5px;
    text-align: center;
    margin-top: 10px !important;
    display: block !important;
    max-height: 65px;
    height: 70px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    margin-left: -1px;
    margin-top: -1px;
    padding: 2px 4px;
    font-size: 12px;
    line-height: 14px;
    border: 1px solid #ccc;
}

.garderobo-widget-popup-list-item-swap-item-price {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    height: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-scroll-button {
    align-items: center;
    background: #fff !important;
    border: 1px solid #e5e5e5;
    border-radius: 0;
    bottom: 30px;
    color: #002d18;
    cursor: pointer;
    display: flex;
    font-family: Montserrat, Helvetica, sans-serif !important;
    font-size: 12px;
    font-weight: 700;
    justify-content: center;
    line-height: 25px;
    margin: 0;
    padding: 5px 20px;
    position: absolute;
    -webkit-text-decoration: none;
    text-decoration: none;
    text-transform: uppercase;
    transition: all .35s;
    z-index: 1;
    left: unset;
    top: unset;
    width: unset;
    height: unset;
    right: calc(30px + 5vw);
    border-top: 1px solid;
    border: 1px solid #002d18;
    border-left: none;
    border-right: none;
}

.garderobo-scroll-button:hover {
    background: #f0f0f0 !important;
}

.garderobo-scroll-button span {
    display: none !important;
}

.modatech-platform-look-like-state-button:hover, .garderobo-widget-popup-list-swap-item-like-button:hover, .modatech-platform-look-like-state-button-liked, .garderobo-like-button-liked-state, .garderobo-widget-popup-list-item-like-button:hover {
    background: url('https://testplatform-static.modatech.ru/like-filled.svg') no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
    background-position: 0 -22px;
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
    display: none;
}

.garderobo-widget-popup-content .garderobo-widget-feed .garderobo-widget-control-left, .garderobo-widget-popup-content .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
}

.garderobo-widget-feed-items-counter-btn--next {
    margin-left: 6px;
}

.garderobo-widget-feed-items-counter-btn--next::before {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40'%3E%3Cpath d='m24.4 20.4-7.9 10c-.1.2-.3.2-.5.2-.1 0-.3 0-.4-.1-.3-.2-.3-.6-.1-.9L23 20l-7.5-9.6c-.2-.3-.2-.7.1-.9s.7-.2.9.1l7.9 10c.2.2.2.6 0 .8'/%3E%3C/svg%3E");
    background-size: cover;
    transform: unset;
    content: "";
    display: block;
    height: 40px;
    width: 40px;
}

.garderobo-widget-feed-items-counter-btn--prev::before {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40'%3E%3Cpath d='m24.4 20.4-7.9 10c-.1.2-.3.2-.5.2-.1 0-.3 0-.4-.1-.3-.2-.3-.6-.1-.9L23 20l-7.5-9.6c-.2-.3-.2-.7.1-.9s.7-.2.9.1l7.9 10c.2.2.2.6 0 .8'/%3E%3C/svg%3E");
    background-size: cover;
    transform: scale(-1);
    content: "";
    display: block;
    height: 40px;
    width: 40px;
}

.garderobo-widget-feed-items-counter-text {
    display: none;
}

.garderobo-widget-look__btn-buy {
    align-items: center;
    background: #295937;
    border: 1px solid #002D18;
    border-radius: 0;
    color: #fff;
    cursor: pointer;
    display: flex;
    font-family: Montserrat, Helvetica, sans-serif !important;
    font-size: 12px;
    height: 40px;
    justify-content: center;
    line-height: 16px;
    padding: 0 30px;
    -webkit-text-decoration: none;
    text-decoration: none;
    text-transform: uppercase;
    transition: all .35s;
    /* margin-top: 20px; */
    bottom: -70px;
    font-weight: 400;
    transition: all .35s;
}

.garderobo-widget-look__btn-buy:hover {
    background: #002D18 !important;
    border: 1px solid #002D18 !important;
    color: #fff !important;
}

.garderobo-widget-popup-container .garderobo-widget-sizes {
    border-color: #002D18;
    border: none;
    border-top: 1px solid #002D18;
    border-bottom: 1px solid #002D18;
    color: #002D18;
    font-family: Montserrat !important;
    margin-right: 0;
}

.empty-div {
    height: calc(100vh - 130px);
}

.gw-inline {
    padding: 50px 0;
    font-family: Montserrat, Helvetica, sans-serif;
}

.gw-products {
    height: 100%;
}

.gw-button {
    border-radius: 0;
    height: 40px;
    font-size: 12px;
    font-family: Montserrat !important;
    cursor: pointer;
}

.gw-button-add-cart {
    background-color: #105A33;
    border-color: transparent;
    transition: all .35s;
    padding: 0;
}

.gw-button-change-product {
    color: #fff;
}

.gw-button-add-cart-disabled {
    background-color: #002D18;
    opacity: unset;
}

.gw-button-add-cart:hover {
    background-color: #004927 !important;
}

.gw-button-add-favorites.garderobo-like-button-liked-state {
    background: #F2F3F6;
}

.gw-select-size {
    border: none;
    border-radius: 0;
    border-color: #002D18;
    border-top: 1px solid #002D18;
    border-bottom: 1px solid #002D18;
    color: #002D18;
    font-family: Montserrat !important;
    margin-right: 0;
    margin-bottom: 10px;
    margin-top: 10px;
    height: 40px;
    line-height: 16px;
    text-align: left;
    font-weight: 400;
    padding: 6px 10px 6px 34px !important;
    position: relative;
    font-size: 12px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-color: transparent;
    padding: 8px 20px 8px 8px;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40'%3E%3Cpath d='m24.4 20.4-7.9 10c-.1.2-.3.2-.5.2-.1 0-.3 0-.4-.1-.3-.2-.3-.6-.1-.9L23 20l-7.5-9.6c-.2-.3-.2-.7.1-.9s.7-.2.9.1l7.9 10c.2.2.2.6 0 .8'/%3E%3C/svg%3E");
    background-position: right 10px center;
    background-repeat: no-repeat;
    background-size: 10%;
}

.gw-select-size:focus-within {
    outline: none;
}

.gw-slider-thumbnail {
    height: auto;
}

.gw-slider-thumbnail .gw-slider-thumbnail-item {
    height: 84px;
}

.gw-product-item-info h2 {
    font-family: Montserrat;
    font-size: 18px;
    font-weight: 400;
    line-height: 21.94px;
    text-align: left;
    margin: 0;
    width: 90%;
    color: #292929;
    text-transform: none;
}

.gw-product-item-price-block {
    font-size: 15px;
    line-height: 21px;
    margin: 10px 0 6px;
}

.gw-product-item-error {
    width: 100%;
    color: #ff3333;
    font: 12px 'FFDIN Regular', 'FFDIN', Arial, sans-serif;
    margin-top: 5px;
}

.gw-product-item-info {
    gap: 0;
}

.gw-product-item-show-more,
.gw-button-change-product {
    font-size: 15px;
    line-height: 21px;
    font-weight: 400;
    color: #767676;
    cursor: pointer;
    text-transform: lowercase;
}

.gw-button-change-product {
    background: transparent;
    border: none;
}

.gw-button-change-product span {
    height: 16px;
}

.gw-products-list-item img {
    width: 130px;
    height: 115px;
}

.gw-products-list-item .gw-product-item-price {
    font-size: 15px;
}

.gw-left-block {
    height: fit-content;
}

.gw-button-slide-next,
.gw-button-slide-prev {
    background-size: cover;
    background-color: transparent;
    content: "";
    display: block;
    height: 40px;
    width: 40px;
}

.gw-button-slide-next svg,
.gw-button-slide-prev svg {
    display: none;
}

.gw-button-slide-next {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40'%3E%3Cpath d='m24.4 20.4-7.9 10c-.1.2-.3.2-.5.2-.1 0-.3 0-.4-.1-.3-.2-.3-.6-.1-.9L23 20l-7.5-9.6c-.2-.3-.2-.7.1-.9s.7-.2.9.1l7.9 10c.2.2.2.6 0 .8'/%3E%3C/svg%3E");
    transform: unset;
}

.gw-button-slide-prev {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40'%3E%3Cpath d='m24.4 20.4-7.9 10c-.1.2-.3.2-.5.2-.1 0-.3 0-.4-.1-.3-.2-.3-.6-.1-.9L23 20l-7.5-9.6c-.2-.3-.2-.7.1-.9s.7-.2.9.1l7.9 10c.2.2.2.6 0 .8'/%3E%3C/svg%3E");
    transform: scale(-1);
}

.gw-button-add-favorites {
    padding: 0;
}

.nl-container-main .garderobo-widget-feed-header {
    color: #002d18 !important;
    font-family: inherit;
    font-family: Montserrat, Helvetica, sans-serif;
    font-size: 32px !important;
    font-weight: 500 !important;
    line-height: 1;
    margin: 0;
    text-transform: uppercase;
}

/* DESKTOP */
@media screen and (min-width: 1280px) {

    .garderobo-widget-popup {
        height: 80vh;
        padding: 20px;
        max-width: 1300px;
        max-height: 1000px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 50%;
        padding-top: 20px;
        height: calc(100% + 20px);
    }

    .garderobo-widget-popup-content .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
        width: 100% !important;
        gap: 0 !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        margin: 10px !important;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(25% + 13px);
        padding-right: 50px;
    }

    .garderobo-widget-feed-item-look .garderobo-widget-feed-items {
        gap: 0 !important;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        font-size: 13px;
        font-weight: 500;
        align-self: flex-start;
        text-align: left;
        line-height: 23px;
    }

    .garderobo-widget-product-price {
        padding: 0 !important;
    }

    .garderobo-widget-product-price-container {
        align-self: flex-start;
        font-size: 13px !important;
        font-weight: 600 !important;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
        padding: 10px 0;
    }

    .garderobo-scroll-button {
        right: 30px;
    }
}


@media screen and (min-width: 768px) and (max-width: 1279px) {
    .garderobo-widget-popup {
        height: 80vh;
        padding: 20px;
        max-width: 1300px;
        max-height: 1000px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 50%;
        padding-top: 20px;
        height: calc(100% + 20px);
    }

    .garderobo-widget-popup-content .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
        width: 100%;
        gap: 0 !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        margin: 10px !important;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(33.33% + 11px);
        padding-right: 32px;
    }

    .garderobo-widget-feed-item-look .garderobo-widget-feed-items {
        gap: 0 !important;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        font-size: 13px;
        font-weight: 500;
        align-self: flex-start;
        text-align: left;
        line-height: 23px;
    }

    .garderobo-widget-product-price {
        padding: 0 !important;
    }

    .garderobo-widget-product-price-container {
        align-self: flex-start;
        font-size: 13px !important;
        font-weight: 600 !important;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
        padding: 10px 0;
    }

    .empty-div {
        height: calc(100vh - 110px);
    }

    .gw-slider {
        height: auto;
        margin: 0 40px;
        aspect-ratio: 1/1;
    }

    .gw-slider-item {
        height: 100% !important;
    }

    .gw-inline {
        gap: 20px;
        padding-inline: 30px;
    }

    .gw-slider-thumbnail-item .gw-slider-item {
        height: 100%;
    }

    .gw-left-block, .gw-products {
        padding-inline: 0;
    }

    .gw-products {
        padding-right: 10px;
    }
}

/* MOBILE */
@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed {
        margin-bottom: 0;
        justify-content: center;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed {
        border: none !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding: 12px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
        width: 100%;
    }

    .garderobo-widget-feed-item-look .garderobo-widget-feed-items {
        gap: 0 !important;
    }

    .garderobo-widget-popup-list-item-text-title {
        font-size: 16px;
        font-weight: 400;
        line-height: 19px;
    }
    .garderobo-widget-feed-item-look .garderobo-widget-feed-items {
        gap: 0 !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
        gap: 0 !important;
    }

    .garderobo-widget-popup-list-content {
        padding: 12px;
        margin: 0 !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: 140px !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed-header {
        display: flex !important;
        align-items: center;
        height: 20px;
        margin-top: 40px;
        position: unset;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        align-items: center;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        font-size: 13px;
        font-weight: 500;
        align-self: flex-start;
        text-align: left;
        line-height: 23px;
    }

    .garderobo-widget-product-price {
        padding: 0 !important;
    }

    .garderobo-widget-product-price-container {
        align-self: flex-start;
        font-size: 13px !important;
        font-weight: 600 !important;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
        padding: 10px 0;
    }

    .garderobo-scroll-button {
        right: 30px;
        bottom: 50px;
    }

    .garderobo-widget-popup-actions-controls button {
        font-size: 12px;
    }

    .garderobo-widget-sizes {
        font-size: 12px !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed .garderobo-widget-control-left, .garderobo-widget-popup-content .garderobo-widget-feed .garderobo-widget-control-right {
        display: none !important;
    }

    .garderobo-widget-sizes {
        padding-left: 10px !important;
    }

    .empty-div {
        height: auto;
    }

    .gw-inline {
        padding: 50px 16px;
    }

    .gw-product-item-info h2 {
        font-size: 16px;
    }

    .gw-button-slide-prev {
        left: 0;
    }

    .gw-button-slide-next {
        right: 0;
    }

    .nl-container-main .garderobo-widget-feed-header {
        font-size: 28px !important;
    }
}

@media screen and (max-width: 500px) {
    .gw-slider {
        aspect-ratio: 1/1;
        width: calc(100% - 50px) !important;
        margin: 0 auto;
    }

    .gw-product-item .gw-product-item-img-block img {
        width: 126px !important;
    }

    .gw-select-size {
        padding: 0 10px !important;
    }

    .garderobo-widget-look-container {
        width: 100%;
    }
}

.garderobo-widget-popup-list-content {
    border: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    border: none !important;
}

/* widgets in popup */
#modatech-widgets-in-popup {
    width: 100%;
}

#modatech-widgets-in-popup .garderobo-widget-control-left.garderobo-widget-hidden,
#modatech-widgets-in-popup .garderobo-widget-control-right.garderobo-widget-hidden {
    display: block !important;
}

#modatech-widgets-in-popup .garderobo-widget-feed-items {
    width: 100% !important;
}

#modatech-widgets-in-popup .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    width: 100% !important;
    padding-bottom: 100% !important;
}

#modatech-widgets-in-popup .garderobo-widget-feed-header {
    position: relative;
}

#modatech-widgets-in-popup .garderobo-widget-feed-items-counter-btn--prev,
#modatech-widgets-in-popup .garderobo-widget-feed-items-counter-btn--next {
    display: none !important;
}

#modatech-widgets-in-popup .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
#modatech-widgets-in-popup .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
}

#modatech-widgets-in-popup .garderobo-widget-feed-item-look .garderobo-widget-feed-items {
    gap: 0 !important
}