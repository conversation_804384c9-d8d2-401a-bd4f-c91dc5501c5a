/* choupette */
.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 20px;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 30px;
    font-weight: 400;
    color: #000000;
    white-space: nowrap;
    text-align: left;
    margin-bottom: 10px;
    margin-top: 10px;
    border-bottom: 1px solid #000;
    padding-bottom: 5px;
    width: calc(100% - 75px);
}

.garderobo-widget-popup__btn-close {
    background-color: unset !important;
    border: unset !important;
    height: 30px !important;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    height: 30px !important;
}

.garderobo-widget-popup-list-item-text-title {
    text-transform: uppercase;
}

.garderobo-widget-popup-list-item-swap-container-item {
    margin-left: 0 !important;
}

.garderobo-widget-popup-list-item-swap-container-item:before {
    background: none !important;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: calc(100% - 75px);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 25%;
    padding: 10px;
    border: 1px solid white;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner:hover {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.garderobo-multiple-controls {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    padding: 10px;
    height: 100%;
    align-content: flex-start;
}

.garderobo-widget-product-sale-badge {
    display: block;
    position: absolute;
    background: url(https://my-choupette.ru/bitrix/templates/new_my-choupette/img/bg-discount.png) repeat-x;
    color: #fff;
    font-size: 15px;
    min-width: 40px;
    line-height: 12px;
    padding: 14px 1px 14px 3px;
    top: 10px;
    left: 20px;
    z-index: 1;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header-content {
    margin: 0 4.5% 10px;
    padding: 5px;
    border-bottom: 2px #ccc solid;
    text-align: center;
    font-size: 14px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 400px;
    width: 100%;
    padding: 0;
    border-bottom: none;
    background-size: cover;
    background-repeat: no-repeat;
    background-position-y: 0;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: auto;
    flex-direction: column-reverse;
    padding: 0;
    padding-bottom: 10px;
    padding-top: 7px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: none;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    order: 2;
    color: #8d7249;
    font-size: 12px;
}



.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    margin: 0 !important;
    font-size: 15px;
    color: #78767b;
    text-align: left;
    width: 100%;
    border: none;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    display: none;
    /*width: 100%;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-like,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-dislike {
    /*width: 50%;*/
    /*height: 40px;*/
    /*display: inline-block;*/
    /*background-color: gray;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    order: 3;
    width: 100%;
    text-align: left;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    padding: 0;
    color: #131415;
    font-size: 18px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price+.garderobo-widget-product-price {
    font-size: 18px;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    font-size: 18px;
    padding-left: 25px;
    order: 2;
    color: #ef5749;
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    order: 3;
    text-decoration: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    cursor: pointer;
    color: #62563b;
    font-size: 50px;
    opacity: 0.5;
    font-family: 'FontAwesome';
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:hover,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:hover {
    opacity: 0.75;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    content: '\f104';
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    content: '\f105';
}

.garderobo-widget-feed-item-look {
    margin-top: 15px;
}

.garderobo-widget-look-product--left-top.garderobo-widget-look-product--layer-1_top,
.garderobo-widget-look-product--right-top.garderobo-widget-look-product--layer-1_top {
    height: 30%;
    width: 25%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
    justify-content: left;
}

@media screen and (min-width: 1600px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 20%;
        padding: 0;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 430px;
    }
}

@media screen and (max-width: 1280px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33%;
        padding: 5px;
    }
}

@media screen and (max-width: 980px) {
    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 300px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        padding: 0;
    }
}

@media screen and (max-width: 680px) {
    .garderobo-widget-container .garderobo-widget-feed {
        margin-bottom: 0;
    }

    .garderobo-widget-container .garderobo-widget-feed {
        justify-content: center;
    }

    .garderobo-widget-feed-header {
        border: none !important;
        font-size: 18px !important;
        line-height: 21px !important;
        color: #7d7d7d !important;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
        font-size: 14px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
        padding-left: 20px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        font-size: 12px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 225px;
        width: 100%;
        max-width: 240px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        line-height: 13px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
        flex-wrap: wrap;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
        text-transform: none;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    }

    .garderobo-widget-popup__btn-close {
        top: 115px;
    }

    .garderobo-widget-popup-collage-container {
        padding-top: 125px;
    }

    .garderobo-widget-popup-list-item-img-wrapper {
        height: 100px;
    }

    .garderobo-widget-popup-list-item-pic {
        margin-left: 40px;
    }

    .garderobo-widget-feed-looks .garderobo-widget-feed-items {
        flex-wrap: wrap !important;
    }

    .garderobo-widget-feed-looks .garderobo-widget-feed .garderobo-widget-feed-container {
        overflow: unset !important;
    }

    .garderobo-widget-feed-looks .garderobo-widget-feed-item_inner {
        height: unset !important;
    }

    .garderobo-widget-feed-looks .garderobo-widget-control-right, .garderobo-widget-feed-looks .garderobo-widget-control-left {
        display: none !important;
    }
}

@media screen and (max-width: 465px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 190px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price+.garderobo-widget-product-price {
        font-size: 14px;
    }

    .garderobo-widget-product-price {
        font-size: 14px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
        font-size: 14px;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        margin-bottom: 0;
    }
}

.garderobo-widget-look-product--shoes {
    height: 20%;
}

.garderobo-widget-look-product--bag {
    width: 21%;
    height: 21%;
}

.garderobo-widget-look-product--left-center-bag {
    top: 54%;
}

.garderobo-widget-look-product--gloves {
    width: 12%;
    height: 20%;
    top: 30%;
    background-position-y: bottom;
}

/* CHILDS */

.garderobo-childs .garderobo-widget-look-product--center-hat {
    left: 42%;
}

.garderobo-childs .garderobo-widget-look-product--hat {
    width: 18%;
}

.garderobo-childs .garderobo-widget-look-product--center-bottom {
    width: 30% !important;
}

.garderobo-childs .garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 30% !important;
}

.garderobo-childs .garderobo-widget-look-product--right-top-3 {
    left: auto !important;
    right: 0 !important;
    top: 31% !important;
    z-index: 10 !important;
}

.garderobo-childs .garderobo-widget-look-product--belt-2 {
    top: 43%;
}

.garderobo-childs .garderobo-widget-look-product--socks {
    width: 13% !important;
    height: 20% !important;
    bottom: 23% !important;
    display: block !important;
}

.garderobo-childs .garderobo-widget-look-product--accessory-head {
    width: 14%;
    height: 14%;
    top: 2%;
    z-index: 12 !important;
}

.garderobo-childs .garderobo-widget-look-product--accessory-head-2 {
    /*left: 25%;*/
}

.garderobo-childs .garderobo-widget-look-product--accessory-glasses {
    width: 14%;
    height: 14%;
    right: 2%;
    z-index: 12 !important;
}

.garderobo-childs .garderobo-widget-look-product--accessory-glasses-2 {
    right: 25% !important;
}

.garderobo-childs .garderobo-widget-look-product--right-gloves {
    right: 24% !important;
    top: 53% !important;
    z-index: 12 !important;
}

.garderobo-childs .garderobo-widget-look-product--shoes {
    left: 12% !important;
    width: 24% !important;
}

.garderobo-childs .garderobo-widget-look-product--bag {
    width: 25%;
    height: 25%;
    right: 6%;
}

#garderobo-additional-recommended header {
    border-bottom: 1px solid #d9d9d9;
    padding: 0 0 7px !important;
    font-size: 16px !important;
    margin-top: 16px !important;
    margin-bottom: 16px !important;
    font-weight: normal;
    text-align: left;
}

#garderobo-additional-recommended .garderobo-widget-feed-item {
    width: 25% !important;
}

#garderobo-additional-recommended .garderobo-widget-product-footer {
    display: none !important;
}

#garderobo-additional-recommended .garderobo-widget-product-image {
    height: 115px !important;
}

#garderobo-additional-recommended .garderobo-widget-control-left,
#garderobo-additional-recommended .garderobo-widget-control-right {
    margin-top: 50px !important;
}

#garderobo-additional-recommended .garderobo-widget-feed {
    margin-bottom: -16px !important;
}

/* Grid Look */
.garderobo-grid-look__gloves,
.garderobo-grid-look__socks {
    display: block;
}

.garderobo-grid-look__gloves_head {
    grid-row: 10 / 14;
}

.garderobo-grid-look__gloves_ring {
    grid-row: 13 / 14;
    grid-column: 18 / 21;
}

.garderobo-grid-look__socks {
    grid-row: 13 / 17;
    grid-column: 5 / 8;
}

.garderobo-grid-look__gloves_glass {
    grid-column: 14 / 17 !important;
}

.garderobo-grid-look__hat {
    grid-row: 1 / 4;
    grid-column: 9 / 12;
}

.garderobo-grid-look__layer_count_2 .garderobo-grid-look__hat {
    grid-row: 1 / 4;
    grid-column: 9 / 12;
}

.garderobo-grid-look__layer_count_3 .garderobo-grid-look__hat {
    grid-row: 1 / 4;
    grid-column: 1 / 4;
}

.garderobo-grid-look__shoes {
    grid-row: 17 / 21;
}

.garderobo-grid-look__bag {
    grid-row: 15 / 20;
}

.garderobo-grid-look__gloves {
    grid-row: 11 / 14;
    grid-column: 18 / 21;
}

.garderobo-grid-look__layer_count_3 .garderobo-grid-look__gloves_head {
    grid-row: 11 / 15;
}

.garderobo-grid-look__layer_count_0 .garderobo-grid-look__gloves_head,
.garderobo-grid-look__layer_count_1 .garderobo-grid-look__gloves_head {
    grid-row: 6 / 10;
    grid-column: 4 / 7;
}

@media screen and (max-width: 425px) {
    #garderobo-additional-recommended .garderobo-widget-feed-item {
        width: 25% !important;
    }

    #garderobo-additional-recommended header {
        font-size: 17px !important;
        border-bottom: none !important;
        margin-bottom: 0 !important;
        text-align: left;
        font-weight: normal;
    }

    #garderobo-additional-recommended .garderobo-widget-feed {
        margin-bottom: -14px !important;
    }

    #garderobo-additional-recommended .garderobo-widget-feed-container {
        width: calc(100% - 50px) !important;
    }

    #garderobo-additional-recommended .garderobo-widget-control-left,
    #garderobo-additional-recommended .garderobo-widget-control-right {
        margin-top: 30px !important;
    }
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
        width: 32% !important;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container--mobile .garderobo-widget-feed-item {
        margin: 0;
    }
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    /*border: 1px solid #eee !important;*/
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    border: none !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 0 !important;
}

.garderobo-widget-look-container {
    padding: 7px;
}

.garderobo-grid-look__product_img_with_positions {
    display: block !important;
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        border: none !important;
    }
}

.garderobo-grid-look-2 p {
    z-index: 50;
    text-transform: uppercase;
    line-height: 11px;
    font-size: 10px;
    background-color: #fff;
    display: flex;
    align-items: center; /* Выравнивание по вертикали по центру */
    justify-content: center;
    flex-wrap: wrap;
    gap: 0;
    text-align: center;
    opacity: 0.7;
    border-radius: 10px;
    padding: 12px;
}

.garderobo-grid-look-2 p strong {
    width: 100%;
}

.garderobo-main-block header {display: none;}
.garderobo-main-block .garderobo-widget-look__label-look-name {display: block !important;
    position: absolute;
    bottom: -40px;
    font-family: Roboto-medium;
    font-size: 18px;
}

.garderobo-main-block .garderobo-widget-look__label-look-link {display: block !important;
    position: absolute;
    transform: translate(-50%);
    left: 200px !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-container .garderobo-widget-feed header {
    margin-top: 0;
}

.garderobo-widget-popup .garderobo-widget-look__label-look-name, .garderobo-widget-popup .garderobo-widget-look__label-look-link {display: none !important;}
.garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {width: calc(100% - 100px);}
.garderobo-main-block .garderobo-widget-look__btn-buy, .garderobo-main-block .garderobo-widget-look__label-look-link {
    border: none; border-bottom: 1px solid #212529;
    font-weight: normal; padding: 0;
    left: 30px;
    font-size: 16px;
    letter-spacing: 2px;
    font-family: Roboto-light;
    text-transform: lowercase;
    padding-bottom: 4px;
    color: #212529;
    bottom: -70px;
}

.garderobo-widget-look__btn-buy {
    height: 40px !important;
    font-size: 12px !important;
    width: 120px;
    bottom: -70px;
}

.garderobo-main-block {height: unset;}
.garderobo-main-block .garderobo-widget-look__btn-buy:hover {background: none;}
.garderobo-main-block .garderobo-widget-popup-list-item-img, .garderobo-main-block .garderobo-widget-popup-list-item-swap-item-img {width: auto !important;}
.garderobo-main-block .garderobo-widget-feed {margin-bottom: 0;}
.garderobo-main-block .garderobo-widget-feed-item-look {margin-top: 0;}
.garderobo-main-block .garderobo-widget-feed-item-look .garderobo-widget-feed-item {padding-bottom: 98% !important; width: 100% !important;}

@media screen and (min-width: 1024px) {
    .garderobo-main-block .garderobo-widget-popup {
        max-width: 75%;
        min-height: 600px;
    }
}

@media screen and (max-width: 768px) {
    .garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-main-block .garderobo-widget-look__label-look-name {display: block !important;
        position: absolute;
        bottom: -40px;
        font-family: Roboto-regular;
        font-size: 16px;
    }

    .garderobo-main-block .garderobo-widget-look__btn-buy, .garderobo-main-block .garderobo-widget-look__label-look-link {
        left: 22px;
        font-size: 14px;
        font-family: Roboto-light;
        letter-spacing: unset;
    }

    .garderobo-main-block .garderobo-widget-look__label-look-link {display: block !important;
        left: 140px !important;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container--mobile .garderobo-widget-feed-item {
        margin: 0;
    }

    .garderobo-widget-popup-content .garderobo-widget-look__label-look-name, .garderobo-widget-popup-content .garderobo-widget-look__label-look-link {display: none !important;}
}

.garderobo-widget-popup-list-item-text-like-btn {
    height: 30px !important;
    min-width: 30px;
    max-width: 30px;
    border: 1px solid #f00;
    margin-top: 0;
    margin-right: 5px;
    position: relative;
    background-color: #fff !important;
}

.garderobo-widget-popup-list-item-text-like-btn:hover {
}

.garderobo-widget-popup-list-item-text-like-btn::before {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: url(https://cdn.danielonline.ru/local/templates/.default/img/icon_wishlist.svg) 50% no-repeat;
    background-size: 20px;
    content: '';
}

.garderobo-widget-popup-action-buttons {
    display: flex;
    flex-wrap: wrap;
}

.garderobo-widget-popup-list-item-text-bottom {
}

.garderobo-widget-popup-list-item-text-prices {
    width: 100%;
}

.garderobo-widget-popup-list-item-text-error {
    text-align: left !important;
}

.garderobo-widget-popup-list-item-text {
    width: 72%;
}

.garderobo-widget-popup-container .garderobo-widget-sizes {
    margin-right: 30px;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    margin-top: 0;
}

.garderobo-widget-popup-actions-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.garderobo-widget-look-container div[data-category-collage="socks"] {
    display: none;
}

.garderobo-widget-look-container div[data-category-collage="socks_tights"] {
    display: none;
}

/*CARDS*/
.garderobo-widget-look-container-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.garderobo-widget-look-container-cards__top-line {
    display: flex;
    flex: 1;
    gap: 10px;
}

.garderobo-widget-look-container-cards__bottom-line {
    display: flex;
    gap: 10px;
    width: 100%;
    justify-content: flex-start;
    height: 130px;
}

.garderobo-widget-look-container-cards__item {
    border: 1px solid #E1E1E1;
    width: 100%;
    background-size: cover;
    background-position: center;
    position: relative;
}

.garderobo-widget-look-container-cards__item__texts {
    position: absolute;
    left: 0;
    width: 100%;
    bottom: 0;
    height: 25%;
    background: linear-gradient(180deg, rgba(79, 79, 79, 0) 0%, #4F4F4F 100%);
    padding: 3px 5px;
    padding-top: 10px;
    font-size: 7px;
    color: #fff;
    display: flex;
    flex-wrap: wrap;
}

.garderobo-widget-look-container-cards__main-item .garderobo-widget-look-container-cards__item__texts {
    width: 100%;
    padding: 4px 16px;
    padding-top: 30px;
    height: 25%;
    font-size: 10px;
}


.garderobo-widget-look-container-cards__item__title, .garderobo-widget-look-container-cards__item__price {
    width: 100%;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 400;
}

.garderobo-widget-look-container-cards__item__price {
    font-size: 8px;
    font-weight: 700;
}

.garderobo-widget-look-container-cards__item__price-old-price {
    margin-left: 5px;
    text-decoration: line-through;
}

.garderobo-widget-look-container-cards__main-item .garderobo-widget-look-container-cards__item__price {
    font-size: 12px;
}

.garderobo-widget-look-container-cards__main-item  .garderobo-widget-look-container-cards__item__price-old-price {
    font-size: 10px;
}

.garderobo-widget-look-container-cards__main-item {
    flex: 66%;
    height: 280px;
}

.garderobo-widget-look-container-cards__right-col {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    flex: calc(33% - 5px);
}

.garderobo-widget-look-container-cards__bottom-line .garderobo-widget-look-container-cards__item {
    width: calc(33% - 5px);
}