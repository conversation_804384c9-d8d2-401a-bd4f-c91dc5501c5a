.garderobo-widget-container {
    --dark: #141414;
    --gray: #858585;
    --gray-light: #e5e5e5;
    --alert: #ff0000;
}

.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 40px;
}

.garderobo-widget-add-all-product-wrap {
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    flex-flow: row;
    align-items: flex-start;
    margin-bottom: 40px;
    position: relative;
}

.garderobo-widget-feed-container-new .garderobo-widget-brand-name {
    display: none;
}

.garderobo-widget-brand-name {
    /* display: block !important;
    margin-bottom: 5px;
    font-size: 1rem; */
}

.garderobo-widget-feed-container-new .garderobo-widget-feed-item:last-child {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed header {
    flex-shrink: 1;
    margin-inline: calc(1 * (100vw / 25));
    font-size: 22px;
    font-weight: bold;
    font-family: inherit;
    color: #292929;
    text-align: left;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.garderobo-widget-look__btn-buy {
    bottom: -40px;
    padding: 0;
    font-size: 12px;
    border: none;
    border-bottom: 1px solid #000;
    text-transform: none;
}

.garderobo-widget-feed-items-counter-text {
    font-size: 18px;
}

/* Grid Look */
.garderobo-grid-look__hat {
    grid-row: 1 / 4;
    grid-column: 9 / 13;
}

.garderobo-grid-look__accessory {
    grid-row: 11 / 14;
    grid-column: 17 / 20;
}

.garderobo-grid-look.garderobo-grid-look__layer_count_1 .garderobo-grid-look__layer-top-left,
.garderobo-grid-look.garderobo-grid-look__layer_count_2 .garderobo-grid-look__layer-top-right {
    padding-inline: 0;
}

.garderobo-grid-look__bottom_skirt {
    grid-row: 10 / 19;
    padding-inline: 5px;
    z-index: 4;
}

.garderobo-grid-look__gloves_glass {
    grid-row: 13 / 16;
    grid-column: 15 / 19;
}

.garderobo-grid-look.garderobo-grid-look__layer_count_1 .garderobo-grid-look__layer-top-left img {
    transform: scale(1) !important;
}

.garderobo-grid-look__layer-top-right {
    transform: scale(1) !important;
}

.garderobo-grid-look__belt {
    grid-row: 10 / 14;
    grid-column: 3 / 7;
}

.garderobo-grid-look__gloves {
    display: block;
    grid-row: 11 / 14;
    grid-column: 17 / 20;
}

.garderobo-grid-look__socks {
    display: block;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    max-width: 112px;
}

.garderobo-widget-popup-list-item-swap-container-item {
    max-width: 92px;
}

.garderobo-widget-feed-item-look {
    margin-inline: 15px;
}

@media only screen and (min-width: 768px) and (max-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 26px;
        font-family: inherit;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 26px;
        font-family: inherit;
    }
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
    align-self: center;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    height: auto;
    flex-direction: column;
    margin-left: calc(1 * (100vw / 25));
    border: 1px solid transparent;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    align-items: flex-start;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-image {
    width: 100%;
    padding-top: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    margin-bottom: 12px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-product-image {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer {
    flex-direction: column !important;
    align-items: flex-start !important;
    width: 100%;
    font-size: 0.9rem;
    line-height: 1.2;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-name,
.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-name {
    width: 90%;
    margin-bottom: 12px;
    font-size: 1rem;
    font-family: inherit;
    text-transform: capitalize;
    font-size: 13px;
    color: #292929;
    margin-bottom: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-name:hover {
    text-decoration: underline;
}

.garderobo-widget-brand-and-price {
    order: 2;
}

.garderobo-widget-sizes-and-cart-btn {
    order: 3;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn {
    font: 1.2rem 'FFDIN Medium', 'FFDIN', Arial, sans-serif;
    color: #fff;
    background-color: var(--alert);
    border: none;
    letter-spacing: 0.03em;
    padding: 1.3rem 1.6rem 1.4rem;
    text-transform: uppercase;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn:hover {
    background-color: #c00;
    color: #fff;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    margin-bottom: 0.75rem;
    gap: 6px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-price-container .garderobo-widget-product-price {
    font-family: 'rubl bold', 'FFDIN Medium';
    font-size: 1.5rem;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    position: relative;
    font-size: 13px;
    font-family: "Archivo", Arial, Helvetica, sans-serif;
    order: 2;
    color: #767676;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    font-size: 13px;
    font-family: "Archivo", Arial, Helvetica, sans-serif;
    order: 2;
    color: #333;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-old-price::before {
    position: absolute;
    transform: translateY(-50%);
    top: 50%;
    content: '';
    border-bottom: solid 1px #767676;
    backface-visibility: hidden;
    height: 0;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-price {
    font-family: "Archivo", Arial, Helvetica, sans-serif;
    font-size: 13px;
    font-weight: 700;
    color: #105a33;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
    opacity: .5;
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:hover,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:hover {
    opacity: 1;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    color: #616667;
    width: 16px;
    height: 16px;
    content: '';
    display: block;
    position: absolute;
    top: 17px;
    left: 17px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    left: 19px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-left-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(14.000000, 14.000000) rotate(-180.000000) translate(-14.000000, -14.000000) translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group' transform='translate(0.000000, -0.000000)'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    left: 15px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-right-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-product-sale-badge {
    display: none;
    position: absolute;
    top: 15px;
    left: 10px;
    padding: 0.4rem 0.5rem 0.5rem;
    font-size: 1.2rem;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    color: #fff;
    background-color: var(--alert);
}

.garderobo-widget-product-sale-badge::after {
    content: '';
    display: block;
    position: absolute;
    top: 2.1rem;
    left: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 1rem 1rem 0;
    border-color: transparen;
    border-right-color: var(--alert);
}

.garderobo-widget-product-control-dislike {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-sizes {
    background-color: #fff;
    border: solid 0.1rem #eee;
    box-shadow: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-sizes:hover {
    border-color: #eee;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-all-product-wrap {
    font-size: 1.5rem;
}

#btn-special-look {
    font-size: 12px;
}

@media screen and (min-width: 768px) {

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: initial !important;
    }
}

@media screen and (min-width: 992px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }
}

@media screen and (min-width: 1200px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
        width: calc(6 * (100vw / 25));
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 22%;
    left: 37%;
}

.garderobo-widget-popup-list-item-img {
    position: relative !important;
}

.garderobo-widget-popup-list-item-text-title {
    font-weight: 600;
}

.garderobo-widget-popup-list-item-swap-container-item::before {
    display: none;
}

.garderobo-widget-look-product--custom-left-top {
    z-index: 10;
    bottom: 45%;
}

.garderobo-widget-look-product--custom-right-top {
    bottom: 45%;
    background-position-y: bottom;
}

.garderobo-widget-look-product--custom-bottom {
    top: 45%;
    height: 50%;
}

.garderobo-widget-look-product--bag {
    width: 18%;
}

.garderobo-widget-look-product--center-bottom {
    width: 30% !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 30% !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--wear {
    height: 50%;
    width: 30%;
}

.garderobo-widget-look-product--socks {
    height: 16% !important;
    bottom: 23% !important;
    display: block !important;
}

.garderobo-widget-look-product--accessory-glasses {
    width: 14%;
    height: 14%;
    right: 11% !important;
    z-index: 12 !important;
    top: 5%;
}

.garderobo-widget-look-product--accessory-glasses-2 {
    right: 25% !important;
    top: 28%;
}

.garderobo-widget-look-product--right-gloves {
    right: 18% !important;
    top: 53% !important;
    z-index: 12 !important;
}

.garderobo-widget-look-product--accessory-glasses {
    width: 14%;
    height: 14%;
    right: 2%;
    z-index: 12 !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--center-layer-full {
    width: 35%;
    bottom: 0;
    top: auto;
}

.garderobo-widget-look-product--shoes {
    height: 21%;
    left: 14%;
}

.garderobo-widget-feed-items-counter {
    min-width: 100px;
}

.garderobo-widget-feed-container:not(.garderobo-widget-feed-item-look) {
    cursor: pointer;
}

.garderobo-widget-feed-container:not(.garderobo-widget-feed-item-look) .duplicate {
    z-index: -1;
}

.garderobo-widget-popup-list-item-swap-container-item--selected .garderobo-widget-popup-list-item-swap-item-price {
    padding: 0;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar,
.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb,
.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #777;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb:hover,
.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb:hover {
    background-color: #121212;
}

.garderobo-widget-popup-list-item-name,
.garderobo-widget-popup-list-item-name-hide {
    display: -webkit-box !important;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    min-height: 31px;
    margin-block: 5px;
    font-size: 10px;
    line-height: 1;
    text-align: center;
    overflow: hidden;
}

.garderobo-widget-popup-list-item-swap-container-list {
    padding-bottom: 10px;
}

.garderobo-widget-popup-list-item-swap-item-price {
    position: static;
    font-size: 12px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin-left: 0;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    min-height: 73px;
}

.garderobo-multiple-controls {
    display: none;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    text-transform: none;
    background-color: #105a33;
    color: #fff;
    border: none;
    width: 100%;
    border-radius: 20px;
}

.garderobo-widget-feed-header--with-counter .showItems4 {
    display: flex !important;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 0;
}

.garderobo-widget-popup-list-item {
    padding: 23px 0;
}

@media only screen and (min-width: 769px) {
    .garderobo-widget-popup {
        max-height: calc(100vh - 270px) !important;
        margin-top2: 80px !important;

        max-width: 65%;
    }

    .garderobo-widget-popup-list-content {
        height: calc(100% - 112px);
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1440px) {
    .garderobo-widget-feed-items-counter {
        margin-left: 30px;
    }

    .garderobo-widget-feed-item-look {
        margin-inline: calc(100vw / 100);
    }

    .garderobo-widget-popup {
        margin-top2: 60px !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed header {
        font-size: 16px !important;
    }

    .garderobo-widget-feed-items-counter-text {
        font-size: 16px;
    }
}

@media only screen and (max-width: 1200px) {

    .garderobo-widget-feed-items-counter {
        margin-left: 40px;
    }

    .garderobo-widget-feed-item-look {
        margin-inline: calc(100vw / 30);
    }

    .garderobo-widget-popup {
        margin-top2: 130px !important;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
        width: calc(9 * (100vw / 25));
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed header {
        font-size: 14px !important;
    }

    .garderobo-widget-feed-items-counter-text {
        font-size: 14px;
    }
}

@media only screen and (max-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
        width: calc(19 * (100vw / 25));
    }

    .garderobo-widget-feed-header--with-counter .showItems2 {
        display: flex !important;
    }

    .garderobo-widget-popup-list-item {
        padding: 27px 0;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup {
        max-height2: calc(100vh - 210px) !important;
        margin-top2: 70px !important;

        height: 100vh !important;
        max-height: 100vh !important;
    }

    .garderobo-widget-popup-container .garderobo-widget-feed-container {
        display: none !important;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup-content .garderobo-widget-feed-header {
        display: flex !important;
        margin-top: 60px;
        margin-bottom: 20px;
        align-items: center;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup-content  .garderobo-widget-control-left, .garderobo-widget-popup-container .garderobo-widget-popup-content  .garderobo-widget-control-right {
        display: none !important;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 14px;
        margin-bottom: 0;
    }

    .garderobo-widget-popup-list-item-text-cart-btn {
        width: 100%;
    }

    .garderobo-widget-popup-container .garderobo-widget-feed-items-counter.garderobo-widget-hidden {
        display: flex !important;
    }

    .garderobo-widget-popup__btn-close {
        top: 45px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup__btn-close {
        top: 18px;
    }

    .garderobo-widget-popup-list-container {
        padding-bottom: 50px;
    }
}

@media only screen and (max-width: 472px) {
    .garderobo-widget-popup-list-item {
        padding: 18px 0;
    }
}

.garderobo-widget-popup-container {
    z-index: 12 !important;
}

.garderobo-widget-look__badge-special {
    display: none;
}

.garderobo-widget-feed-item--special .garderobo-widget-look-container {
    border: none;
}

.garderobo-widget-look-container, .garderobo-widget-feed-item--special .garderobo-widget-look-container {
    border: 1px solid #f0f0f0;
    border-radius: 0;
    margin: 10px;
    padding: 20px;
}

.garderobo-widget-popup-content .garderobo-widget-look-container {
    margin: 0;
    padding: 20px;
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    align-self: center;
    height: 100%;
    margin: 0 !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 24px 12px;
    width: unset;
    gap: 24px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: unset;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 160px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-content .garderobo-widget-control-left, .garderobo-widget-popup-content .garderobo-widget-control-right {
    display: block !important;
    margin-top: -45px;
}

.garderobo-widget-popup-content .garderobo-widget-control-left {
    left: 15px !important;
}

.garderobo-widget-popup-content .garderobo-widget-control-right {
    right: 15px !important;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    width: 100%;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    background: #fff;
    border-radius: 8px;
    opacity: 0.7;
}

.garderobo-widget-popup-collage-container {
    padding-top: 25px;
}

.garderobo-widget-sizes {
    width: 100%;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 150px;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: unset;
    height: 80%;
    width: 100%;
    border: none !important;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: none;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    margin-left: -1px;
    margin-top: -1px;
    padding: 0;
    font-size: 9px;
    line-height: 8px;
    border: 1px solid #ccc;
    padding: 2px 4px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
}

.garderobo-widget-feed-items-counter {
    display: flex !important;
}

.garderobo-widget-feed-items-counter.garderobo-widget-hidden {
    display: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-feed-header {
    display: none !important;
}

.garderobo-widget-popup {
    max-width: 900px;
    height: 80vh !important;
    max-height: 600px !important;
}

.garderobo-scroll-button, .garderobo-scroll-button:hover {
    background-color: #105a33;
    font-size: 7px;
}

/* SIZE PANEL */

.garderobo-set-size-panel {
    position: fixed;
    background: #fff;
    top: 0;
    right: -100vw;
    height: 100vh;
    width: 500px;
    z-index: 999;
    transition: bottom 0.5s ease, right 0.5s ease;
    padding: 0 24px;
}

.garderobo-set-size-panel h2 {
    font-size: 19px;
    margin-top: 14px;
}

.garderobo-set-size-panel-active {
    right: 0;
}

@media only screen and (max-width: 768px) {
    .garderobo-set-size-panel {
        bottom: -100vh;
        top: unset;
        left: 0;
        height: 100vh;
        width: 100vw;
    }

    .garderobo-set-size-panel-active {
        bottom: 0;
    }

    .garderobo-widget-look__bottom-panel {
        width: 100%;
        position: fixed;
        bottom: 0;
    }

    .garderobo-set-size-panel-size-buttons div {
        width: calc(33.3% - 12px) !important;
    }
}

.garderobo-set-size-panel-close-button {
    color: #000;
    right: 15px;
    top: 15px;
    position: absolute;
    font-size: 26px;
    line-height: 15px;
    z-index: 100;
    padding: 10px;
    cursor: pointer;
    transition: transform 0.1s ease-in-out;
}

.garderobo-set-size-panel-close-button::before {
    content: '';
    width: 2px;
    height: 100%;
    background-color: #333;
    position: absolute;
    top: 0;
    left: 50%;
    transform: rotate(45deg);
}

.garderobo-set-size-panel-close-button::after {
    content: '';
    width: 2px;
    height: 100%;
    background-color: #333;
    position: absolute;
    top: 0;
    left: 50%;
    transform: rotate(-45deg);
}

.garderobo-widget-sizes-custom-button {
    padding: 10px 0;
    margin-bottom: 10px;
    margin-top: 10px;
    width: 100%;
    border-top: 1px solid #d7d7d7;
    border-bottom: 1px solid #d7d7d7;
    cursor: pointer;
    position: relative;
}

.garderobo-widget-sizes-custom-button::after {
    content: "\E927";
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    position: absolute;
    right: 0px;
    font-size: 20px;
}

.garderobo-widget-look__bottom-panel {
    height: 50px;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    background: #105a33;
    border: none;
    color: #fff;
    border-radius: 20px;
    width: 100%;
    font-weight: normal;
}

.garderobo-widget-popup-list-item-text-cart-btn:hover {
    background: #187c48;
}

.garderobo-set-size-panel-size-buttons {
    display: flex;
    gap: 14px;
    flex-wrap: wrap;
    margin-top: 48px;
}

.garderobo-set-size-panel-size-buttons div {
    background-color: #fff;
    color: #292929;
    border: 1px solid #e5e5e5;
    width: calc(25% - 14px);
    cursor: pointer;
    padding: 15px 0 ;
    text-align: center;
    border-radius: 30px;
}

.garderobo-set-size-panel-size-buttons-active, .garderobo-set-size-panel-size-buttons div:hover {
    background-color: #292929 !important;
    color: #fff !important;
}

.garderobo-looks-simple-popup {
    max-width: 600px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-img-v2 {
    object-fit: contain;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    padding: 0;
}

