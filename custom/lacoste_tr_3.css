.garderobo-widget-container {
    --dark: #141414;
    --gray: #858585;
    --gray-light: #e5e5e5;
    --alert: #ff0000;
}

.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 40px;
}

.garderobo-widget-add-all-product-wrap {
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    flex-flow: row;
    align-items: flex-start;
    margin-bottom: 40px;
    position: relative;
}

.garderobo-widget-feed-container-new .garderobo-widget-brand-name {
    display: none;
}

.garderobo-widget-brand-name {
    /* display: block !important;
    margin-bottom: 5px;
    font-size: 1rem; */
}

.garderobo-widget-feed-container-new .garderobo-widget-feed-item:last-child {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed header {
    flex-shrink: 1;
    margin-inline: calc(1 * (100vw / 25));
    font-size: 22px;
    font-weight: bold;
    font-family: inherit;
    color: #292929;
    text-align: left;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.garderobo-widget-look__btn-buy {
    bottom: -40px;
    padding: 0;
    font-size: 12px;
    border: none;
    border-bottom: 1px solid #000;
    text-transform: none;
}

.garderobo-widget-feed-items-counter-text {
    font-size: 18px;
}

/* Grid Look */
.garderobo-grid-look__hat {
    grid-row: 1 / 4;
    grid-column: 9 / 13;
}

.garderobo-grid-look__accessory {
    grid-row: 11 / 14;
    grid-column: 17 / 20;
}

.garderobo-grid-look.garderobo-grid-look__layer_count_1 .garderobo-grid-look__layer-top-left,
.garderobo-grid-look.garderobo-grid-look__layer_count_2 .garderobo-grid-look__layer-top-right {
    padding-inline: 0;
}

.garderobo-grid-look__bottom_skirt {
    grid-row: 10 / 19;
    padding-inline: 5px;
    z-index: 4;
}

.garderobo-grid-look__gloves_glass {
    grid-row: 13 / 16;
    grid-column: 15 / 19;
}

.garderobo-grid-look.garderobo-grid-look__layer_count_1 .garderobo-grid-look__layer-top-left img {
    transform: scale(1) !important;
}

.garderobo-grid-look__layer-top-right {
    transform: scale(1) !important;
}

.garderobo-grid-look__belt {
    grid-row: 10 / 14;
    grid-column: 3 / 7;
}

.garderobo-grid-look__gloves {
    display: block;
    grid-row: 11 / 14;
    grid-column: 17 / 20;
}

.garderobo-grid-look__socks {
    display: block;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    max-width: 112px;
}

.garderobo-widget-popup-list-item-swap-container-item {
    max-width: 92px;
}

.garderobo-widget-feed-item-look {
    margin-inline: 15px;
}

@media only screen and (min-width: 768px) and (max-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 26px;
        font-family: inherit;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 26px;
        font-family: inherit;
    }
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
    align-self: center;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    height: auto;
    flex-direction: column;
    margin-left: calc(1 * (100vw / 25));
    border: 1px solid transparent;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    align-items: flex-start;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-image {
    width: 100%;
    padding-top: 100%;
    background-repeat: no-repeat;
    background-size: cover;
    margin-bottom: 12px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-product-image {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    font-size: 0.9rem;
    line-height: 1.2;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-name,
.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-name {
    width: 90%;
    margin-bottom: 12px;
    font-size: 1rem;
    font-family: inherit;
    text-transform: capitalize;
    font-size: 13px;
    color: #292929;
    margin-bottom: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-name:hover {
    text-decoration: underline;
}

.garderobo-widget-brand-and-price {
    order: 2;
}

.garderobo-widget-sizes-and-cart-btn {
    order: 3;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn {
    font: 1.2rem 'FFDIN Medium', 'FFDIN', Arial, sans-serif;
    color: #fff;
    background-color: var(--alert);
    border: none;
    letter-spacing: 0.03em;
    padding: 1.3rem 1.6rem 1.4rem;
    text-transform: uppercase;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn:hover {
    background-color: #c00;
    color: #fff;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    align-items: baseline;
    justify-content: flex-start;
    width: 100%;
    margin-bottom: 0.75rem;
    order: 2;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-price-container .garderobo-widget-product-price {
    font-family: 'rubl bold', 'FFDIN Medium';
    font-size: 1.5rem;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    position: relative;
    font-family: 'FFDIN Regular';
    font-size: 1.1rem;
    font-size: 0.8rem;
    line-height: 1;
    margin-left: 3.5px;
    order: 2;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-old-price::before {
    position: absolute;
    transform: translateY(-50%);
    top: 50%;
    content: '';
    border-bottom: solid 1px red;
    backface-visibility: hidden;
    height: 0;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-price {
    font-family: "Archivo", Arial, Helvetica, sans-serif;
    font-size: 13px;
    font-weight: 700;
    color: #292929;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
    opacity: .5;
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:hover,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:hover {
    opacity: 1;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    color: #616667;
    width: 16px;
    height: 16px;
    content: '';
    display: block;
    position: absolute;
    top: 17px;
    left: 17px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    left: 19px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-left-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(14.000000, 14.000000) rotate(-180.000000) translate(-14.000000, -14.000000) translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group' transform='translate(0.000000, -0.000000)'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    left: 15px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-right-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-product-sale-badge {
    display: none;
    position: absolute;
    top: 15px;
    left: 10px;
    padding: 0.4rem 0.5rem 0.5rem;
    font-size: 1.2rem;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    color: #fff;
    background-color: var(--alert);
}

.garderobo-widget-product-sale-badge::after {
    content: '';
    display: block;
    position: absolute;
    top: 2.1rem;
    left: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 1rem 1rem 0;
    border-color: transparen;
    border-right-color: var(--alert);
}

.garderobo-widget-product-control-dislike {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-sizes {
    background-color: #fff;
    border: solid 0.1rem #eee;
    box-shadow: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-sizes:hover {
    border-color: #eee;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-all-product-wrap {
    font-size: 1.5rem;
}

#btn-special-look {
    font-size: 12px;
}

@media screen and (min-width: 768px) {

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: initial !important;
    }
}

@media screen and (min-width: 992px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }
}

@media screen and (min-width: 1200px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
        width: calc(6 * (100vw / 25));
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 22%;
    left: 37%;
}

.garderobo-widget-popup-list-item-img {
    position: relative !important;
}

.garderobo-widget-popup-list-item-text-title {
    font-weight: 600;
}

.garderobo-widget-popup-list-item-swap-container-item::before {
    display: none;
}

.garderobo-widget-look-product--custom-left-top {
    z-index: 10;
    bottom: 45%;
}

.garderobo-widget-look-product--custom-right-top {
    bottom: 45%;
    background-position-y: bottom;
}

.garderobo-widget-look-product--custom-bottom {
    top: 45%;
    height: 50%;
}

.garderobo-widget-look-product--bag {
    width: 18%;
}

.garderobo-widget-look-product--center-bottom {
    width: 30% !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 30% !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--wear {
    height: 50%;
    width: 30%;
}

.garderobo-widget-look-product--socks {
    height: 16% !important;
    bottom: 23% !important;
    display: block !important;
}

.garderobo-widget-look-product--accessory-glasses {
    width: 14%;
    height: 14%;
    right: 11% !important;
    z-index: 12 !important;
    top: 5%;
}

.garderobo-widget-look-product--accessory-glasses-2 {
    right: 25% !important;
    top: 28%;
}

.garderobo-widget-look-product--right-gloves {
    right: 18% !important;
    top: 53% !important;
    z-index: 12 !important;
}

.garderobo-widget-look-product--accessory-glasses {
    width: 14%;
    height: 14%;
    right: 2%;
    z-index: 12 !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--center-layer-full {
    width: 35%;
    bottom: 0;
    top: auto;
}

.garderobo-widget-look-product--shoes {
    height: 21%;
    left: 14%;
}

.garderobo-widget-feed-items-counter {
    min-width: 100px;
}

.garderobo-widget-feed-container:not(.garderobo-widget-feed-item-look) {
    cursor: pointer;
}

.garderobo-widget-feed-container:not(.garderobo-widget-feed-item-look) .duplicate {
    z-index: -1;
}

.garderobo-widget-popup-list-item-swap-container-item--selected .garderobo-widget-popup-list-item-swap-item-price {
    padding: 0;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar,
.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb,
.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #777;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb:hover,
.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb:hover {
    background-color: #121212;
}

.garderobo-widget-popup-list-item-name,
.garderobo-widget-popup-list-item-name-hide {
    display: -webkit-box !important;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    min-height: 31px;
    margin-block: 5px;
    font-size: 10px;
    line-height: 1;
    text-align: center;
    overflow: hidden;
}

.garderobo-widget-popup-list-item-swap-container-list {
    padding-bottom: 10px;
}

.garderobo-widget-popup-list-item-swap-item-price {
    position: static;
    font-size: 12px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin-left: 0;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    min-height: 73px;
}

.garderobo-multiple-controls {
    display: none;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    text-transform: none;
}

.garderobo-widget-feed-header--with-counter .showItems4 {
    display: flex !important;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 0;
}

.garderobo-widget-popup-list-item {
    padding: 23px 0;
}

@media only screen and (min-width: 769px) {
    .garderobo-widget-popup {
        max-height: calc(100vh - 270px) !important;
        margin-top2: 80px !important;

        max-width: 65%;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1440px) {
    .garderobo-widget-feed-items-counter {
        margin-left: 30px;
    }

    .garderobo-widget-feed-item-look {
        margin-inline: calc(100vw / 100);
    }

    .garderobo-widget-popup {
        margin-top2: 60px !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed header {
        font-size: 16px !important;
    }

    .garderobo-widget-feed-items-counter-text {
        font-size: 16px;
    }
}

@media only screen and (max-width: 1200px) {

    .garderobo-widget-feed-items-counter {
        margin-left: 40px;
    }

    .garderobo-widget-feed-item-look {
        margin-inline: calc(100vw / 30);
    }

    .garderobo-widget-popup {
        margin-top2: 130px !important;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
        width: calc(9 * (100vw / 25));
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed header {
        font-size: 14px !important;
    }

    .garderobo-widget-feed-items-counter-text {
        font-size: 14px;
    }
}

@media only screen and (max-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
        width: calc(19 * (100vw / 25));
    }

    .garderobo-widget-feed-header--with-counter .showItems2 {
        display: flex !important;
    }

    .garderobo-widget-popup-list-item {
        padding: 27px 0;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup {
        max-height2: calc(100vh - 210px) !important;
        margin-top2: 70px !important;

        height: 100vh !important;
        max-height: 100vh !important;
    }

    .garderobo-widget-popup-container .garderobo-widget-feed-container {
        display: none !important;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup-content .garderobo-widget-feed-header {
        display: flex !important;
        margin-top: 40px;
        margin-bottom: 20px;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup-content  .garderobo-widget-control-left, .garderobo-widget-popup-container .garderobo-widget-popup-content  .garderobo-widget-control-right {
        display: none !important;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 14px;
        margin-bottom: 0;
    }

    .garderobo-widget-popup-list-item-text-cart-btn {
        width: 100%;
    }

    .garderobo-widget-popup-container .garderobo-widget-feed-items-counter.garderobo-widget-hidden {
        display: flex !important;
    }
}

@media only screen and (max-width: 472px) {
    .garderobo-widget-popup-list-item {
        padding: 18px 0;
    }
}

.garderobo-widget-popup-container {
    z-index: 9999 !important;
}

.lc-popup {
    z-index: 0;
}

.garderobo-widget-look__badge-special {
    display: none;
}

.garderobo-widget-feed-item--special .garderobo-widget-look-container {
    border: none;
}

.garderobo-widget-look-container, .garderobo-widget-feed-item--special .garderobo-widget-look-container {
    border: 1px solid #f0f0f0;
    border-radius: 0;
    margin: 10px;
    padding: 20px;
}

.garderobo-widget-popup-content .garderobo-widget-look-container {
    margin: 0;
    padding: 20px;
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    align-self: center;
    height: 100%;
    margin: 0 !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 24px 12px;
    width: unset;
    gap: 24px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: unset;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 160px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-content .garderobo-widget-control-left, .garderobo-widget-popup-content .garderobo-widget-control-right {
    display: block !important;
    margin-top: -45px;
}

.garderobo-widget-popup-content .garderobo-widget-control-left {
    left: 15px !important;
}

.garderobo-widget-popup-content .garderobo-widget-control-right {
    right: 15px !important;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    width: 100%;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    background: #fff;
    border-radius: 8px;
    opacity: 0.7;
}

.garderobo-widget-popup-collage-container {
    padding-top: 25px;
}

.garderobo-widget-sizes {
    width: 100%;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 150px;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: unset;
    height: 80%;
    width: 100%;
    border: none !important;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    margin-left: -1px;
    margin-top: -1px;
    padding: 0;
    font-size: 9px;
    line-height: 8px;
    border: 1px solid #ccc;
    padding: 2px 4px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
}

.garderobo-widget-feed-items-counter {
    display: flex !important;
}

.garderobo-widget-feed-items-counter.garderobo-widget-hidden {
    display: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-feed-header {
    display: none !important;
}

.garderobo-widget-popup {
    max-width: 900px;
    height: 80vh !important;
    max-height: 600px !important;
}

.garderobo-scroll-button, .garderobo-scroll-button:hover {
    background-color: #00f;
    font-size: 7px;
}





.garderobo-widget-popup .garderobo-widget-feed-container {
    border: 1px solid #f0f0f0;
}

.garderobo-widget-popup .garderobo-widget-popup-collage-container {
    border: none;
}

.garderobo-widget-popup .garderobo-widget-popup-list-content {
    border: 1px solid #f0f0f0;
    margin-right: 15px;
}

.garderobo-widget-popup-list-header {
    border: none;
    height: 65px;
}

.garderobo-widget-popup-list-content {
    height: calc(100% - 103px - 1px);
}

.garderobo-widget-popup-collage-container2::after {
    content: ""; /* Обязательно для псевдоэлемента */
    position: absolute; /* Позиционирование относительно родительского элемента */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url(data:image/png;base64,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); /* Прозрачный красный цвет */
    transform: rotate(45deg); /* Поворот фона на 45 градусов */
    z-index: -1; /* Помещаем псевдоэлемент под содержимое элемента */
}

.garderobo-widget-popup-list-item {
    border-color: #f0f0f0;
}