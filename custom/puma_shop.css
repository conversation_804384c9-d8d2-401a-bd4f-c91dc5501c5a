/* PUMA */

.garderobo-widget-container {
    --dark: #141414;
    --gray: #858585;
    --gray-light: #e5e5e5;
    --alert: #ff0000;
}
.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 40px;
}

.garderobo-widget-add-all-product-wrap {
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    flex-flow: row;
    align-items: flex-start;
    margin-bottom: 40px;
    position: relative;
}

.garderobo-widget-feed-container-new .garderobo-widget-brand-name {
    display: none;
}

.garderobo-widget-feed-container-new .garderobo-widget-feed-item:last-child {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font: 600 1.5rem 'FFDIN Medium', 'FFDIN', Arial, sans-serif;
    letter-spacing: 0;
    color: #000;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 40px;
    text-transform: uppercase;
}
@media only screen and (min-width: 768px) and (max-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font: 600 2.15rem 'FFDIN Medium', 'FFDIN', Arial, sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}
@media screen and (min-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font: 600 2.5rem 'FFDIN Medium', 'FFDIN', Arial, sans-serif;
        letter-spacing: 0.02em;
    }
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 50%;
    padding: 15px 10px;
    height: auto;
    border: 1px solid transparent;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    position: relative;
    display: flex;
    flex-flow: row wrap;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: auto;
    box-sizing: border-box;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item:hover {
    border-color: #ccc;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    align-items: flex-start;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    width: 100%;
    padding-top: 100%;
    background-repeat: no-repeat;
    background-size: contain;
    margin-bottom: 15px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-product-image {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    font-size: 0.9rem;
    line-height: 1.2;
}

.garderobo-widget-container
    .garderobo-widget-feed
    .garderobo-widget-feed-container-new
    .garderobo-widget-feed-items
    .garderobo-widget-feed-item
    .garderobo-widget-product-footer
    .garderobo-widget-product-name,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    text-align: center;
    width: 100%;
    min-height: 6.3rem;
    margin-bottom: 7px;
    font: 1.6rem 'FFDIN Regular', 'FFDIN', Arial, sans-serif;
    line-height: 1.2;
    order: 1;
}

.garderobo-widget-brand-and-price {
    order: 2;
}

.garderobo-widget-sizes-and-cart-btn {
    order: 3;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn {
    font: 1.2rem 'FFDIN Medium', 'FFDIN', Arial, sans-serif;
    color: #fff;
    background-color: var(--alert);
    border: none;
    letter-spacing: 0.03em;
    padding: 1.3rem 1.6rem 1.4rem;
    text-transform: uppercase;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn:hover {
    background-color: #c00;
    color: #fff;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    align-items: baseline;
    justify-content: center;
    width: 100%;
    margin-bottom: 0.75rem;
    order: 2;
}

.garderobo-widget-container
    .garderobo-widget-feed
    .garderobo-widget-feed-container-new
    .garderobo-widget-feed-items
    .garderobo-widget-feed-item
    .garderobo-widget-product-footer
    .garderobo-widget-product-price-container
    .garderobo-widget-product-price {
    font-family: 'rubl bold', 'FFDIN Medium';
    font-size: 1.5rem;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    position: relative;
    font-family: 'FFDIN Regular';
    font-size: 1.1rem;
    line-height: 1;
    margin-left: 3.5px;
    order: 2;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price::before {
    position: absolute;
    transform: translateY(-50%);
    top: 50%;
    content: '';
    border-bottom: solid 1px red;
    backface-visibility: hidden;
    height: 0;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    font-size: 1.5rem;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: -30px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: -30px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    color: #616667;
    width: 24px;
    height: 24px;
    content: '';
    display: block;
    position: absolute;
    top: 12px;
    left: 12px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-left-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(14.000000, 14.000000) rotate(-180.000000) translate(-14.000000, -14.000000) translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group' transform='translate(0.000000, -0.000000)'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-right-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-product-sale-badge {
    display: inline-block;
    position: absolute;
    top: 15px;
    left: 10px;
    padding: 0.4rem 0.5rem 0.5rem;
    font-size: 1.2rem;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    color: #fff;
    background-color: var(--alert);
}

.garderobo-widget-product-sale-badge::after {
    content: '';
    display: block;
    position: absolute;
    top: 2.1rem;
    left: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 1rem 1rem 0;
    border-color: transparen;
    border-right-color: var(--alert);
}

.garderobo-widget-product-control-dislike {
    display: none;
}

.garderobo-widget-container
    .garderobo-widget-feed
    .garderobo-widget-feed-container-new
    .garderobo-widget-feed-items
    .garderobo-widget-feed-item
    .garderobo-widget-product-footer
    .garderobo-widget-sizes {
    background-color: #fff;
    border: solid 0.1rem #eee;
    box-shadow: none;
}

.garderobo-widget-container
    .garderobo-widget-feed
    .garderobo-widget-feed-container-new
    .garderobo-widget-feed-items
    .garderobo-widget-feed-item
    .garderobo-widget-product-footer
    .garderobo-widget-sizes:hover {
    border-color: #eee;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-all-product-wrap {
    font-size: 1.5rem;
}

#btn-special-look {
    font-size: 12px;
}

@media screen and (min-width: 425px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }
}

@media screen and (min-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33.33%;
    }
}

@media screen and (min-width: 992px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 25%;
    }
}

@media screen and (min-width: 1140px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 20%;
    }
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 22%;
    left: 37%;
}

.garderobo-widget-look-product--socks {
    display: none;
}

.garderobo-widget-popup-list-item-img {
    position: relative !important;
}

.garderobo-widget-popup-list-item-text-title {
    font-weight: 600;
}

.garderobo-widget-popup-list-item-swap-container-item::before {
    display: none;
}

.garderobo-widget-look-product--custom-left-top {
    z-index: 10;
    bottom: 45%;
}

.garderobo-widget-look-product--custom-right-top {
    bottom: 45%;
    background-position-y: bottom;
}

.garderobo-widget-look-product--custom-bottom {
    top: 45%;
    height: 50%;
}

.garderobo-widget-look-product--gloves {
    display: none;
}