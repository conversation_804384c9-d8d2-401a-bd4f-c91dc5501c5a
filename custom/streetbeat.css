.garderobo-widget-container {
    font-family: Open Sans,arial,sans-serif;
}

.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 20px;
    flex: 1;
    align-self: center;
    height: 100%;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 24px;
    font-weight: 700;
    line-height: 26px;
    color: #222;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 30px;
    font-family: Proxima Nova ExCn,arial,sans-serif;
}

.garderobo-widget-popup-content .garderobo-widget-feed header  {
    font-size: 24px !important;
    font-weight: 700;
    line-height: 26px;
    color: #222;
    text-transform: none;
    font-family: Proxima Nova ExCn,arial,sans-serif;
    text-align: left;
    margin-left: 74px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: calc(100% - 75px);
    align-items: center;
    height: 100%;
    flex-direction: column;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    background-image: url('data:image/svg+xml,%3Csvg xmlns%3D%22http%3A//www.w3.org/2000/svg%22 width%3D%2240%22 height%3D%2240%22 viewBox%3D%220 0 40 40%22 fill%3D%22none%22%3E%3Cg clip-path%3D%22url(%23clip0_1998_5267)%22%3E%3Ccircle cx%3D%2220%22 cy%3D%2220%22 r%3D%2219.5%22 fill%3D%22%230A3241%22 stroke%3D%22white%22/%3E%3Cpath fill-rule%3D%22evenodd%22 clip-rule%3D%22evenodd%22 d%3D%22M17.79 12.125L26.25 20.625L17.79 29.125L16.25 27.5784L23.1713 20.625L16.25 13.6719L17.79 12.125Z%22 fill%3D%22white%22/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id%3D%22clip0_1998_5267%22%3E%3Crect width%3D%2240%22 height%3D%2240%22 fill%3D%22white%22/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E');
    background-repeat: no-repeat;
    background-size: contain;
    width: 40px;
    height: 40px;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
    transform: rotate(180deg);
}

.garderobo-widget-control-left.garderobo-widget-muted, .garderobo-widget-control-right.garderobo-widget-muted {
    display: block !important;
    cursor: not-allowed !important;
    opacity: 20%;
    pointer-events: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
    top: calc(50% - -24px);
}

.garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
    top: calc(50% - -24px);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    height: 100%;
    width: 100%;
}

.garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    width: 32%;
    padding-bottom: 33.33%;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}

/* STANDART LIST */
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: calc(20% - 2px);
    border: 1px solid white;
    padding: 0;
    margin: 0;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 240px;
    width: 91%;
    padding: 20px 10px 0;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-y: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: auto;
    flex-direction: column-reverse;
    padding: 10px 10px 20px 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: block;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    text-align: center;
    border: none;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.31;
    color: #222;
    margin: 10px 0 0;
}

.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    font-size: 13px;
    font-weight: 400;
    line-height: 1.31;
    color: #222;
    margin: 10px 0 0 !important;
}

.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item:hover {
    border-color: transparent;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    color: #F71735;
    font-size: 12px;
    font-weight: 700;
    line-height: 20px;
    padding: 0;
}

.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-feed-item .garderobo-widget-product-price {
    color: #F71735;
    font-size: 12px;
    font-weight: 700;
    line-height: 20px;
    padding: 0;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    font-size: 12px;
    padding: 0 0 0 5px;
    order: 2;
    text-decoration: line-through;
    color: #222;
    line-height: 20px;
}

.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-feed-item .garderobo-widget-product-old-price {
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
    padding: 0;
    color: #222;
    height: fit-content;
    margin-left: 5px;
}

.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-product-price-container {
    align-items: center;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
}

.garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1 / 1;
    max-width: 100%;
    width: 100%;
    max-height: 100%;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 0;
}

/* COLLAGES POPUP */
.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    height: 100%;
    margin: 0;
    border: none;
    border-right: 1px solid #E0E0E0;
    align-self: unset;
}

.garderobo-widget-popup-content {
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
    padding: 0;
    border-right: none;
    border-right: 1px solid #EFF1F3;
    padding-bottom: 60px;
}

.garderobo-widget-popup .garderobo-widget-feed {
    border: none;
    align-self: unset;
    margin-top: 0;
    margin-right: 0;
    height: 100%;
    flex: 1;
    margin: 0 20px !important;
    padding: 74px 0;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    height: unset !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-bottom: 0 !important;
}

.garderobo-widget-popup-list-header {
    display: none;
}

.garderobo-widget-popup-list-content {
    height: calc(100% - 84px);
    margin: 0;
    border: none;
    margin-top: auto;
    margin-bottom: auto;
}

.garderobo-widget-popup-list-item:first-child {
    border-top: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 30px 40px 30px 56px;
    width: unset;
    gap: 20px;
    border-top: 1px solid #EFF1F3;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 71px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    border-bottom: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 108px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-list-item-swap-element {
    position: absolute;
    top: 10px;
    left: 20px;
}

.garderobo-widget-popup-list-item-swap-icon {
    background-image: url('data:image/svg+xml,%3Csvg xmlns%3D%22http%3A//www.w3.org/2000/svg%22 width%3D%2232%22 height%3D%2232%22 viewBox%3D%220 0 32 32%22 fill%3D%22none%22%3E%3Crect width%3D%2232%22 height%3D%2232%22 rx%3D%2216%22 fill%3D%22%23E9E9E9%22/%3E%3Cg clip-path%3D%22url(%23clip0_1559_44085)%22%3E%3Cpath d%3D%22M10.4714 16.7926C10.9158 19.4776 13.292 21.5406 16.1402 21.5406C18.4613 21.5406 20.5488 20.1588 21.4504 18.0637L21.5229 17.8952L22.3335 18.2528L22.2624 18.4198C21.2227 20.8626 18.8162 22.4342 16.1401 22.4342C12.9064 22.4342 10.1925 20.1128 9.61945 17.0676L8.46781 18.6297L7.75 18.0957L9.85332 15.2452L12.7287 17.3332L12.1892 18.0471L10.4714 16.7926Z%22 fill%3D%22%23222222%22/%3E%3Cpath d%3D%22M21.7851 14.9358C21.3482 12.2389 18.9668 10.1437 16.1134 10.1437C13.8181 10.1437 11.7314 11.4996 10.8301 13.6197L10.758 13.7893L9.94667 13.4314L10.0178 13.2643C11.0575 10.8216 13.4639 9.25 16.1401 9.25C19.3693 9.25 22.0551 11.5407 22.6546 14.576L23.724 13.031L24.45 13.5264L22.4343 16.4378L19.5011 14.4392L20.0018 13.7167L21.7851 14.9358Z%22 fill%3D%22%23222222%22/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id%3D%22clip0_1559_44085%22%3E%3Crect width%3D%2218%22 height%3D%2218%22 fill%3D%22white%22 transform%3D%22translate(7 7)%22/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E');
    background-repeat: no-repeat;
    background-size: contain;
    width: 32px;
    height: 32px;
    cursor: pointer;
}

.garderobo-widget-popup-list-item-swap-text { 
    display: none;
}

.garderobo-widget-popup-list-item-swap-element:hover .garderobo-widget-popup-list-item-swap-text {
    display: block;
    color: #222222;
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;
    padding: 10px;
    background-color: #fff;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .15);
}

.garderobo-widget-popup-list-item-swap:hover::after {
    opacity: 1;
} 

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: unset;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text {
    display: initial;
}

.garderobo-widget-popup-list-item-like-button {
    background-image: url('data:image/svg+xml,%3Csvg xmlns%3D%22http%3A//www.w3.org/2000/svg%22 width%3D%2240%22 height%3D%2240%22 viewBox%3D%220 0 40 40%22 fill%3D%22none%22%3E%3Crect y%3D%22-0.00195312%22 width%3D%2240%22 height%3D%2240%22 rx%3D%2220%22 fill%3D%22%23222222%22 fill-opacity%3D%220.1%22/%3E%3Cpath d%3D%22M12.7711 12.9599C15.8554 11.0125 18.8755 12.4731 20 13.4468C21.1245 12.4731 24.1446 11.0125 27.2289 12.9599C31.0843 15.3942 30.6024 20.2628 27.7108 22.6971L20 30L12.2892 22.6971C9.39759 20.2628 8.91566 15.3942 12.7711 12.9599Z%22 stroke%3D%22%23222222%22/%3E%3C/svg%3E');
    background-repeat: no-repeat;
    background-size: contain;
    width: 40px;
    height: 40px;
    transition: background 0.3s ease;
    cursor: pointer;
    position: absolute;
    right: 40px;
    top: 95px;
}

.garderobo-widget-popup-list-item-text-brand {
    display: none !important;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 0;
    font-size: 13px;
    font-weight: 400;
    line-height: 20px;
    order: 0;
    color: #222;
}

.garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 0;
}

.garderobo-widget-popup-list-item-text-price {
    font-size: 13px;
    font-weight: 700;
    line-height: 20px;
    color: #222;
}

.garderobo-widget-popup-list-item-text-prices {
    font-weight: 600;
}

.garderobo-widget-popup-list-item-text-new-price {
    color: #F71735;
    font-size: 13px;
    font-weight: 700;
    line-height: 20px;
    margin-right: 5px;
    padding: 0;
}

.garderobo-widget-popup-list-item-text-discount {
    font-size: 13px;
    font-weight: 400;
    line-height: 20px;
    padding: 0;
    color: #222;
    height: fit-content;
}

.garderobo-widget-popup-list-item-text-bottom {
    margin-top: 0;
    margin-bottom: 5px;
    order: -1;
}

.garderobo-widget-popup-list-item-text-prices {
    flex-direction: row-reverse;
    align-items: center;
    gap: 0; 
    padding: 0;
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 10px;
    width: 100%;
}

.garderobo-widget-popup-actions-controls {
    margin-top: 20px;
}

.garderobo-widget-popup-action-buttons {
    width: calc(50% - 30px);
}

.garderobo-widget-sizes {
    width: calc(50% - 30px);
    border-radius: 1px;
    font-size: 15px !important;
    line-height: 23px;
    font-weight: 400;
    margin-right: 0 !important;
    height: 42px;
    padding: 10px !important;
    position: relative;
    border-color: #AFB9C3;
    color: #222;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    text-transform: none;
    background-color: #0A3241;
    margin-top: 0;
    width: 100%;
    height: 42px;
    border-radius: 1px;
    border: none;
    font-weight: 400;
    font-size: 15px;
    line-height: 23px;
    text-align: center;
    transition: .3s;
    color: #fff;
}

.garderobo-widget-popup-list-item-text-cart-btn-link-state {
    background-color: #EDED64;
    color: #000;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text {
    padding-right: 20px;
    position: relative;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-cart-btn {
    margin-bottom: 0;
    width: calc(50% - 40px);
    position: absolute;
    left: 213px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-like-button {
    top: unset;
    bottom: 1px;
    right: 20px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-prices {
    width: fit-content;
    margin-bottom: 20px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-bottom > div {
    width: 100%;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-error {
    position: absolute !important;
    bottom: -18px;
    text-align: start;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-bottom {
    margin: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-img-v2 {
    object-fit: contain;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 20px 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    padding: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-content {
    height: calc(100% - 102px);
    border-bottom: 1px solid #e0e0e0;
    margin: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-header {
    font-size: 24px;
    font-weight: 700;
    line-height: 26px;
    color: #222;
    text-align: center;
    font-family: Proxima Nova ExCn, arial, sans-serif;
    padding: 0;
}

.garderobo-widget-popup-list-item-swap-button {
    display: none !important;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

/* SWAP CONTAINER */
.garderobo-widget-popup-list-item-swap-container {
    padding-bottom: 8px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 170px;
    padding: 12px;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    right: unset;
    left: 20px;
    top: 10px;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiByeD0iMTYiIGZpbGw9IiMyMjIyMjIiLz4KPGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXAwXzE1NTlfNDQ0MzApIj4KPHBhdGggZD0iTTEwLjQ3MTQgMTYuNzkyNkMxMC45MTU4IDE5LjQ3NzYgMTMuMjkyIDIxLjU0MDYgMTYuMTQwMiAyMS41NDA2QzE4LjQ2MTMgMjEuNTQwNiAyMC41NDg4IDIwLjE1ODggMjEuNDUwNCAxOC4wNjM3TDIxLjUyMjkgMTcuODk1MkwyMi4zMzM1IDE4LjI1MjhMMjIuMjYyNCAxOC40MTk4QzIxLjIyMjcgMjAuODYyNiAxOC44MTYyIDIyLjQzNDIgMTYuMTQwMSAyMi40MzQyQzEyLjkwNjQgMjIuNDM0MiAxMC4xOTI1IDIwLjExMjggOS42MTk0NSAxNy4wNjc2TDguNDY3ODEgMTguNjI5N0w3Ljc1IDE4LjA5NTdMOS44NTMzMiAxNS4yNDUyTDEyLjcyODcgMTcuMzMzMkwxMi4xODkyIDE4LjA0NzFMMTAuNDcxNCAxNi43OTI2WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTIxLjc4NTEgMTQuOTM1OEMyMS4zNDgyIDEyLjIzODkgMTguOTY2OCAxMC4xNDM3IDE2LjExMzQgMTAuMTQzN0MxMy44MTgxIDEwLjE0MzcgMTEuNzMxNCAxMS40OTk2IDEwLjgzMDEgMTMuNjE5N0wxMC43NTgwIDEzLjc4OTNMMTkuOTQ2NjcgMTMuNDMxNEwxMC4wMTc4IDEzLjI2NDNDMTEuMDU3NSAxMC44MjE2IDEzLjQ2MzkgOS4yNSAxNi4xNDAxIDkuMjVDMTkuMzY5MyA5LjI1IDIyLjA1NTEgMTEuNTQwNyAyMi42NTQ2IDE0LjU3NkwyMy43MjQgMTMuMDMxTDI0LjQ1IDEzLjUyNjRMMjIuNDM0MyAxNi40Mzc4TDE5LjUwMTEgMTQuNDM5MkwyMC4wMDE4IDEzLjcxNjdMMjEuNzg1MSAxNC45MzU4WiIgZmlsbD0id2hpdGUiLz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSJjbGlwMF8xNTU5XzQ0NDMwIj4KPHJlY3Qgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiBmaWxsPSJ3aGl0ZSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNyA3KSIvPgo8L2NsaXBQYXRoPgo8L2RlZnM+Cjwvc3ZnPgo=");
    transform: unset;
    padding: 0;
    width: 32px;
    height: 32px;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close:hover {
    transform: unset;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close::before, .garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close::after {
    display: none;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 0 !important;
    position: relative;
    /* height: 80%; */
    width: 100%;
    border: none !important;
    justify-content: start;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
    padding: 10px;
}

.garderobo-widget-popup-list-item-swap-item-img {
    max-height: 108px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
    height: calc(100% - 20px);
    overflow-y: auto;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 14px;
    border: 1px solid #ccc;
    display: flex;
    align-items: center;
    height: fit-content;
}

.garderobo-widget-popup-list-item-swap-item-price {
    display: flex;
    justify-content: center;
    color: #222;
    font-size: 13px;
    font-weight: 700;
    line-height: 20px;
    text-decoration: none;
}

.garderobo-widget-popup-list-item-swap-item-price.garderobo-widget-popup-list-item-swap-item-price--sale {
    color: #F71735;
}

.garderobo-widget-popup-list-item-swap-item-price--old {
    display: block !important;
    font-size: 13px;
    font-weight: 400;
    line-height: 20px;
    padding: 0;
    color: #222;
    text-decoration: line-through;
    padding-left: 5px;
}

.garderobo-widget-popup-list-item-swap-container-item--selected .garderobo-widget-popup-list-item-swap-item-price {
    padding: 0;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    height: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-popup-list-item-text-title a {
    color: #222;
}

.garderobo-widget-look__btn-buy {
    height: 42px;
    transition: .3s;
    color: #fff;
    border: none;
    border-radius: 1px;
    background: #0A3241;
    box-shadow: 0 0 0 rgba(10,50,65,0);
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;
    text-transform: unset;
    padding-inline: 32px;
    bottom: -68px;
    transition: color .2s ease 0s,background-color .2s ease 0s,box-shadow .2s ease 0s;
}

.garderobo-widget-look__btn-buy:hover, .garderobo-widget-popup-list-item-text-cart-btn:hover, .garderobo-multiple-btn:hover {
    background: rgba(10, 50, 65, .9);
    box-shadow: 2px 4px 7px rgba(10, 50, 65, .6);
    color: #fff;
}

.garderobo-grid-look__product_img[data-product-type="socks"] {
    display: none;
}

/* .garderobo-grid-look__product_img[data-product-type="hat"][data-already-in-collage="1"], .garderobo-grid-look__product_img[data-product-type="bag"][data-already-in-collage="1"] {
    display: none;
} */

.garderobo-scroll-button, .garderobo-scroll-button:hover {
    background-color: #000;
    z-index: 1;
    right: 52px;
    left: unset;
}

.garderobo-multiple-btn {
    width: 198px;
    height: 42px;
    color: #fff;
    border: none;
    border-radius: 1px;
    background: #0A3241;
    box-shadow: 0 0 0 rgba(10,50,65,0);
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;
    text-transform: unset;
    transition: color .2s ease 0s,background-color .2s ease 0s,box-shadow .2s ease 0s;
}

.garderobo-multiple-controls {
    margin-top: 12px;
}

.modatech-platform-look-like-state-button-liked, .garderobo-like-button-liked-state {
    background: url('data:image/svg+xml,%3Csvg xmlns%3D%22http%3A//www.w3.org/2000/svg%22 width%3D%2240%22 height%3D%2240%22 viewBox%3D%220 0 40 40%22 fill%3D%22none%22%3E%3Crect y%3D%22-0.00195312%22 width%3D%2240%22 height%3D%2240%22 rx%3D%2220%22 fill%3D%22%23222222%22 fill-opacity%3D%220.1%22/%3E%3Cpath d%3D%22M12.7711 12.9599C15.8554 11.0125 18.8755 12.4731 20 13.4468C21.1245 12.4731 24.1446 11.0125 27.2289 12.9599C31.0843 15.3942 30.6024 20.2628 27.7108 22.6971L20 30L12.2892 22.6971C9.39759 20.2628 8.91566 15.3942 12.7711 12.9599Z%22 fill%3D%22%23222222%22 stroke%3D%22%23222222%22/%3E%3C/svg%3E') no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
}

.garderobo-widget-sizes-custom-dropdown {
    width: calc(50% - 30px);
    position: relative;
}

.garderobo-widget-sizes-custom-dropdown-selected {
    border: 1px solid #AFB9C3;
    border-radius: 1px;
    cursor: pointer;
    padding: 0 14px;
    font-size: 15px;
    line-height: 23px;
    font-weight: 400;
    color: #222;
    height: calc(100% - 1.6px);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.garderobo-widget-sizes-custom-dropdown-error .garderobo-widget-sizes-custom-dropdown-selected {
    background-color: rgba(255, 232, 235, 0.5);
    border-color: #F71735;
}

.garderobo-widget-sizes-custom-dropdown-icon {
    background-image: url('data:image/svg+xml,%3Csvg xmlns%3D%22http%3A//www.w3.org/2000/svg%22 width%3D%229%22 height%3D%226%22 viewBox%3D%220 0 9 6%22 fill%3D%22none%22%3E%3Cpath fill-rule%3D%22evenodd%22 clip-rule%3D%22evenodd%22 d%3D%22M4.79183 5.50033L0.833496 0.916992L8.75016 0.916994L4.79183 5.50033Z%22 fill%3D%22%23222222%22/%3E%3C/svg%3E');
    background-repeat: no-repeat;
    background-size: contain;
    width: 9px;
    height: 6px;
    transition: 0.15s cubic-bezier(1,0.5,0.8,1);
}

.garderobo-widget-sizes-custom-dropdown-icon-rotate {
    transform: rotate(180deg);
}

.garderobo-widget-sizes-custom-dropdown-items {
    position: absolute;
    top: 48px;
    background-color: #fff;
    z-index: 99;
    width: 100%;
    max-height: 130px;
    overflow-y: auto;
    font-size: 15px;
    line-height: 23px;
    color: #222;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .15);
}

.garderobo-widget-sizes-custom-dropdown-items::-webkit-scrollbar {
    width: 3px;
    background: #fff;
}

.garderobo-widget-sizes-custom-dropdown-items::-webkit-scrollbar-thumb {
    background-color: #AFB9C3;
    border-radius: 10px;
}

.garderobo-widget-sizes-custom-dropdown-item {
    padding: 10px 14px;
    cursor: pointer;
}

.garderobo-widget-sizes-custom-dropdown-item:hover {
    background-color: #EFF1F3;
}

.garderobo-widget-sizes-custom-dropdown-item-selected {
    background-color: #EFF1F3;
}

.garderobo-widget-sizes-custom-dropdown-items-hidden {
    display: none;
}

.garderobo-widget-popup-list-item-text-error {
    width: calc(50% - 30px);
    text-align: center;
    color: #F71735;
    font-family: Open Sans,arial,sans-serif;
    font-size: 11px;
    line-height: 18px;
    font-weight: 400;
}

.garderobo-widget-feed-thumbnail {
    display: flex;
    margin-top: 40px;
}

.garderobo-widget-feed-thumbnail-item {
    background-color: #EFF1F3;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    margin: 0 3px;
}

.garderobo-widget-feed-thumbnail-item-active {
    background-color: #0A3241;
}

/* DESKTOP */
@media screen and (min-width: 1280px) {
    .garderobo-widget-container {
        max-width: 1360px;
        padding: 0 20px;
        margin: 0 auto;
    }

    .garderobo-widget-container.garderobo-widget-container-for-popup {
        max-width: unset;
        padding: unset;
        margin: unset;
    }

    .garderobo-widget-popup__btn-close {
        padding: 10px;
    }

    .garderobo-widget-popup__btn-close {
        right: 20px;
        top: 20px;
    }
}

@media screen and (min-width: 767px) and (max-width: 1332px) {
    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        padding: 30px 10px 30px 56px;
    }

    .garderobo-widget-popup-list-item-like-button {
        right: 10px;
    }

    .garderobo-widget-popup-list-item-text-cart-btn, .garderobo-widget-sizes-custom-dropdown-selected {
        font-size: 12px;
    }
}

@media screen and (min-width: 1024px) and (max-width: 1140px) {
    .garderobo-widget-popup {
        min-width: 95%;
    }
}

@media screen and (min-width: 769px) {
    .garderobo-widget-popup {
        height: 100% !important;
        max-width: 1800px;
        max-height: 100vh;
        position: fixed;
        top: 0;
        right: 0;
        height: 100%;
        transform: translateX(100%);
        transition: transform 0.6s ease-in-out;
    }

    .garderobo-widget-popup-container--opened .garderobo-widget-popup {
        transform: translateX(0);
    }

    .garderobo-widget-popup-container {
        top: 100% !important;
        display: flex !important;
        z-index: 99999 !important;
        justify-content: flex-end;
    }

    .garderobo-widget-popup-container.garderobo-widget-popup-container--opened {
        top: 0 !important;
    }

    .garderobo-widget-popup.garderobo-looks-simple-popup {
        max-width: 650px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 60%;
        max-width: 60%;
        padding-top: 20px;
        height: calc(100% + 20px);
        display: flex;
        flex-direction: column;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 100%;
        max-width: 100%;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-content {
        margin-left: 0 !important;
        padding: 0;
    }

    .garderobo-widget-popup-list-item-like-button:hover, .modatech-platform-look-like-state-button:hover, .garderobo-widget-popup-list-swap-item-like-button:hover {
        background-size: contain;
        transition: background 0.3s ease;
        background: url('data:image/svg+xml,%3Csvg xmlns%3D%22http%3A//www.w3.org/2000/svg%22 width%3D%2240%22 height%3D%2240%22 viewBox%3D%220 0 40 40%22 fill%3D%22none%22%3E%3Crect y%3D%22-0.00195312%22 width%3D%2240%22 height%3D%2240%22 rx%3D%2220%22 fill%3D%22%23222222%22 fill-opacity%3D%220.1%22/%3E%3Cpath d%3D%22M12.7711 12.9599C15.8554 11.0125 18.8755 12.4731 20 13.4468C21.1245 12.4731 24.1446 11.0125 27.2289 12.9599C31.0843 15.3942 30.6024 20.2628 27.7108 22.6971L20 30L12.2892 22.6971C9.39759 20.2628 8.91566 15.3942 12.7711 12.9599Z%22 fill%3D%22%23222222%22 stroke%3D%22%23222222%22/%3E%3C/svg%3E') no-repeat;
    }
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 50%;
    }
}

/* MOBILE */
@media screen and (max-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed {
        margin-bottom: 0;
        justify-content: center;
        height: 100%;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed {
        border: none !important;
        margin-left: 0 !important;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 24px;
        font-weight: 700;
        line-height: 26px;
        margin-bottom: 44px;
        white-space: wrap;
        text-align: center;
        margin-left: 0;
    }

    .garderobo-widget-popup-list-content {
        margin-left: 0 !important;
        padding: 0;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        padding: 12px;
    }

    .garderobo-widget-popup-list-item-like-button {
        width: 32px;
        height: 32px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        max-width: 140px;
        padding: 0 0 0 10px;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        margin: 0 !important;
        padding: 64px 0;
    }

    .garderobo-widget-popup-collage-container {
        height: 500px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
        flex: unset;
        padding: 0;
    }

    .garderobo-multiple-controls {
        flex-direction: column;
        gap: 10px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text {
        padding-right: 10px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        gap: 16px;
    }
}

@media screen and (max-width: 576px) {
    .garderobo-widget-popup {
        max-height: 100vh !important;
        width: auto !important;
        padding: 0;
        border-radius: 10px 10px 0 0;
        top: 100%;
        transition: top 0.7s ease;
        position: fixed !important;;
    }

    .garderobo-widget-popup-container--opened .garderobo-widget-popup {
        top: 20px;
        position: relative !important;
    }

    .garderobo-widget-popup-container {
        top: 100% !important;
        display: unset !important;
        z-index: 99999 !important;
    }

    .garderobo-widget-popup-container.garderobo-widget-popup-container--opened {
        top: 0 !important;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-header {
        padding: 0;
        margin-top: 24px;
    }

    .garderobo-widget-container .garderobo-multiple .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 100%;
    }

    .garderobo-widget-popup-actions-controls {
        flex-direction: column;
    }

    .garderobo-widget-sizes, .garderobo-widget-popup-action-buttons {
        width: 100%;
    }

    .garderobo-widget-sizes, .garderobo-widget-popup-list-item-text-cart-btn {
        height: 51px;
    }

    .garderobo-widget-sizes-custom-dropdown {
        width: 100%;
        height: 51px;
    }

    .garderobo-widget-popup-list-item-img {
        margin: 0;
        align-self: normal !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        padding: 0;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: 85px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        padding: 20px;
    }

    .garderobo-widget-popup-list-item-swap {
        bottom: 35px;
        top: unset;
        left: 27px;
    }

    .garderobo-widget-popup-list-item-swap-element {
        bottom: 35px;
        top: unset;
        left: 27px;
    }

    .garderobo-widget-popup-list-item-swap-element:hover .garderobo-widget-popup-list-item-swap-text {
        display: none;
    }

    .garderobo-widget-popup-list-item-like-button {
        bottom: 35px;
        top: unset;
        left: 67px;
    }

    .garderobo-widget-popup-list-item-text-error {
        position: absolute;
        bottom: 4px;
    }

    .garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: 20px;
    }

    .garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: 20px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-widget-look__btn-buy {
        width: calc(100%);
    }

    .garderobo-widget-container {
        padding: 0 20px;
    }

    .garderobo-widget-look-container {
        padding: 0;
    }

    .garderobo-widget-popup-list-container {
        margin-top: 40px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-cart-btn {
        position: unset;
        width: 100%;
        margin-top: 10px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-item-pic {
        padding-left: 10px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-item-like-button {
        left: -82px;
    }

    .garderobo-widget-feed-similar .garderobo-widget-feed-item {
        width: calc(50% - 5px) !important;
    }

    .garderobo-widget-container .garderobo-widget-feed-similar .garderobo-widget-feed-item .garderobo-widget-product-image {
        max-height: 150px;
    }

    .garderobo-widget-feed-similar {
        margin-top: 50px;
    }
}