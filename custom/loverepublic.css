.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 20px;
}

.garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    padding-bottom: 0 !important;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
    margin-bottom: 14px;
    text-transform: uppercase;
    padding: 0 10px;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed header, .garderobo-widget-feed-items-counter, .garderobo-widget-feed-items-counter-text {
    font-size: 16px !important;
    line-height: 24px;
    font-weight: 500;
    text-transform: uppercase;
    color: #000
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed header {
    margin: 0;
    height: 60px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0;
}

.garderobo-widget-feed-items-counter-text {
    margin-bottom: 0;
    margin: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
    height: calc(100% - 60px);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 25%;
    padding: 0 !important;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    padding: 40px 40px 80px !important;
    margin: 10px !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    background: #F6F6F6;
}

.garderobo-widget-look__btn-view {
    display: block !important;
    background-image: url("data:image/svg+xml;base64,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");
    background-repeat: no-repeat;
    background-position-x: 10px;
    background-position-y: 10px;
    border: 1px solid rgba(192, 192, 192, 1);
    border-radius: 50%;
    width: 45px;
    height: 45px;
    position: absolute;
    right: -16px;
    bottom: -56px;
    transition: all .1s ease;
}

.garderobo-widget-look__btn-view:hover {
    width: auto;
    background-color: #fff;
    border-radius: 28px;
}

.garderobo-widget-look__btn-view span {
    padding-left: 42px;
    padding-right: 16px;
    display: none;
}

.garderobo-widget-look__btn-view:hover span {
    display: block;
}

.garderobo-widget-look__btn-buy {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    height: auto;
    align-content: flex-start;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items {
    /* gap: 8px; */
    margin-right: 80px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header-content {
    margin: 0 4.5% 10px;
    padding: 5px;
    border-bottom: 2px #ccc solid;
    text-align: center;
    font-size: 14px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 470px;
    width: 100%;
    padding: 20px 10px 0;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-y: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: auto;
    flex-direction: row !important;
    padding: 10px 10px 20px 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: none;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    width: 100%;
    font-size: 1.125rem;
    font-weight: 500;
    line-height: 1.5rem;
    text-align: left;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    display: none;
    /*width: 100%;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    color: rgba(0, 0, 0, 0.4) !important;
    font-size: 0.75rem;
    font-weight: 400;
    letter-spacing: 0.0625em;
    line-height: 0.9375rem;
    width: 100%;
    text-align: left;
    margin: 0;
    margin-top: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
    color: rgba(0, 0, 0, 0.4) !important;
    padding: 0 !important;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    padding: 0 4px;
    color: #241f1f;
    font-size: 12px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price+.garderobo-widget-product-price {
    text-transform: uppercase;
    font-size: 12px;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    font-size: 10px;
    padding: 0 2px 0 5px;
    order: 2;
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    order: 3;
    text-decoration: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    width: 36px;
    height: 36px;
    background-repeat: no-repeat;
    cursor: pointer;
    top: 50%;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0px 0px 10px 0px #0000001A;
    background-image: url('data:image/svg+xml,%3Csvg%20width%3D%226%22%20height%3D%2214%22%20viewBox%3D%220%200%206%2014%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0.633346%2014L-0.00148634%2013.5132L4.99186%207.00159L-0.00148748%200.49L0.633345%200.00318574L6%207.00159L0.633346%2014Z%22%20fill%3D%22black%22%2F%3E%3C%2Fsvg%3E');
    background-size: 6px 14px;
    background-position: center;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: -8px;
    transform: rotate(180deg);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 72px;
}

.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-similar .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-right,
.garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-left {
    background-image: url('data:image/svg+xml,%3Csvg%20width%3D%226%22%20height%3D%2214%22%20viewBox%3D%220%200%206%2014%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0.633346%2014L-0.00148634%2013.5132L4.99186%207.00159L-0.00148748%200.49L0.633345%200.00318574L6%207.00159L0.633346%2014Z%22%20fill%3D%22black%22%2F%3E%3C%2Fsvg%3E');
    width: 6px;
    height: 14px;
    box-shadow: unset;
    background-color: unset;
}

.garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-left {
    transform: translateY(-50%) rotate(180deg);
    left: 55px !important;
}

.garderobo-widget-popup-content .garderobo-widget-control-right {
    right: 15px !important;
}

.garderobo-widget-popup-content .garderobo-widget-control-left {
    left: 15px !important;
}

.garderobo-widget-look-product--left-top.garderobo-widget-look-product--layer-1_top,
.garderobo-widget-look-product--right-top.garderobo-widget-look-product--layer-1_top {
    height: 30%;
    width: 25%;
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed {
        margin-bottom: 0;
    }

    .garderobo-widget-container .garderobo-widget-feed {
        justify-content: center;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 400px;
        width: 100%;
        max-width: 240px;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 14px;
        line-height: 20px;
        padding: 0 20px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        font-size: 0.9375rem;
        font-weight: 500;
        line-height: 1.1875rem;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
        flex-wrap: wrap;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
        text-transform: none;
    }

    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        padding: 20px 20px 50px 20px !important;
    }

    /* .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left, */
    /* .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right { */
        /* transform: unset; */
    /* } */

    .garderobo-widget-popup-collage-container .garderobo-widget-feed-items-counter {
        display: none !important;
    }

    .garderobo-widget-popup__btn-close {
        right: 26px;
        top: 25px;
        width: 30px;
        height: 30px;
    }
}

@media screen and (max-width: 425px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(50% - 5px);
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 165px;
    }

    .garderobo-widget-feed-item {
        height: unset !important;
    }

    .garderobo-widget-feed-items {
        gap: 10px;
    }
}

.garderobo-widget-look-product--shoes {
    height: 20%;
}

.garderobo-widget-look-product--bag {
    width: 21%;
    height: 21%;
}

.garderobo-widget-look-product--left-center-bag {
    top: 54%;
}

.garderobo-widget-look-product--gloves {
    width: 12%;
    height: 20%;
    top: 30%;
    background-position-y: bottom;
}

.garderobo-widget-feed-similar {
    display: none !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    border: none !important;
    padding-bottom: 0px !important;
    display: flex;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 20px !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-thumbnail {
    display: none;
}

.garderobo-widget-popup-collage-container .garderobo-widget-look__btn-view {
    display: none !important;
}

.garderobo-grid-look__product_img_with_positions {
    display: block !important;
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        border: none !important;
    }
}

.garderobo-grid-look-2 p {
    z-index: 50;
    text-transform: uppercase;
    line-height: 11px;
    font-size: 10px;
    display: block;
    align-items: center; /* Выравнивание по вертикали по центру */
    flex-wrap: wrap;
    gap: 0;
    text-align: center;
    opacity: 0.7;
    border-radius: 10px;
}

.garderobo-grid-look-2 p strong {
    width: 100%;
    display: block;
}

.garderobo-main-block header {display: none;}
.garderobo-main-block .garderobo-widget-look__label-look-name {display: block !important;
    position: absolute;
    bottom: -40px;
    font-size: 18px;
}

.garderobo-main-block .garderobo-widget-look__label-look-link {display: block !important;
    position: absolute;
    transform: translate(-50%);
    left: 200px !important;
}

.garderobo-widget-popup .garderobo-widget-look__label-look-name, .garderobo-widget-popup .garderobo-widget-look__label-look-link {display: none !important;}
.garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {width: calc(100% - 100px);}
.garderobo-main-block .garderobo-widget-look__btn-buy, .garderobo-main-block .garderobo-widget-look__label-look-link {
    border: none; border-bottom: 1px solid #212529;
    font-weight: normal; padding: 0;
    left: 30px;
    font-size: 16px;
    letter-spacing: 2px;
    text-transform: lowercase;
    padding-bottom: 4px;
    color: #212529;
    bottom: -70px;
}

.garderobo-main-block {height: unset;}
.garderobo-main-block .garderobo-widget-look__btn-buy:hover {background: none;}
.garderobo-main-block .garderobo-widget-popup-list-item-img, .garderobo-main-block .garderobo-widget-popup-list-item-swap-item-img {width: auto !important;}
.garderobo-main-block .garderobo-widget-feed {margin-bottom: 0;}
.garderobo-main-block .garderobo-widget-feed-item-look {margin-top: 0;}
.garderobo-main-block .garderobo-widget-feed-item-look .garderobo-widget-feed-item {padding-bottom: 98% !important; width: 100% !important;}

@media screen and (min-width: 1024px) {
    .garderobo-main-block .garderobo-widget-popup {
        max-width: 75%;
        min-height: 600px;
    }
}

@media screen and (max-width: 768px) {
    .garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-main-block .garderobo-widget-look__label-look-name {display: block !important;
        position: absolute;
        bottom: -40px;
        font-size: 16px;
    }

    .garderobo-main-block .garderobo-widget-look__btn-buy, .garderobo-main-block .garderobo-widget-look__label-look-link {
        left: 22px;
        font-size: 14px;
        letter-spacing: unset;
    }

    .garderobo-main-block .garderobo-widget-look__label-look-link {display: block !important;
        left: 140px !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-look__label-look-name, .garderobo-widget-popup-content .garderobo-widget-look__label-look-link {display: none !important;}
}

.garderobo-widget-popup-list-item-brand,
.garderobo-widget-popup-list-item-brand-hide {
    display: -webkit-box !important;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    min-height: 31px;
    margin-block: 5px;
    font-size: 10px;
    line-height: 1;
    text-align: center;
    overflow: hidden;
}

.garderobo-widget-sizes-buttons-wrapper {
    display: block;
}

.garderobo-widget-sizes-buttons {
    display: none;
    flex-wrap: wrap;
    margin: 12px 0;
}

.garderobo-widget-sizes-buttons div {
    border: 1px solid #000;
    padding: 8px 12px;
    cursor: pointer;
    margin-right: -1px;
    margin-top: -1px;
}

.garderobo-widget-popup-list-item-swap-button {
    display: none !important;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    padding: 0;
    margin-top: 0;
    position: absolute;
    right: 0;
    bottom: 20px;
}

.garderobo-widget-popup-list-item-text-cart-btn-link-state {
    overflow: hidden;
}

.garderobo-widget-popup-list-item-text-cart-btn-link-state::before {
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-scroll-button {
    position: absolute;
    left: unset;
    top: unset;
    right: 0;
    bottom: 110px;
    border-radius: 0;
    background: #000 !important;
    width: 100px;
    height: 45px;
    padding: 0;
}

#btn-special-look .garderobo-btn-special-look-arrow, .garderobo-scroll-button .garderobo-btn-special-look-arrow {
    display: none !important;
}

.garderobo-widget-popup-actions-controls button {
    background: #fff;
    border: 1px solid #E9E9E9;
    width: 128px;
    height: 32px;
    font-size: 10px;
    line-height: 14px;
    font-weight: 500;
    color: #000;
    text-transform: uppercase;
}

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
    overflow: unset;
    display: flex;
    flex-direction: column-reverse;
    justify-content: left;
}

.garderobo-widget-popup-list-item-text-error {
    position: absolute;
    bottom: 48px;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 4px 0 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    width: 85%;
    text-transform: uppercase;
    order: 1;
}

.garderobo-widget-popup-list-item-text-new-price {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: rgba(161, 1, 1, 1) ;
}

.garderobo-widget-popup-list-item-text-discount {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: rgba(153, 153, 153, 1);
    margin-right: 8px;
}

.garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 0;
    padding-top: 0;
    flex-direction: row-reverse;
}

.garderobo-widget-popup-container .garderobo-widget-popup-list-item-text-bottom {
    margin: 0;
    order: 2;
}
.garderobo-widget-popup-list-item-text-price {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #000;
}
.garderobo-widget-sizes-button-active {
    background-color: #bbb;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 128px;
}

.garderobo-widget-popup-list-item-swap-container-item--not-available {
    position: relative;
}

.garderobo-widget-popup-list-item-swap-container-item--not-available::after {
    content: "Product Not Available";
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    color: #000;
    display: flex;
    padding: 0 17px;
    text-transform: uppercase;
    text-align: center;
    padding-top: 75px;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 0 !important;
    position: relative;
    height: 80%;
    width: 100%;
    border: none !important;
    justify-content: start;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    /* height: 75%; */
    background-color: #F6F6F6;
    padding: 30px;
    min-height: 170px;
}

.garderobo-widget-popup-list-item-brand {
    width: 100%;
    margin: 0 !important;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 500 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    height: 20px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: none !important;
    margin-top: 10px !important;
}

.garderobo-widget-popup-list-item-text-brand {
    display: none !important;
    margin: 0 !important;
    font-weight: 500 !important;
    text-transform: uppercase;
    font-size: 20px;
    line-height: 28px;
}

.garderobo-widget-popup-list-item-swap-item-price {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.5px;
    color: #222 !important;
    text-decoration: none;
    padding: 0;
    text-align: left;
    display: flex;
    white-space: nowrap;
    gap: 10px;
    flex-flow: row-reverse;
    justify-content: center;
    margin-top: 20px;
}

.garderobo-widget-popup-list-item-swap-container-item--selected .garderobo-widget-popup-list-item-swap-item-price {
    padding: 0;
}

.garderobo-widget-popup-list-item-swap-item-price--sale .garderobo-widget-popup-list-item-swap-item-price--old {
    display: block !important;
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
    text-decoration: line-through;
    color: #999;
}

.garderobo-widget-popup-list-item-swap-item-price {
    position: unset;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup-list-item-swap-item-price {
    justify-content: flex-end;
    margin: 0;
    padding: 0;
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
    color: #A10101 !important;
}

.garderobo-widget-popup-collage-container {
    padding: 48px 0;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}

.garderobo-widget-feed-item {
    padding: 0 !important;
    margin: 0 !important;
    height: 100%;
}

.garderobo-widget-feed-item-min-width {
    padding: 0 !important;
    margin: 0 !important;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    padding-bottom: 0 !important;
}

.garderobo-widget-feed-container {
    align-items: center;
}

.garderobo-widget-feed-items {
    height: 100%;
}

.garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1/1;
    position: relative !important;
    box-sizing: border-box;
    width: 100%;
    max-height: 100%;
    cursor: inherit;
}

.garderobo-widget-feed-item--special .garderobo-widget-look-container {
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    display: flex; 
    width: 44%;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    height: 100%;
    width: 100%;
    padding: 0 0 40px 40px;
    height: 100%;
    align-self: unset;
    margin: 0 !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 115px;
    height: 153px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: 100%;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
    background-color: #F6F6F6;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close-txt {
    position: absolute;
    top: 0;
    right: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #000;
    text-decoration: underline;
}

.look-loading-state::after {
    content: "Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 1;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-grid-look__product-icon {
    /* display: block !important; */
    width: 28px;
    height: 28px;
    background-color: #000;
    border: 2px solid #fff;
    border-radius: 50%;
    animation: vibrate 1s infinite;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-grid-look__product-icon-plus {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
}

@keyframes vibrate {
    0% {
      box-shadow: 0 0 0 0 rgba(135, 129, 129, 0.7);
    }

    50% {
      box-shadow: 0 0 0 0.6rem rgba(0, 0, 0, 0);
    }

    100% {
      box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

.garderobo-grid-look__product_img_with_positions {
    position: relative;
}

.garderobo-grid-look__product-disabled-layout img {
    opacity: 0.2;
}

.garderobo-grid-look__product-disabled-layout::before {
    content: "Not Available";
    display: flex;
    position: absolute;
    justify-content: center;
    height: 100%;
    width: 50%;
    left: 25%;
    text-transform: uppercase;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    text-align: center;
}

@media screen and (min-width: 767px) {
    .garderobo-widget-popup {
        height: 80vh !important;
        max-width: 1024px;
        max-height: 600px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 56%;
        padding-right: 40px;
    }
}

@media screen and (max-width: 766px) {
    .garderobo-widget-feed-header {
        display: block;
    }

    .garderobo-widget-feed-container {
        height: auto;
    }

    .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
        margin-bottom: 0 !important;
    }

    .garderobo-widget-popup-container .garderobo-widget-sizes {
        font-size: 14px !important;
    }

    .garderobo-widget-popup-collage-container {
        padding-top: 48px !important;
        margin-bottom: -28px !important;
    }
}

/* PLATFORM */
.modatech-look-widgets img {
    cursor: pointer;
    width: 100%;
    object-fit: contain;
}

.modatech-article-content .modatech-look-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(calc(50% - 12px), 1fr));
}

.modatech-article-content .modatech-look-widget-container {
    max-width: unset;
}

.modatech-look-widgets {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 24px;
}

.modatech-look-widget-container {
    flex: calc(33% - 24px);
    max-width: calc(33% - 12px);
    border: 1px solid #BABBC1;
}

.modatech-look-widget-container {
    position: relative;
    padding: 20px;
}

.modatech-platform-overlay {
    display: flex;
    position: absolute;
    width: 100%;
    height: 100%;
    background: #000;
    top: 0;
    left: 0;
    align-items: center;
    opacity: 0.5;
}

.modatech-platform-overlay p {
    color: #fff;
    padding-left: 10px;
    font-size: 14px;
    text-align: center;
}

.modatech-platform-loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #000;
    border-radius: 50%;
    min-width: 30px;
    min-height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.modatech-platform-look-like-state-button, .garderobo-widget-popup-list-item-like-button, .garderobo-widget-popup-list-swap-item-like-button {
    width: 20px;
    height: 17px;
    position: absolute;
    right: 16px;
    top: 16px;
    cursor: pointer;
    background: url('https://testplatform-static.modatech.ru/like-bordered.svg') no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
    z-index: 1;
}

.garderobo-widget-popup-list-item-like-button {
    bottom: 26px;
    right: 146px;
    top: unset;
}

.garderobo-widget-popup-list-swap-item-like-button {
    top: 11px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-swap-item-like-button {
    top: 36px;
    right: 20px;
}

.garderobo-widget-popup-list-item-not-available {
    /* opacity: 1; */
    position: relative;
}

/* .garderobo-widget-popup-list-item-not-available::after { */
    /* content: "";
    position: absolute;
    width: 100%;
    height: 100%; */
    /* background-color: rgba(255, 255, 255, 0.8);
    color: #000;
    display: flex;
    text-transform: uppercase;
    text-align: center;
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
    align-items: unset;
    padding-top: 20px; */
/* } */

.garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-item-img, .garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-item-img-v2 {
    opacity: 30%;
}

.garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-item-text-cart-btn {
    display: none;
}

.garderobo-widget-popup-list-item-not-available .garderobo-widget-sizes-custom-dropdown {
    display: none;
}

.garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-item-swap-container-product-size {
    display: none;
}

.garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-item-swap-item-price {
    display: none;
}

.garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-swap-item-like-button {
    display: none;
}

.garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-item-text-subscribe-btn {
    display: block !important;
    position: absolute;
    right: 0;
    bottom: 20px;
}

.modatech-platform-look-like-state-button:hover, .garderobo-widget-popup-list-swap-item-like-button:hover, .modatech-platform-look-like-state-button-liked, .garderobo-like-button-liked-state, .garderobo-widget-popup-list-item-like-button:hover {
    background: url('https://testplatform-static.modatech.ru/like-filled.svg') no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
}

/* ARTICLE */
.modatech-platform {
    font-weight: 300;
    color: #222;
}

.modatech-platform h1 {
    font-weight: 300;
    font-size: 48px;
    line-height: 69px;
    text-align: center;
}

.modatech-platform p {
    font-weight: 300;
}

.modatech-article-header {
    display: flex;
    gap: 100px;
    padding-bottom: 36px;
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 40px;
    justify-content: flex-end;
}

.modatech-article-title__info {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    text-align: center;
}

.modatech-article-header h1 {
    padding-bottom: 30px;
    position: relative;
    margin-bottom: 12px;
}

.modatech-article-header h1::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: #222;
}

.modatech-article-title__categories {
    font-size: 14px;
    line-height: 20px;
    text-transform: uppercase;
}

.modatech-article-title__author {
    font-size: 14px;
    font-weight: 600 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    margin-bottom: 6px;
}

.modatech-article-title__social-share {
    height: 25px;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 36px;
    margin-top: 70px;
}

.modatech-article-title__social-share a {
    display: block;
    align-self: center;
    cursor: pointer;
}

.modatech-article-title__image {
    flex: 0 0 calc(50% - 50px);
    aspect-ratio: 1/1;
}

.modatech-article-title__image img {
    width: 100%;
    height: unset;
    object-fit: cover;
    aspect-ratio: 1/1;
}

.modatech-article-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    gap: 60px;
}

.modatech-article-content {
    max-width: 670px;
    flex: 1;
    margin: 0 auto;
    font-size: 18px;
    line-height: 31px;
}

.modatech-article-content img {
    max-width: 100%;
}

.modatech-article-excerpt {
    font-style: italic;
    margin-top: 0 !important;
}

.modatech-article-content h2 {
    font-weight: 400;
    font-size: 32px;
    line-height: 54px;
    margin-top: 40px;
}

.modatech-article-content h3 {
    font-size: 20px;
    line-height: 28px;
    margin-bottom: 8px;
    font-weight: 300;
    text-transform: uppercase;
}

.modatech-article-content p {
    margin-bottom: 12px;
    margin-top: 8px;
}

.modatech-article-sidebar {
    min-width: 400px;
    max-width: 400px;
}

.modatech-article-sidebar-content {
    border-top: 1px solid #222;
    border-bottom: 1px solid #222;
    position: sticky;
    top: 87px;
    padding: 19px 0;
}

.modatech-article-sidebar-content h2 {
    font-size: 32px;
    font-weight: 300;
    line-height: 46px;
    margin-bottom: 36px;
}

.modatech-articles-list {
    display: flex;
    flex-wrap: wrap;
    gap: 36px;
}

.modatech-articles-list-item {
    display: flex;
    gap: 64px;
    position: relative;
    width: 100%;
    padding-bottom: 36px;
    border-bottom: 1px solid #E0E0E0
}

.modatech-related-articles-list .modatech-articles-list-item {
    gap: 24px;
}

.modatech-related-articles-list .modatech-articles-list-item-excerpt {
    display: none;
}

.modatech-related-articles-list .modatech-articles-list-item {
    padding-bottom: 0;
    border-bottom: none;
}

.modatech-related-articles-list {
    gap: 36px;
}

.modatech-articles-list-item:hover h3 {
    color: #aa7e5b;
}

.modatech-articles-list-item-categories {
    font-size: 12px;
    line-height: 17px;
    margin-bottom: 12px;
    text-transform: uppercase;
}

.modatech-articles-list-item h3 {
    font-size: 18px;
    font-weight: 400;
    line-height: 26px;
    position: relative;
}

.modatech-related-articles-list .modatech-articles-list-item h3 {
    font-weight: 300;
}

.modatech-articles-list-item img {
    width: 322px;
    min-width: 322px;
    height: 322px;
    object-fit: cover;
}

.modatech-related-articles-list img {
    width: 80px;
    min-width: 80px;
    height: 80px;
}

.modatech-articles-list-item-info {
    flex: 1;
    align-self: center;
}

.modatech-articles-list-item-info-footer {
    display: flex;
    flex-wrap: nowrap;
    gap: 14px;
    font-size: 12px !important;
    line-height: 17px !important;
    padding-top: 8px;
    margin-top: 24px;
    position: relative;
}

.modatech-articles-list-item-author {
    font-weight: 500;
    text-transform: uppercase;
}

.modatech-articles-list-item-info-footer::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 44px;
    height: 1px;
    background-color: #222;
}

.modatech-articles-list-item-author::after {
    content: "\2022";
    font-weight: 900;
    margin-left: 10px;
}

.modatech-articles-list-item-excerpt {
    margin-top: 12px;
    font-size: 17px;
    line-height: 30px;
    font-weight: 300;
}

/* BLOGGERS */

.modatech-bloggers-list {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.modatech-bloggers-list-item {
    flex: 1 0 calc(25% - 24px);
    max-width: calc(25% - 12px);
    margin-bottom: 96px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    color: #222;
}

.modatech-bloggers-list-item-info {
    color: #222;
    text-decoration: none;
}

.modatech-bloggers-list-item img {
    width: 100%;
}

.modatech-subscribe-button {
    margin-top: 24px;
    text-align: center;
    padding: 16px;
    width: 100%;
    text-transform: uppercase;
    border: 1px solid #222;
    border-radius: 24px;
    height: 48px;
    align-self: flex-end;
    max-width: 320px;
    cursor: pointer;
}

.modatech-subscribe-button:hover {
    background: #222;
    color: #fff;
}

.modatech-blogger-header {
    display: flex;
    gap: 75px;
    padding-bottom: 36px;
    border-bottom: 1px solid #E0E0E0
}

.modatech-blogger-header .modatech-article-title__social-share {
    justify-content: left;
}

.modatech-blogger-header__image {
    flex: 1 0 calc(50%);
    aspect-ratio: 1/1;
}

.modatech-blogger-header__image img {
    width: 100%;
    height: unset;
    object-fit: cover;
}

.modatech-blogger-header__texts h1 {
    text-align: left;
}

.modatech-blogger-header__texts {
    align-self: flex-end;
}

.modatech-blogger-header__info {
    display: flex;
    flex-wrap: wrap;
}

.modatech-subscribe-button-hide-state {
    display: none;
}

.modatech-article-title__social-share {
    align-self: flex-end;
}

.modatech-blogger-title, .modatech-blogger-header__title {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 1px;
    margin-top: 12px;
}

.modatech-bloggers-list-item h2 {
    font-size: 24px;
    font-weight: 400;
    line-height: 35px;
    margin-top: 12px;
    margin-bottom: 4px;
}

.modatech-blogger-bio {
    font-size: 14px;
    font-weight: 300;
    line-height: 20px;
}

.modatech-blogger-header__bio {
    font-size: 18px;
    line-height: 26px;
    margin-top: 24px;
}

.modatech-blogger-looks {
    padding: 36px 0;
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 36px;
}

.modatech-blogger-looks h2 {
    font-size: 24px;
    font-weight: 400;
    line-height: 35px;
    letter-spacing: 1.5px;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 36px;
}

.modatech-button-next-page {
    border: 1px solid #222;
    padding: 12px 74px;
    text-transform: uppercase;
    font-weight: 400;
    font-size: 13px;
    line-height: 17px;
    margin: 0 auto;
    cursor: pointer;
}

.modatech-button-next-page:hover {
    background: #222;
    color: #fff;
}

.modatech-lk-bloggers-list .modatech-bloggers-list-item {
    flex: 1 0 calc(33% - 24px);
    max-width: calc(33% - 12px);
}

.modatech-lk-bloggers-list {
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 36px;
}

.modatech-lk-look-widgets {
    justify-content: left;
    margin-bottom: 36px;
}

.garderobo-widget-popup-content {
    gap: 20px;
}

.garderobo-widget-sizes-custom-dropdown {
    position: absolute;
    bottom: 26px;
    /* padding-right: 30px; */
    width: calc(100% - 135px);
}

.garderobo-widget-sizes-custom-dropdown-selected {
    cursor: pointer;
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: #000;
    width: fit-content;
    display: flex;
    gap: 10px;
    align-items: center;
}

.garderobo-widget-sizes-custom-dropdown-icon {
    background-image: url('data:image/svg+xml,%3Csvg%20width%3D%226%22%20height%3D%2214%22%20viewBox%3D%220%200%206%2014%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0.633346%2014L-0.00148634%2013.5132L4.99186%207.00159L-0.00148748%200.49L0.633345%200.00318574L6%207.00159L0.633346%2014Z%22%20fill%3D%22black%22%2F%3E%3C%2Fsvg%3E');
    background-repeat: no-repeat;
    width: 6px;
    height: 14px;
    transition: 0.15s cubic-bezier(1,0.5,0.8,1);
    transform: rotate(90deg);
}

.garderobo-widget-sizes-custom-dropdown-icon-rotate {
    transform: rotate(-90deg);
}

.garderobo-widget-sizes-custom-dropdown-items {
    position: absolute;
    top: 28px;
    background-color: #fff;
    border: 1px solid #000;
    z-index: 99;
    width: 100%;
    max-height: 350px;
    overflow-y: auto;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #000;
}

.garderobo-widget-sizes-custom-dropdown-item {
    padding: 10px 20px;
    cursor: pointer;
}

.garderobo-widget-sizes-custom-dropdown-item:hover {
    background-color: #F6F6F6;
}

.garderobo-widget-sizes-custom-dropdown-item-selected {
    background-color: #F6F6F6;
}

.garderobo-widget-sizes-custom-dropdown-items-hidden {
    display: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    border: none;
    padding: 0 0 20px 0;
    gap: 20px;
}

.garderobo-widget-popup-list-item-with-swap {
    margin-bottom: 110px;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar, .garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background: #fff;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb, .garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #fff;
    border-radius: 2px;
}

.garderobo-widget-popup-list-content .garderobo-widget-popup-list-item-swap-container-list {
    gap: 8px;
}

.garderobo-widget-popup-collage-container {
    border-right: none;
}

.garderobo-widget-popup-list-header {
    height: 60px;
    border-bottom: none;
}

.garderobo-widget-popup-collage-container {
    padding: 0;
}

.garderobo-widget-popup__btn-close {
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M19.2914 19.9986L10 10.7071L10.7071 10L19.9986 19.2914L29.29 10L29.9971 10.7071L20.7057 19.9986L29.9971 29.29L29.29 29.9971L19.9986 20.7057L10.7071 29.9971L10 29.29L19.2914 19.9986Z' fill='black'/%3E%3C/svg%3E");
    padding: 0;
    width: 40px;
    height: 40px;
    top: 10px;
    right: 8px;
}

.garderobo-widget-popup__btn-close::before, .garderobo-widget-popup__btn-close::after {
    display: none;
}

.garderobo-widget-feed-items-counter button {
    display: none;
}

.garderobo-widget-feed-items-counter {
    display: none;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-items-counter {
    letter-spacing: -2px;
    margin-left: 8px;
    display: flex;
}

.cart-info h2 {
    margin-top: 48px;
}

.garderobo-looks-simple-popup {
    max-width: 615px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-pic {
    margin: 0 !important;
    padding: 0 !important;
    width: auto !important;
    max-width: 200px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    padding: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text {
    padding-right: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-container {
    width: 100% !important;
    max-width: unset !important;
    margin-left: 20px !important;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-content {
    height: calc(100% - 100px) !important;
    margin-left: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-bottom {
    max-width: 300px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-swap {
    display: block !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-look-container {
    max-width: 90%;
}
.garderobo-widget-feed-thumbnail {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container {
    background-color: #fff;
    height: 246px;
}

.garderobo-widget-popup-list-item-swap-container-top h3 {
    margin: 0;
    padding: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    text-transform: none;
}

.garderobo-widget-popup-list-item-swap-container-top {
    margin-bottom: 12px;
}

.garderobo-widget-popup-list-item-name {
    display: block !important;
    margin: 8px 0 4px;
    font-size: 12px;
    line-height: 14px;
    font-weight: 400;
    width: 100%;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 14px;;
}


.modatech-shop-the-model-show {
    max-width: 360px;
    padding: 16px 25px 16px 16px;
    border: 1px solid #E9E9E9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    margin: 32px 0;
}

.modatech-shop-the-model-info-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: #000;
    text-transform: uppercase;
}

.modatech-shop-the-model-info h3, .modatech-shop-the-model-info p {
    margin: 0;
}

.modatech-shop-the-model-info-products-count {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #999999;
}

.modatech-shop-the-model-products {
    display: flex;
    position: relative;
    width: 115px;
    height: 44px;
}

.modatech-shop-the-model-products-block {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    box-shadow: 0px 0px 10px 0px #0000001A;
    border: 1px solid #C0C0C0;
    background-color: #fff;
    display: none;
}

.modatech-shop-the-model-products-block:nth-child(-n+3) {
    display: block;
    position: absolute;
}

.modatech-shop-the-model-products-block:nth-child(1) {
    z-index: 2;
}

.modatech-shop-the-model-products-block:nth-child(2) {
    z-index: 1;
    left: 26px;
}

.modatech-shop-the-model-products-block:nth-child(3) {
    z-index: 0;
    left: 48px;
}

.modatech-shop-the-model-products-block-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
    padding: 5px;
}

.modatech-shop-the-model-products-right-arrow {
    background-image: url('data:image/svg+xml,%3Csvg%20width%3D%226%22%20height%3D%2214%22%20viewBox%3D%220%200%206%2014%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0.633346%2014L-0.00148634%2013.5132L4.99186%207.00159L-0.00148748%200.49L0.633345%200.00318574L6%207.00159L0.633346%2014Z%22%20fill%3D%22black%22%2F%3E%3C%2Fsvg%3E');
    width: 6px;
    height: 14px;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
        width: calc(33.33% - 20px) !important;
        padding-bottom: 0 !important;
    }

    .garderobo-shop-the-model-simple-popup, .garderobo-product-popup {
        height: 100% !important;
        max-width: 520px;
        max-height: 100vh;
        position: fixed;
        top: 0;
        right: 0;
        height: 100%;
        transform: translateX(100%);
        transition: transform 0.2s ease-in-out;
    }

    .garderobo-product-popup {
        max-width: 688px;
    }

    .garderobo-widget-popup-container--opened .garderobo-shop-the-model-simple-popup,
    .garderobo-widget-popup-container--opened .garderobo-product-popup {
        transform: translateX(0);
    }

    .garderobo-widget-popup-container-shop-the-model, .garderobo-product-popup-container {
        top: 100% !important;
        display: flex !important;
        z-index: 99999 !important;
        justify-content: flex-end;
    }

    .garderobo-widget-popup-container-shop-the-model.garderobo-widget-popup-container--opened,
    .garderobo-product-popup-container.garderobo-widget-popup-container--opened {
        top: 0 !important;
    }
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-container {
    margin: 0 !important;
    padding: 0;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-pic {
    max-width: 115px;
    width: 100% !important;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    padding: 12px;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-swap {
    display: none !important;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-header {
    padding-left: 40px;
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
    text-transform: uppercase;
    justify-content: flex-start;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-content {
    padding: 0 20px 0 40px;
}

.garderobo-widget-popup-list-item-deleted-text {
    display: block !important;
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    color: #999999;
    text-transform: uppercase;
    margin-top: 4px;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-text-cart-btn, .garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-text-subscribe-btn {
    background: #fff;
    border: 1px solid #E9E9E9;
    width: 128px;
    height: 32px;
    font-size: 10px;
    line-height: 12px;
    font-weight: 500;
    color: #000;
    text-transform: uppercase;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-img-v2 {
    object-fit: contain;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-content {
    height: calc(100% - 80px) !important;
}

.garderobo-widget-look__badge-special {
    display: none;
}

.garderobo-widget-feed-item--special .garderobo-widget-look__badge-special {
    left: -10px;
    top: -10px;
    display: block;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup__btn-close {
    right: 15px;
}

.garderobo-widget-popup-content {
    border: none;
}

.garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-text-title {
    margin-top: 4px;
}

@media (min-width: 576px) and (max-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: 32px;
    }
}

@media (max-width: 992px) {
    .modatech-article-container {
        flex-wrap: wrap;
    }

    .modatech-article-content {
        max-width: unset;
        flex: 100%;
    }

    .modatech-article-sidebar {
        max-width: unset;
        min-width: unset;
    }

    .modatech-bloggers-list-item {
        flex: 1 0 calc(33% - 24px);
        max-width: calc(33% - 12px);
        margin-bottom: 48px;
    }

    .modatech-lk-bloggers-list .modatech-bloggers-list-item {
        flex: 1 0 calc(50% - 24px);
        max-width: calc(50% - 12px);
    }

    .modatech-lk-articles-list .modatech-articles-list-item img {
        width: 250px;
        min-width: 250px;
        height: 250px;
    }

    .modatech-lk-articles-list .modatech-articles-list-item {
        gap: 36px;
    }
}

@media (max-width: 900px) {
    .modatech-article-header, .modatech-blogger-header {
        flex-wrap: wrap;
        gap: unset;
        row-gap: 24px;
    }

    .modatech-article-title__image {
        flex: 100%;
    }

    .modatech-bloggers-list-item {
        flex: 1 0 calc(50% - 24px);
        max-width: calc(50% - 12px);
    }

    .modatech-look-widget-container {
        flex: calc(50% - 36px);
        max-width: calc(50% - 18px);
        border: 1px solid #BABBC1;
    }
}

@media (max-width: 576px) {
    .modatech-platform h1 {
        font-size: 36px;
        line-height: 52px;
    }

    .modatech-article-title__social-share {
        margin-top: 40px;
    }

    .modatech-article-content p {
        font-size: 14px;
        line-height: 22px;
    }

    .modatech-bloggers-list-item {
        flex: 100%;
        max-width: unset;
    }

    .modatech-look-widget-container {
        flex: 100%;
        max-width: unset;
    }

    .modatech-articles-list-item {
        gap: 20px;
        flex-wrap: wrap;
    }

    .modatech-articles-list-item img, .modatech-lk-articles-list .modatech-articles-list-item img {
        width: 100%;
        min-width: unset;
        height: 100%;
        aspect-ratio: 1/1;
    }

    .garderobo-widget-popup {
        max-height: calc(100vh - 20px) !important;
        width: auto !important;
        top: 100%;
        transition: top 0.7s ease;
        position: fixed !important;;
    }

    .garderobo-widget-popup-container--opened .garderobo-widget-popup {
        top: 20px;
        position: relative !important;
    }

    .garderobo-widget-popup-container {
        top: 100% !important;
        display: unset !important;
        z-index: 99999 !important;
    }

    .garderobo-widget-popup-container.garderobo-widget-popup-container--opened {
        top: 0 !important;
    }

    .modatech-lk-bloggers-list .modatech-bloggers-list-item {
        flex: 1 0 calc(100% - 24px);
        max-width: calc(100% - 12px);
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        height: 248px;
        width: 100% !important;
        max-width: unset;
    }

    .modatech-related-articles-list img {
        width: 80px !important;
        min-width: 80px !important;
        height: 80px !important;
    }

    .modatech-article-content .modatech-look-widgets {
        grid-template-columns: repeat(auto-fit, minmax(100%, 1fr));
    }

    .modatech-subscribe-button {
        max-width: unset;
    }

    .garderobo-widget-popup-list-content {
        display: flex;
        flex-wrap: wrap;
        column-gap: 2.5px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
        padding: 30px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        width: calc(50% - 2px);
        flex-direction: column;
        gap: 0;
    }

    .garderobo-widget-sizes-custom-dropdown {
        /* display: none; */
        position: unset;
        width: 100%;
        padding: 0;
    }

    .garderobo-widget-popup-list-item-text {
        flex-direction: column;
        width: 100%;
        padding: 12px 8px 0;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        border: none !important;
        margin-left: 0 !important;
        padding: 0 20px;
    }

    .garderobo-widget-popup__btn-close {
        right: 14px;
        top: 16px;
        width: 30px;
        height: 30px;
        background-size: cover;
    }


    .garderobo-widget-popup-list-header {
        display: none;
    }

    .garderobo-widget-popup-list-item-swap-button, .garderobo-widget-popup-actions-controls button {
        font-size: 12px;
    }

    .garderobo-widget-popup-actions-controls button {
        width: 100%;
        position: unset !important;
    }

    .garderobo-widget-popup {
        background: #fff !important;
    }

    .modatech-article-content h3 {
        font-size: 16px;
        line-height: 20px;
    }

    .garderobo-widget-popup-content {
        border: none !important;
    }

    .garderobo-widget-popup-list-item-text-error {
        position: unset;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed-container, .garderobo-widget-popup-collage-container .garderobo-widget-feed-container .garderobo-widget-control-right, .garderobo-widget-popup-collage-container .garderobo-widget-feed-container .garderobo-widget-control-left {
        display: none !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
        width: 100%;
        padding: 0 !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-control-left {
        left: 0 !important;
    }
    .garderobo-widget-popup-content .garderobo-widget-control-right {
        right: 0 !important;
    }

    .modatech-shop-the-model-show {
        max-width: unset;
        width: auto;
    }
}

@media (max-width: 576px) {
    .garderobo-widget-feed-item::before {
        background-color: unset;
        content: unset;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 240px;
    }

    .garderobo-widget-feed-item {
        height: unset;
        width: calc(50% - 5px) !important;
    }

    .garderobo-widget-feed-items {
        gap: 0;
    }

    .garderobo-widget-feed-item-look .garderobo-widget-feed-items {
        gap: 0;
    }

    .garderobo-widget-feed-item-look {
        flex-direction: column;
    }

    .garderobo-widget-feed-thumbnail {
        width: 100%;
        display: flex;
        overflow-x: auto;
        overflow-y: hidden;
        height: 180px;
        padding: 20px 0;
        margin-bottom: 20px;
    }

    .garderobo-widget-feed-thumbnail::-webkit-scrollbar {
        width: 4px;
        height: 4px;
        background: #ccc;
    }
    
    .garderobo-widget-feed-thumbnail::-webkit-scrollbar-thumb {
        background-color: #000;
        border-radius: 2px;
    }

    .garderobo-widget-feed-thumbnail-item {
        width: 100%;
        padding: 6px;
        max-width: 84px;
        flex: 0 0 auto;
        height: 84px;
        margin: 12px 0;
    }

    .garderobo-widget-feed-thumbnail-item-active {
        box-shadow: 0 4px 16px 0 #00000014;
        border: 1px solid var(--gw-gray-2);
    }

    .garderobo-widget-feet-slider-item {
        width: 100%;
        height: 100%;
        position: relative;
    }
    .garderobo-widget-feet-slider-item img {
        position: absolute;
        object-fit: contain;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        margin-bottom: 0 !important;
    }

    .garderobo-widget-container.garderobo-widget-container-for-popup .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        margin-bottom: 70px !important;
    }

    .garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(100% - 20px) !important;
    }

    .garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-look__btn-buy {
        width: 90%;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed-items {
        flex-wrap: unset !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed-items .garderobo-widget-feed-item  {
        width: 100% !important;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-container {
        margin-left: 0 !important;
    }

    .garderobo-widget-look-container {
        width: 80%;
    }

    .garderobo-widget-popup .garderobo-widget-look-container {
        width: 100%;
    }

    .garderobo-widget-feed-item-look .garderobo-widget-look-container {
        width: 100%;
    }

    .garderobo-widget-popup-list-item-text-title {
        font-size: 12px;
        line-height: 14px;
        font-weight: 400;
        width: 100%;
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 0 0 8px;
        order: initial;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup-list-item-text-bottom {
        order: initial;
        margin-bottom: 10px;
    }

    .garderobo-widget-popup-list-item-like-button {
        top: 12px;
        right: 12px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        padding-bottom: 0 !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        padding-bottom: 0px !important;
    }

    .garderobo-widget-popup-list-content .garderobo-widget-popup-list-item-swap-item-price {
        position: unset;
    }

    .garderobo-widget-popup-list-content .garderobo-widget-popup-list-swap-item-like-button {
        display: none;
    }

    .garderobo-widget-popup-list-content .garderobo-widget-popup-list-item-swap-container-list {
        gap: 2px;
        padding: 0 20px 50px;
    }

    .garderobo-widget-popup-list-content .garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup-list-item-swap-item-price {
        padding: 0;
        margin: 0;
        justify-content: flex-end;
    }

    .garderobo-widget-popup-list-content .garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
        overflow: unset;
        height: unset;
    }

    .garderobo-widget-popup-list-content .garderobo-widget-popup-list-item-swap-container-item:last-child {
        margin-right: 10px;
    }

    .garderobo-widget-popup-list-item-swap-item-img-wrapper {
        height: 197px !important;
        background-color: #F6F6F6;
        padding: 30px;
    }

    .garderobo-widget-popup-list-item-text-prices {
        flex-direction: row;
    }

    .garderobo-widget-popup-list-item-swap-item-content {
        padding: 0 !important;
        height: 100%;
    }

    .garderobo-widget-popup-list-item-swap-container-item {
        min-width: 148px;
    }

    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items {
        margin-right: 0;
    }

    .garderobo-widget-sizes-custom-dropdown-items {
        width: 100%;
        position: fixed;
        top: unset;
        bottom: 0;
        left: 0;
        border: none;
        transition: bottom 0.7s ease;
    }

    .garderobo-widget-popup-list-item-swap-container {
        position: fixed;
        height: 353px;
        top: unset;
        bottom: 0;
        transition: bottom 0.7s ease;
    }

    .hidden {
        bottom: -100%;
    }

    .garderobo-widget-popup-list-item-swap-container-top {
        margin-bottom: 12px;
        padding: 0 20px;
    }

    .garderobo-widget-popup-list-item-swap-container-top div, .garderobo-widget-sizes-custom-dropdown-top div {
        display: block;
        height: 30px;
        cursor: pointer;
    }

    .garderobo-widget-popup-list-item-swap-container-top div::after, .garderobo-widget-sizes-custom-dropdown-top div::after {
        content: '';
        display: block;
        width: 60px;
        height: 2px;
        background-color: #999;
        position: absolute;
        top: 8px;
        left: 50%;
        transform: translateX(-50%);
    }
    
    .garderobo-widget-sizes-custom-dropdown-top h3 {
        margin: 0;
        padding: 12px 20px;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        text-transform: uppercase;
        border-bottom: 1px solid #E9E9E9;
    }

    .garderobo-widget-look__btn-view {
        bottom: -30px;
        right: 0px;
    }

    .garderobo-widget-popup-list-item-swap-container .garderobo-widget-popup__btn-close-txt {
        display: none;
    }

    .garderobo-widget-sizes-custom-dropdown-selected {
        font-size: 12px;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed header {
        font-size: 14px !important;
        padding: 0;
        margin-bottom: 8px;
    }

    .garderobo-widget-popup-list-item-with-swap {
        margin-bottom: 0;
    }

    .garderobo-widget-sizes-custom-dropdown-wrapper-hidden {
        display: none;
    }

    .garderobo-widget-popup-list-item-swap-wrapper, .garderobo-widget-sizes-custom-dropdown-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 2;
        background-color: rgba(0, 0, 0, 0.08);
    }

    .garderobo-widget-sizes-custom-dropdown-wrapper {
        transition: top 0.4s ease;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: 4px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: 4px;
    }

    .garderobo-widget-feed-item--special .garderobo-widget-look__badge-special {
        left: -10px;
        top: -10px;
    }

    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) {
        padding: 40px 0 20px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        padding: 0 10px;
    }

    .garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-content {
        padding: 0;
    }

    .garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-text {
        padding: 12px 8px 0;
        width: 100%;
    }

    .garderobo-widget-popup-container .garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-text-bottom {
        margin-bottom: 0;
    }

    /* .garderobo-widget-popup-container .garderobo-widget-popup-list-item-text-bottom */

    .garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-text-cart-btn {
        bottom: 0;
        width: 100%;
        right: unset;
        position: unset;
    }

    .garderobo-shop-the-model-simple-popup .garderobo-widget-sizes-custom-dropdown {
        margin: 10px 0;
    }

    .garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-header {
        font-size: 14px;
        padding: 0 20px;
    }

    .garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-text-subscribe-btn {
        position: unset;
        width: 100%;
    }

    .garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item-text-title {
        margin: 0;
    }

    .garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-item-text-prices {
        display: none;
    }

    .garderobo-widget-popup-list-item-deleted-text {
        margin: 8px 0;
    }

    .garderobo-shop-the-model-simple-popup .garderobo-widget-popup-list-item:not(.garderobo-widget-popup-list-item-not-available) .garderobo-widget-popup-list-item-text-bottom {
        margin-top: 8px;
    }

    .garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-item-text-title {
        margin: 0;
    }

    .garderobo-widget-popup:not(.garderobo-shop-the-model-simple-popup) .garderobo-widget-popup-list-item-not-available .garderobo-widget-popup-list-item-text-bottom {
        display: none;
    }

    #garderobo {
        padding: 0;
    }
}

.garderobo-grid-look__product-disabled-layout::before {
    content: "";
}

.garderobo-widget-popup-content .garderobo-grid-look__product-disabled-layout::before {
    content: "";
    display: flex;
    position: absolute;
    justify-content: center;
    height: 100%;
    width: 50%;
    left: 25%;
    text-transform: uppercase;
    align-items: unset;
    padding-top: 20px;
    font-size: 12px;
    line-height: 14px;
    font-weight: 400;
    text-align: center;
}

.garderobo-widget-popup-content .garderobo-grid-look__product-disabled-layout div {
    opacity: 0.1;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    display: none;
}


/*----------------  PRODUCT POPUP ---------------------*/
.garderobo-widget-popup-content-pictures {
    height: calc(100% - 60px);
    width: 37%;
    overflow-y: auto;
    padding-right: 14px;
}

.garderobo-widget-popup-content-pictures::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.garderobo-widget-popup-content-pictures::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #DADFE9;
}

.garderobo-widget-popup-content-pictures-item {
    max-width: 230px;
    margin-bottom: 10px;
}

.garderobo-widget-popup-content-pictures-item:last-child {
    margin-bottom: 0;
}

.garderobo-widget-popup-content-pictures-item img {
    width: 100%;
}

.garderobo-product-popup-container {
    justify-content: flex-end;
}

.garderobo-product-popup {
    padding: 28px;
}

.garderobo-product-popup .garderobo-widget-popup-content {
    border: none;
}

.garderobo-product-popup .garderobo-product-popup-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 22px;
}

.garderobo-product-popup .garderobo-product-popup-header .garderobo-widget-popup__btn-close {
    position: relative;
    right: 0;
    top: 0;
}

.garderobo-product-popup-header-title {
    font-size: 24px;
    line-height: 30px;
    font-weight: 500;
}

.garderobo-widget-popup-content {
    justify-content: flex-start;
}

.garderobo-widget-popup-content-product-info {
    height: calc(100% - 60px);
    width: 60%;
}

.garderobo-widget-popup-content-product-brand {
    font-size: 18px;
    line-height: 26px;
    margin: 0;
    font-weight: 500;
}

.garderobo-widget-popup-content-product-name {
    font-size: 16px;
    line-height: 24px;
    margin: 0 0 6px;
    font-weight: 400;
}

.garderobo-widget-popup-content-product-price {
    display: flex;
    font-size: 18px;
    font-weight: 400;
    line-height: 22px;
}

.garderobo-widget-popup-content-product-price-new {
    color: rgba(161, 1, 1, 1);
}

.garderobo-widget-popup-content-product-price-old {
    text-decoration: line-through;
    margin-left: 5px;
}

.garderobo-widget-popup-content-product-sizes-block {
    margin-top: 24px;
    margin-bottom: 24px;

    span {
        display: block;
        margin-bottom: 10px;
    }
}

.garderobo-widget-popup-content-product-sizes {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.garderobo-widget-popup-content-product-sizes-item {
    min-width: 40px;
    height: 32px;
    border: 2px solid #ccc;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 16px;
    line-height: 24px;
    padding: 0 10px;
}

.garderobo-widget-popup-content-product-sizes-item-selected {
    border: 2px solid #000;
}

.garderobo-widget-popup-content-product-add-to-cart-btn {
    background: #fff;
    border: 1px solid #E9E9E9;
    width: calc(100% - 38px);
    height: 44px;
    font-size: 16px;
    line-height: 24px;
    font-weight: 500;
    color: #000;
    text-transform: uppercase;
}

.garderobo-widget-popup-content-product-add-to-cart-btn-disabled {
    opacity: 0.4;
}

.garderobo-widget-popup-content-product-add-to-cart-error-msg {
    margin-top: 8px;
    color: #ff3333;
}

.garderobo-widget-popup-content-product-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.garderobo-widget-popup-content-product-buttons .garderobo-widget-popup-list-item-like-button {
    position: unset;
    width: 28px;
    height: 25px;
}

@media (max-width: 576px) {
    .garderobo-product-popup {
        padding: 20px;
    }

    .garderobo-widget-popup-content-pictures {
        display: flex;
        width: 100%;
        min-height: 184px;
        padding: 0;
        overflow-x: auto;
        overflow-y: hidden;
        gap: 10px;
    }
    
    .garderobo-widget-popup-content-pictures-item {
        min-width: 124px;
    }

    .garderobo-widget-popup-content-product-info {
        width: 100%;
    }
}

.garderobo-grid-look__product[data-category-collage="belt"] {
    display: none;
}