:root {
    --gw-dark: #222222;
    --gw-dark-1: #18191B;
    --gw-gray-1: #DADFE9;
    --gw-gray-2: #F2F3F6;
    --gw-blue: #283DF4;
    --gw-red: #E91E1E;
    --gw-white: #FFF;
}

.gw-container {
    max-width: 1232px;
    width: auto;
    margin: auto;
    font-family: "Jost", sans-serif;
}

.gw-inline {
    display: flex;
    gap: 60px;
    height: 100%;
}

.gw-page-header {
    padding: 23px 0;
    font-size: 20px;
    line-height: 34px;
    font-weight: 400;
    text-align: center;
    color: var(--dark);
    border-bottom: 1px solid var(--gw-gray-1);
}

.gw-button {
    width: 100%;
    height: 44px;
    border-radius: 8px;
    border: none;
    font-family: "Jost",sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    cursor: pointer;
}

.gw-button-add-cart {
    background-color: var(--gw-dark-1);
    color: #fff;
}

.gw-button-add-favorites {
    background-color: var(--gw-gray-2);
    color: var(--gw-dark-1);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
}

.gw-button-add-favorites.garderobo-like-button-liked-state {
    color: var(--gw-red);
}

.gw-select-size {
    height: 44px;
    width: 100%;
    border: 1px solid var(--gw-gray-1);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: var(--gw-gray-1);
    padding: 10px 12px;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url('./icons/arrow-bottom.svg') no-repeat right 8px center;
    background-size: 24px;
}

.gw-left-block, .gw-products {
    width: 50%;
    padding-inline: 20px;
}

.gw-left-block {
    position: relative;
    height: 100%;
}

.gw-slider {
    aspect-ratio: 1/1;
    position: relative;
    overflow: hidden;
    margin: 0 60px;
}

.gw-slider-item {
    height: 100%;
    position: relative;
}

.gw-slider-wrapper .gw-slider-item {
    margin: auto;
    flex: 0 0 auto;
    display: flex;
    justify-content: center;
    width: 100%;
    transition: transform 0.5s ease;
}

.gw-slider-wrapper .gw-slider-item .gw-slider-item-img {
    object-fit: contain;
}

.gw-slider-item-img {
    position: absolute;
}

.gw-slider-wrapper {
    height: 100%;
    margin: 0 auto 20px;
    position: relative;
    display: flex;
    width: 100%;
}

.gw-slider-thumbnail {
    height: 101px;
    display: flex;
    overflow-x: auto;
    padding: 20px 0;
}

.gw-slider-thumbnail::-webkit-scrollbar {
    width: 0;
    height: 0;
}

.gw-slider-thumbnail .gw-slider-thumbnail-item {
    max-width: 84px;
    flex: 0 0 auto;
    width: 100%;
    padding: 5px 8px;
    margin-inline: 6px;
    cursor: pointer;
    border: 1px solid var(--gw-white);
}
.gw-slider-thumbnail .gw-slider-thumbnail-item-active {
    box-shadow: 0 4px 16px 0 #00000014;
    border: 1px solid var(--gw-gray-2);
}

.gw-slider-thumbnail .gw-slider-thumbnail-item img {
    object-fit: contain;
}
.gw-product-item {
    display: flex;
    padding: 24px 0;
    margin-bottom: 24px;
    position: relative;
}

.gw-product-item:first-child {
    padding-top: 0;
}

.gw-product-item .gw-product-item-img-block {
    width: 32%;
    margin-right: 24px;
    position: relative;
    display: flex;
    flex-direction: column;
}
.gw-button-change-product {
    color: var(--gw-dark);
    font-size: 14px;
    font-weight: 400;
    width: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin: 12px auto 0;
}

.gw-product-item .gw-product-item-img-block img {
    object-fit: contain;
    width: 100% !important;
    max-height: 200px;
}

.gw-products {
    height: 100vh;
    overflow-y: auto;
}

.gw-products::-webkit-scrollbar,
.gw-products-list::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.gw-products::-webkit-scrollbar-thumb,
.gw-products-list::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: var(--gw-gray-1);
}

.gw-product-item-info {
    width: 68%;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.gw-product-item-info h2, .gw-product-item-price-block {
    font-size: 20px;
    line-height: 28px;
    color: var(--gw-dark);
}

.gw-product-item-info h2 {
    margin: 0;
    font-weight: 500;
}

.gw-product-item-price-block {
    font-weight: 600;
}

.gw-product-item-price-block {
    font-weight: 600;
    display: flex;
    gap: 5px;
}

.gw-product-item-price-block .gw-product-item-old-price {
    text-decoration: line-through;
}

.gw-product-item-price-with-discount-block .gw-product-item-price {
    color: #ff3333;
}

.gw-product-item-buttons {
    display: flex;
    gap: 12px;
}

.gw-product-item-show-more {
    font-size: 20px;
    line-height: 34px;
    font-weight: 400;
    color: var(--gw-blue);
    cursor: pointer;
}

.gw-button-slide {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: var(--gw-gray-1);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
}

.gw-button-slide-next {
    top: calc(50% - 90px);
    right: 20px;
}

.gw-button-slide-prev {
    top: calc(50% - 90px);
    left: 20px;
    display: none;
}

.gw-button-add-favorites span:nth-child(2) {
    height: 24px;
}

.gw-product-item-img-block a {
    padding-inline: 20px;
}

.gw-products-list {
    position: absolute;
    display: flex;
    gap: 20px;
    overflow-x: auto;
    height: 100%;
    width: 100%;
    background: var(--gw-white);
}

.gw-products-list-item {
    max-width: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}

.gw-products-list-item .gw-product-item-price {
    font-size: 16px;
    margin-top: 8px;
}

.gw-products-list-item img {
    width: 150px;
    height: 150px;
    object-fit: contain;
}

.gw-products-list-item-sizes {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 4px;
    color: var(--gw-dark);
    font-size: 12px;
}

.gw-products-list-item-sizes li {
    border: 1px solid #ccc;
    padding: 2px 5px;
}
.gw-button-add-favorites.garderobo-like-button-liked-state svg {
    fill: var(--gw-red);
}
.gw-button-add-favorites.garderobo-like-button-liked-state svg path {
    stroke: var(--gw-red);
}

@media screen and (max-width: 1200px) {
    .gw-button-add-favorites {
        min-width: 40px;
        width: 40px;
    }

    .gw-button-add-favorites span:first-child {
        display: none;
    }
}

@media screen and (max-width: 767px) {
    #widgets-inline {
        height: auto;
    }

    .gw-inline {
        flex-direction: column;
        padding-inline: 16px;
    }

    .gw-left-block, .gw-products {
        width: 100%;
        padding-inline: 0;
    }

    .gw-products {
        overflow-y: unset;
    }

    .gw-product-item {
        gap: 16px;
    }

    .gw-product-item .gw-product-item-img-block {
        width: unset;
        margin-right: 0;
    }

    .gw-product-item .gw-product-item-img-block img {
        width: 200px !important;
        max-height: 200px;
    }
    .gw-product-item-info {
        width: 100%;
    }

    .gw-slider {
        aspect-ratio: 1/1;
        margin: 0 130px;
    }

    .gw-slider-wrapper {
        width: 100%;
    }

    .gw-slider-thumbnail {
        height: 100px;
    }

    .gw-product-item-buttons {
        gap: 8px;
    }

    .gw-product-item-info h2 {
        font-size: 16px;
        line-height: 22px;
    }

    .gw-product-item-show-more {
        font-size: 16px;
        line-height: 27px;
    }

    .gw-product-item-info {
        gap: 0;
    }

    .gw-select-size {
        margin: 8px 0 16px;
    }

    .gw-product-item-img-block a {
        padding-inline: 0;
    }

    .gw-button-slide-prev {
        left: -16px;
    }

    .gw-button-slide-next {
        right: -16px;
    }
}

@media screen and (max-width: 576px) {
    .gw-product-item .gw-product-item-img-block img {
        width: 140px !important;
    }

    .gw-slider {
        aspect-ratio: 1/1;
        width: 250px;
        margin: 0 auto;
    }

    .empty-div {
        height: auto;
    }
}

.gw-slider-thumbnail-item .gw-grid-look__product-disabled-layout {
    display: none;
}

.gw-grid-look__product-disabled-layout {
    display: flex;
    position: absolute;
    justify-content: center;
    text-transform: uppercase;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    text-align: center;
}