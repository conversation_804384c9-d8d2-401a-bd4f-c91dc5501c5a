.garderobo-widget-container {
    --dark: #141414;
    --gray: #858585;
    --gray-light: #e5e5e5;
    --alert: #ff0000;
}

.garderobo-widget-container .garderobo-widget-feed:not(.garderobo-widget-feed-similar) {
    margin-bottom: 180px;
}

.garderobo-widget-feed-similar {
    margin-bottom: 125px;
}

.garderobo-widget-add-all-product-wrap {
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    flex-flow: row;
    align-items: flex-start;
    margin-bottom: 40px;
    position: relative;
}

.garderobo-widget-feed-container-new .garderobo-widget-brand-name {
    display: none;
}

.garderobo-widget-feed-container-new .garderobo-widget-feed-item:last-child {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed header {
    /* flex-shrink: 1; */
    /* margin-inline: 20px; */
    /* font-size: 22px; */
    font-weight: 500;
    color: #002D18;
    text-align: left;
    margin-bottom: 32px;
    text-transform: uppercase;
    padding-right: calc((100vw - 15px) / 25);
}

.garderobo-widget-look__btn-buy {
    bottom: -36px;
    left: 0;
    transform: unset;
    padding: 0;
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
    border: none;
    color: #002D18;
    text-transform: uppercase;
    border-bottom: 1px solid #002D18;
    text-transform: uppercase;
}

.garderobo-widget-feed-items-counter-text {
    font-size: 21px;
    line-height: 32px;
    font-weight: 700;
    color: #002D18;
    display: flex;
    letter-spacing: 2px;
}

.xl\:col-span-8 .garderobo-widget-container .garderobo-widget-feed header {
    font-size: 1.25rem;
    line-height: 1.625rem;
}

.xl\:col-span-8 .garderobo-widget-container {
    margin: 0 !important;
}

.xl\:col-span-8 .garderobo-widget-container .garderobo-widget-look-container {
    background: #fff;
}

.garderobo-widget-popup-list-item-text-prices {
    padding: 0;
    gap: 13px;
    flex-direction: row-reverse;
}

.garderobo-widget-feed-items-counter-btn {
    font-family: pz-icon !important;
    font-style: normal;
    font-weight: 400 !important;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    font-size: 9px;
    line-height: 24px;
    width: 7px;
    height: 12px;
    overflow: unset;
    display: flex;
}

.garderobo-widget-feed-items-counter-btn--next::before {
    transform: unset;
    content: "";
}

.garderobo-widget-feed-items-counter-btn--prev::before {
    content: "";
}

.garderobo-widget-feed-items-counter-btn::before {
    width: auto;
    height: auto;
    background-image: none;
}

/* Grid Look */
.garderobo-grid-look__hat {
    grid-row: 1 / 4;
    grid-column: 9 / 13;
}

.garderobo-grid-look__accessory {
    grid-row: 11 / 14;
    grid-column: 17 / 20;
}

.garderobo-grid-look.garderobo-grid-look__layer_count_1 .garderobo-grid-look__layer-top-left,
.garderobo-grid-look.garderobo-grid-look__layer_count_2 .garderobo-grid-look__layer-top-right {
    padding-inline: 0;
}

.garderobo-grid-look__bottom_skirt {
    grid-row: 10 / 19;
    padding-inline: 5px;
    z-index: 4;
}

.garderobo-grid-look__gloves_glass {
    grid-row: 13 / 16;
    grid-column: 15 / 19;
}

.garderobo-grid-look.garderobo-grid-look__layer_count_1 .garderobo-grid-look__layer-top-left img {
    transform: scale(1) !important;
}

.garderobo-grid-look__layer-top-right {
    transform: scale(1) !important;
}

.garderobo-grid-look__belt {
    grid-row: 10 / 14;
    grid-column: 3 / 7;
}

.garderobo-grid-look__gloves {
    display: block;
    grid-row: 11 / 14;
    grid-column: 17 / 20;
}

.garderobo-grid-look__socks {
    display: block;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    max-width: 112px;
}

.garderobo-widget-popup-list-item-swap-container-item {
    max-width: 92px;
}

.garderobo-widget-popup-list-item-swap-container {
    height: calc(100% + 70px);
    z-index: 0;
}

@media only screen and (min-width: 768px) and (max-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 26px;
        font-family: inherit;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
    align-self: center;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    height: auto;
    flex-direction: column;
    border: 1px solid transparent;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    align-items: flex-start;
}

.garderobo-widget-feed-items {
    display: flex;
    gap: 13px !important;
    -webkit-scroll-snap-type: x mandatory;
    -moz-scroll-snap-type: x mandatory;
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory;
    -webkit-scroll-behavior: smooth;
    -moz-scroll-behavior: smooth;
    -ms-scroll-behavior: smooth;
    scroll-behavior: smooth;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items:has(.garderobo-widget-feed-item:nth-child(3):last-child) .garderobo-widget-feed-item {
    flex: 0 0 calc((100% / 3) - (2 * 13px / 3));
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    flex: 0 0 calc((100% / 3) - (2 * 70px / 3));
    padding: 0 !important;
    width: auto !important;
    -webkit-scroll-snap-align: start;
    -moz-scroll-snap-align: start;
    -ms-scroll-snap-align: start;
    scroll-snap-align: start;
    -webkit-scroll-snap-stop: normal;
    -moz-scroll-snap-stop: normal;
    -ms-scroll-snap-stop: normal;
    scroll-snap-stop: normal;
    background-color: #F4F4F4;
    cursor: pointer;
    margin-bottom: 50px !important;
    aspect-ratio: 1 / 1;
}

.garderobo-widget-container-for-popup .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    margin-bottom: 0 !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item {
    background-color: #F4F4F4;
}

.garderobo-widget-feed-similar .garderobo-widget-feed-items .garderobo-widget-feed-item,
.garderobo-widget-feed-recommendations .garderobo-widget-feed-items .garderobo-widget-feed-item,
.garderobo-widget-feed-popular .garderobo-widget-feed-items .garderobo-widget-feed-item,
.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed-item {
    flex: 0 0 calc((100% / 4) - (3 * 30px / 4));
    padding: 0 !important;
    width: auto !important;
    -webkit-scroll-snap-align: start;
    -moz-scroll-snap-align: start;
    -ms-scroll-snap-align: start;
    scroll-snap-align: start;
    -webkit-scroll-snap-stop: normal;
    -moz-scroll-snap-stop: normal;
    -ms-scroll-snap-stop: normal;
    scroll-snap-stop: normal;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-image {
    height: unset;
    width: 100%;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-y: center;
    background-origin: content-box;
    aspect-ratio: 1 / 1;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-product-image {
    margin-bottom: 0;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer {
    flex-direction: column !important;
    align-items: flex-start !important;
    width: 100%;
    font-size: 0.9rem;
    line-height: 1.2;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-name,
.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-name {
    width: 90%;
    font-family: inherit;
    text-transform: capitalize;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: #002D18;
    margin: 9px 0 8px;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-name {
    cursor: pointer;
}

.garderobo-widget-brand-and-price {
    order: 2;
}

.garderobo-widget-sizes-and-cart-btn {
    order: 3;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn {
    font: 1.2rem 'FFDIN Medium', 'FFDIN', Arial, sans-serif;
    color: #fff;
    background-color: var(--alert);
    border: none;
    letter-spacing: 0.03em;
    padding: 1.3rem 1.6rem 1.4rem;
    text-transform: uppercase;
    transition: background-color 0.3s, color 0.3s, border-color 0.3s;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-to-cart-btn:hover {
    background-color: #c00;
    color: #fff;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    gap: 13px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-price-container .garderobo-widget-product-price {
    font-family: 'rubl bold', 'FFDIN Medium';
    font-size: 1.5rem;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    position: relative;
    font-size: 16px;
    font-weight: 400;
    order: 2;
    color: #60706B;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    font-size: 16px;
    font-weight: 400;
    line-height: 100%;
    order: 2;
    color: #0D564D;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-old-price::before {
    position: absolute;
    transform: translateY(-50%);
    top: 50%;
    content: '';
    border-bottom: solid 1px #767676;
    backface-visibility: hidden;
    height: 0;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-product-price {
    font-size: 1rem;
    font-weight: 700;
    color: #012D18;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
    opacity: .5;
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:hover,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:hover {
    opacity: 1;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    color: #616667;
    width: 16px;
    height: 16px;
    content: '';
    display: block;
    position: absolute;
    top: 17px;
    left: 17px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    left: 19px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-left-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(14.000000, 14.000000) rotate(-180.000000) translate(-14.000000, -14.000000) translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group' transform='translate(0.000000, -0.000000)'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    left: 15px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-right-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-product-sale-badge {
    display: none;
    position: absolute;
    top: 15px;
    left: 10px;
    padding: 0.4rem 0.5rem 0.5rem;
    font-size: 1.2rem;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    color: #fff;
    background-color: var(--alert);
}

.garderobo-widget-product-sale-badge::after {
    content: '';
    display: block;
    position: absolute;
    top: 2.1rem;
    left: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 1rem 1rem 0;
    border-color: transparent;
    border-right-color: var(--alert);
}

.garderobo-widget-product-control-dislike {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-sizes {
    background-color: #fff;
    border: solid 0.1rem #eee;
    box-shadow: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items .garderobo-widget-feed-item_inner .garderobo-widget-product-footer .garderobo-widget-sizes:hover {
    border-color: #eee;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-add-all-product-wrap {
    font-size: 1.5rem;
}

#btn-special-look {
    font-size: 12px;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 22%;
    left: 37%;
}

.garderobo-widget-popup-list-item-img {
    position: relative !important;
}

.garderobo-widget-popup-list-item-text-title {
    font-weight: 400;
    line-height: 24px;
    color: #002D18;
    margin-top: 5px;
}

.garderobo-widget-popup-list-item-text-bottom {
    margin-top: 8px;
}

.garderobo-widget-popup-action-buttons, .garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-cart-btn {
    position: absolute;
    left: 0;
    bottom: -68px;
    width: 100%;
}

.garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 21px;
}

.garderobo-widget-popup-list-item-swap-container-item::before {
    display: none;
}

.garderobo-widget-look-product--custom-left-top {
    z-index: 10;
    bottom: 45%;
}

.garderobo-widget-look-product--custom-right-top {
    bottom: 45%;
    background-position-y: bottom;
}

.garderobo-widget-look-product--custom-bottom {
    top: 45%;
    height: 50%;
}

.garderobo-widget-look-product--bag {
    width: 18%;
}

.garderobo-widget-look-product--center-bottom {
    width: 30% !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 30% !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--wear {
    height: 50%;
    width: 30%;
}

.garderobo-widget-look-product--socks {
    height: 16% !important;
    bottom: 23% !important;
    display: block !important;
}

.garderobo-widget-look-product--accessory-glasses {
    width: 14%;
    height: 14%;
    right: 11% !important;
    z-index: 12 !important;
    top: 5%;
}

.garderobo-widget-look-product--accessory-glasses-2 {
    right: 25% !important;
    top: 28%;
}

.garderobo-widget-look-product--right-gloves {
    right: 18% !important;
    top: 53% !important;
    z-index: 12 !important;
}

.garderobo-widget-look-product--accessory-glasses {
    width: 14%;
    height: 14%;
    right: 2%;
    z-index: 12 !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--center-layer-full {
    width: 35%;
    bottom: 0;
    top: auto;
}

.garderobo-widget-look-product--shoes {
    height: 21%;
    left: 14%;
}

.garderobo-widget-popup-content .garderobo-widget-feed-items-counter {
    gap: 23px;
    align-items: flex-end;
}

.garderobo-widget-feed-container:not(.garderobo-widget-feed-item-look) {
    cursor: pointer;
}

.garderobo-widget-feed-container:not(.garderobo-widget-feed-item-look) .duplicate {
    z-index: -1;
}

.garderobo-widget-popup-list-item-swap-container-item--selected .garderobo-widget-popup-list-item-swap-item-price {
    padding: 0;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar,
.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb,
.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #777;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb:hover,
.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb:hover {
    background-color: #121212;
}

.garderobo-widget-popup-list-item-name,
.garderobo-widget-popup-list-item-name-hide {
    display: -webkit-box !important;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    min-height: 31px;
    margin-block: 5px;
    font-size: 10px;
    line-height: 1;
    text-align: center;
    overflow: hidden;
}

.garderobo-widget-popup-list-item-name {
    line-height: 14px;
}

.garderobo-widget-popup-list-item-swap-container-list {
    padding-bottom: 10px;
}

.garderobo-widget-popup-list-item-swap-item-price {
    position: static;
    font-size: 12px;
}

.garderobo-widget-popup-list-item-swap-item-price {
    font-size: 12px;
    line-height: 100%;
    font-weight: 700;
    /* margin: 0; */
    text-decoration: none;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.garderobo-widget-popup-list-item-swap-item-price--sale {
    color: #0D564D;
}

.garderobo-widget-popup-list-item-swap-item-price--old {
    display: block !important;
    line-height: 100%;
    font-weight: 400;
    color: #60706B;
    text-decoration: line-through;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin-left: 0;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    min-height: 73px;
}

.garderobo-multiple-controls {
    display: none;
}

.garderobo-widget-feed-header--with-counter .showItems4 {
    display: flex !important;
}

.garderobo-multiple .garderobo-widget-feed-header--with-counter .showItems4 {
    display: none !important;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 0;
}

.garderobo-widget-popup-list-item {
    padding: 0;
}

.garderobo-widget-popup-container {
    z-index: 39 !important;
    justify-content: end;
    top: unset !important;
}

.garderobo-widget-popup-list-item-text {
    margin: 0 !important;
    width: calc(100% - 195px) !important;
    padding-right: 0 !important;
}


.garderobo-widget-look__badge-special {
    display: none;
}

.garderobo-widget-feed-item--special .garderobo-widget-look-container {
    border: none;
}

.garderobo-widget-look-container, .garderobo-widget-feed-item--special .garderobo-widget-look-container {
    padding: 45px;
}

.garderobo-widget-popup-content .garderobo-widget-look-container {
    margin: 0;
    padding: 50px;
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    padding: 33px 50px 0 50px;
    border-right: none;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    align-self: center;
    height: 100%;
    margin: 0 !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    gap: 15px;
    border: none;
    margin-bottom: 118px;
}

.garderobo-widget-popup-list-item-text-new-price {
    font-size: 16px;
    line-height: 100%;
    font-weight: 700;
    color: #012D18;
    margin: 0;
}
.garderobo-widget-popup-list-item-text-discount {
    font-size: 16px;
    line-height: 100%;
    font-weight: 400;
    color: #60706B;
}

.garderobo-widget-popup-list-item-text-price {
    color: #002D18;
    font-weight: 700;
}

.garderobo-widget-popup-list-content {
    padding: 22px 50px 70px;
}

.garderobo-widget-popup-list-header {
    display: none;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 180px;
    height: 180px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
    border: 1px solid #002D18;
    background-color: #F4F4F4;
    padding: 0;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: auto;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    height: 100%;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 160px;
    object-fit: contain;
    align-self: center;
    padding: 10px;
}

.garderobo-widget-popup-content .garderobo-widget-control-left, .garderobo-widget-popup-content .garderobo-widget-control-right {
    display: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-control-left {
    left: 15px !important;
}

.garderobo-widget-popup-content .garderobo-widget-control-right {
    right: 15px !important;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    width: 100%;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    background: #fff;
    border-radius: 8px;
    opacity: 0.7;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    top: -15px;
    right: 0;
}

.garderobo-widget-sizes {
    width: 100%;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 150px;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: unset;
    height: 100%;
    width: 100%;
    border: none !important;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: none;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    margin-left: -1px;
    margin-top: -1px;
    padding: 0;
    font-size: 9px;
    line-height: 8px;
    border: 1px solid #ccc;
    padding: 2px 4px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
}

.garderobo-widget-feed-items-counter {
    display: flex !important;
    gap: 33px;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-items-counter {
    display: none !important;
}

.garderobo-widget-popup-list-item-swap-button {
    display: block !important;
    width: 160px;
    border-top: 1px solid rgba(0, 45, 24, 1);
    border-bottom: 1px solid rgba(0, 45, 24, 1);
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.12px;
    text-transform: uppercase;
    color: #002D18;
}

.garderobo-widget-feed-items-counter.garderobo-widget-hidden {
    display: none !important;
}

.garderobo-widget-popup__btn-close {
    padding: 8px;
    top: 36px;
    right: 52px;
}

.garderobo-widget-product-badges {
    margin-top: 10px;
}

.garderobo-widget-product-badges-item {
    padding: 0 7px;
    background-color: #292929;
    border-radius: 5px;
    font-size: 11px;
    color: #fff;
    opacity: .6;
    line-height: 23px;
}

.garderobo-widget-popup-container .garderobo-widget-popup-content .garderobo-widget-feed-header, .garderobo-looks-simple-popup .garderobo-widget-popup-list-header {
    display: flex !important;
    margin-bottom: 0;
    font-size: 20px !important;
    line-height: 26px;
    align-items: center;
    justify-content: flex-start;
    padding: 0 0 32px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-header {
    padding: 33px 50px 10px;
    border: none;
    font-weight: 500;
}

.garderobo-widget-popup {
    max-width: 650px;
    height: calc(100dvh - var(--header-desktop-nav-hidden-height)) !important;
    max-height: 100vh !important;
    overflow: auto;
    bottom: 0;
}

.garderobo-widget-sizes-custom-dropdown {
    position: absolute;
    bottom: 0;
    width: calc(100% - 195px);
    border-bottom: 1px solid #F0F0F0
}

.garderobo-widget-sizes-custom-dropdown-selected {
    cursor: pointer;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: #002D18;
    display: flex;
    gap: 10px;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 12px;
}

.garderobo-widget-sizes-custom-dropdown-icon {
    background-image: url('data:image/svg+xml,%3Csvg%20width%3D%226%22%20height%3D%2214%22%20viewBox%3D%220%200%206%2014%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M0.633346%2014L-0.00148634%2013.5132L4.99186%207.00159L-0.00148748%200.49L0.633345%200.00318574L6%207.00159L0.633346%2014Z%22%20fill%3D%22black%22%2F%3E%3C%2Fsvg%3E');
    background-repeat: no-repeat;
    width: 6px;
    height: 14px;
    transition: 0.15s cubic-bezier(1,0.5,0.8,1);
    transform: rotate(90deg);
    margin-right: 4px;
}

.garderobo-widget-sizes-custom-dropdown-icon-rotate {
    transform: rotate(-90deg);
}

.garderobo-widget-sizes-custom-dropdown-items {
    position: absolute;
    top: 28px;
    background-color: #fff;
    border: 1px solid #000;
    z-index: 99;
    width: 100%;
    max-height: 179px;
    overflow-y: auto;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #000;
}

.garderobo-widget-sizes-custom-dropdown-items::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.garderobo-widget-sizes-custom-dropdown-items::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #DADFE9;
}

.garderobo-widget-sizes-custom-dropdown-item {
    padding: 10px 20px;
    cursor: pointer;
}

.garderobo-widget-sizes-custom-dropdown-item:hover {
    background-color: #F6F6F6;
}

.garderobo-widget-sizes-custom-dropdown-item-selected {
    background-color: #F6F6F6;
}

.garderobo-widget-sizes-custom-dropdown-items-hidden {
    display: none;
}

.garderobo-widget-look__bottom-panel {
    height: auto;
    width: 100%;
    padding: 20px 50px;
    border-top: 1px solid #6F6F6F;
    flex-direction: row-reverse;
    justify-content: space-between;
    align-items: center;
    color: #002D18;
    position: fixed;
    width: 631px;
}

.garderobo-widget-look__bottom-panel-info p, .garderobo-widget-look__bottom-panel-info p span {
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
    text-transform: uppercase;
}

.garderobo-widget-look__bottom-panel .garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 0;
    margin-left: 2px;
}

.garderobo-widget-look__bottom-panel > div {
    flex-direction: row;
    align-items: center;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    color: #fff;
    font-weight: 400;
    background: #012D18;
    border: 1px solid #012D18;
    height: 48px;
    width: calc(100% - 181px);
    transition-property: all;
    transition-timing-function: cubic-bezier(.4,0,.2,1);
    transition-duration: .15s;
    margin-top: 0;
}

.garderobo-widget-popup-list-item-text-cart-btn:hover {
    background: rgb(0 73 39 / 1);
}

.garderobo-widget-popup-list-item-text-cart-btn--disabled {
    border: none;
    border-top: 1px solid #6F6F6F;
    border-bottom: 1px solid #6F6F6F;
    background: #C8C8C8;
    height: 48px;
    opacity: 1;
    margin: 0;
}

.garderobo-widget-popup-list-item-text-cart-btn--disabled:hover {
    background: #C8C8C8;
}

.garderobo-set-size-panel-size-buttons {
    display: flex;
    gap: 14px;
    flex-wrap: wrap;
    margin-top: 48px;
}

.garderobo-set-size-panel-size-buttons div {
    background-color: #fff;
    color: #292929;
    border: 1px solid #e5e5e5;
    width: calc(25% - 14px);
    cursor: pointer;
    padding: 15px 0 ;
    text-align: center;
    border-radius: 30px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-content {
    overflow: unset;
    padding-bottom: 0;
}

.garderobo-set-size-panel-size-buttons-active, .garderobo-set-size-panel-size-buttons div:hover {
    background-color: #292929 !important;
    color: #fff !important;
}

.garderobo-looks-simple-popup {
    margin: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-img-v2 {
    object-fit: contain;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    padding: 0;
}

.showItems3 {
    display: none !important;
}

.garderobo-scroll-button {
    background-color: #002D18;
    border-radius: 0;
    font-size: 18px;
    width: auto;
    height: 45px;
    padding: 0 21px;
    font-weight: 600;
    line-height: 27px;
    z-index: 30;
    left: 0;
    top: 50px;
}

.garderobo-scroll-button:hover {
    background-color: #002D18;
    border-radius: 0;
}

.garderobo-scroll-button span {
    display: none !important;
}

/* SIZE PANEL */
.garderobo-set-size-panel {
    position: fixed;
    background: #fff;
    top: 0;
    right: -100vw;
    height: 100vh;
    width: 500px;
    z-index: 999;
    transition: bottom 0.5s ease, right 0.5s ease;
    padding: 0 24px;
}

.garderobo-set-size-panel h2 {
    font-size: 19px;
    margin-top: 14px;
}

.garderobo-set-size-panel-active {
    right: 0;
}

@media only screen and (min-width: 769px) {
    .garderobo-widget-popup-list-content {
        height: calc(100% - 112px);
    }
    
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: initial !important;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 48px;
        line-height: 64px;
    }

    .garderobo-widget-popup-content {
        flex-direction: column;
        height: auto;  
        border: none; 
    }

    .garderobo-widget-popup-collage-container, .garderobo-widget-popup-list-container {
        width: 100%;
    }
}

@media screen and (min-width: 992px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }

    .xl\:col-span-8 .garderobo-widget-container .garderobo-widget-feed header {
        margin-inline: 20px;
    }

    .xl\:col-span-8 .garderobo-widget-container {
        margin-inline: 0;
    }

    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) {
        padding-left: calc((100vw - 15px) / 25);
    }
}



@media only screen and (min-width: 768px) {
    .garderobo-widget-popup {
        max-width: 40.625rem;
        height: 100dvh !important;
    }

    .garderobo-widget-look__bottom-panel {
        max-width: 40.625rem;
    }

    .garderobo-widget-popup-container {
        z-index: 40 !important;
        top: 0 !important;
    }
}

@media only screen and (min-width: 1024px) {
    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
        padding-bottom: 0 !important;
    }
}

@media only screen and (min-width: 1170px) {
    .garderobo-widget-popup {
        max-width: 38.4375rem;
        height: calc(100dvh - var(--header-desktop-nav-hidden-height)) !important;
    }

    .garderobo-widget-look__bottom-panel {
        max-width: 38.4375rem;
    }

    .garderobo-widget-popup-container {
        z-index: 40 !important;
        top: unset !important;
    }
}

@media screen and (min-width: 1200px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -5px;
    }
}

@media only screen and (min-width: 1024px) and (max-width: 1439px) {
    .garderobo-scroll-button {
        width: 100%;
        position: unset;
        display: flex;
    }
}

@media only screen and (min-width: 1200px) and (max-width: 1440px) {
    .garderobo-widget-feed-items-counter {
        margin-left: 30px;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed header {
        font-size: 16px !important;
    }

    .garderobo-widget-feed-items-counter-text {
        font-size: 16px;
    }
}

@media only screen and (max-width: 1200px) {
    .garderobo-widget-popup-collage-container .garderobo-widget-feed header {
        font-size: 14px !important;
    }

    .garderobo-widget-feed-items-counter-text {
        font-size: 14px;
    }
}

@media only screen and (max-width: 1024px) {
    .garderobo-scroll-button {
        display: flex;
        position: unset;
        width: 100%;
    }

    .garderobo-scroll-button:hover {
        width: 100%;
    }
}


@media only screen and (max-width: 768px) {
    .garderobo-set-size-panel {
        bottom: -100vh;
        top: unset;
        left: 0;
        height: 100vh;
        width: 100vw;
    }

    .garderobo-set-size-panel-active {
        bottom: 0;
    }

    .garderobo-widget-look__bottom-panel {
        width: 100%;
        position: fixed;
        bottom: env(safe-area-inset-bottom);
    }

    .garderobo-set-size-panel-size-buttons div {
        width: calc(33.3% - 12px) !important;
    }

    .garderobo-widget-feed-header--with-counter .showItems2 {
        display: flex !important;
    }

    .garderobo-widget-popup-list-item {
        padding: 0;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup {
        height: 100vh !important;
        max-height: 100vh !important;
        width: 100%;
        max-width: unset;
        bottom: unset;
        top: 0;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup.garderobo-looks-simple-popup {
        height: 100vh !important;
    }

    .garderobo-widget-look__bottom-panel {
        max-width: unset;
        width: 100%;
    }

    .garderobo-widget-popup-container .garderobo-widget-popup-content  .garderobo-widget-control-left, .garderobo-widget-popup-container .garderobo-widget-popup-content  .garderobo-widget-control-right {
        display: none !important;
    }

    .garderobo-widget-container .garderobo-widget-feed header, .garderobo-looks-simple-popup .garderobo-widget-popup-list-header {
        font-size: 22px;
        line-height: 64px;
        font-weight: 500;
        margin-bottom: 0;
        padding: 0 16px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-header {
        padding: 0 0 32px;
    }

    .garderobo-widget-popup-list-item-text-cart-btn {
        width: 100%;
    }

    .garderobo-widget-popup-container .garderobo-widget-feed-items-counter.garderobo-widget-hidden {
        display: flex !important;
    }

    .garderobo-widget-popup__btn-close {
        right: 15px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup__btn-close {
        top: 36px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-container {
        padding-bottom: 0;
    }

    .garderobo-widget-look-container, .garderobo-widget-feed-item--special .garderobo-widget-look-container {
        padding: 20px;
    }

    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item,
    .garderobo-widget-feed-similar .garderobo-widget-feed-items .garderobo-widget-feed-item, .garderobo-widget-feed-recommendations .garderobo-widget-feed-items .garderobo-widget-feed-item, .garderobo-widget-feed-popular .garderobo-widget-feed-items .garderobo-widget-feed-item,
    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items:has(.garderobo-widget-feed-item:nth-child(3):last-child) .garderobo-widget-feed-item,
    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed-item {
        flex: 0 0 calc(100% - 128px);
    }

    .garderobo-widget-look__btn-buy {
        font-size: 15px;
    }

    .garderobo-widget-feed-item-look {
        margin-left: 16px;
        margin-right: 0;
    }

    .garderobo-widget-feed-similar .garderobo-widget-feed-container, .garderobo-multiple .garderobo-widget-feed-container {
        margin-left: 16px;
    }

    .garderobo-widget-container .garderobo-widget-feed {
        margin-bottom: 50px !important;
    }

    .garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-similar {
        margin-bottom: 86px !important;
    }

    .garderobo-widget-feed-items-counter, .garderobo-widget-feed-header--with-counter .showItems4 {
        display: none !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
        padding: 0 0 13px;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed-item-look {
        margin: 0;
    }

    .garderobo-widget-popup-list-content {
        padding: 0;
    }

    .garderobo-widget-popup-content {
        padding: 32px 16px 40px;
        border: none;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed-items {
        overflow-x: hidden;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        flex-direction: column;
        gap: 13px;
        margin-bottom: 50px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item:last-child {
        margin-bottom: 150px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item:last-child {
        margin-bottom: 150px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: auto;
        height: auto;
        aspect-ratio: 1 / 1;
        padding: 0;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
        max-height: none;
    }

    .garderobo-widget-popup-list-item-text {
        width: auto !important;
        overflow: unset;
    }

    .garderobo-widget-popup-list-item-text-title {
        font-size: 12px;
        line-height: 20px;
        margin: 0;
    }

    .garderobo-widget-sizes-custom-dropdown {
        position: relative;
        width: 100%;
        margin-bottom: 18px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-sizes-custom-dropdown {
        margin-top: 10px;
    }

    .garderobo-widget-popup-action-buttons, .garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-cart-btn {
        position: unset;
    }

    .garderobo-widget-popup-action-buttons {
        flex-direction: column;
        gap: 17px;
    }

    .garderobo-widget-popup-list-item-text-discount, .garderobo-widget-popup-list-item-text-new-price, .garderobo-widget-popup-list-item-text-price {
        font-size: 12px;
    }

    .garderobo-widget-popup-list-item-text-prices {
        margin-bottom: 0;
    }

    .garderobo-widget-popup-list-item-text-bottom {
        margin: 10px 0;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-bottom {
        margin: 10px 0 0;
    }

    .garderobo-widget-sizes-custom-dropdown-selected {
        font-size: 12px;
        line-height: 24px;
        padding-bottom: 7px;
    }

    .garderobo-widget-popup-content .garderobo-widget-look-container {
        padding: 20px;
    }

    .garderobo-widget-look__bottom-panel {
        flex-direction: column-reverse;
        left: 0;
        padding: 0;
        gap: 0;
    }

    .garderobo-widget-look__bottom-panel .garderobo-widget-popup-list-item-text-cart-btn {
        width: 100%;
        height: 64px;
    }

    .garderobo-widget-look__bottom-panel-info {
        padding: 19px 0;
    }

    .garderobo-scroll-button {
        font-size: 16px;
        line-height: 16px;
        font-weight: 600;
        height: 30px;
        margin-bottom: 24px;
    }

    .garderobo-widget-popup-container {
        z-index: 40 !important;
        top: 0 !important;
        justify-content: unset;
        align-items: unset;
    }

    .garderobo-widget-popup-list-item-swap-button {
        width: fit-content;
        font-size: 15px;
        font-weight: 400;
        line-height: 27px;
        border-top: unset;
        margin: auto;
    }

    .garderobo-widget-popup-list-item-swap-container {
        height: 100%;
    }

    .garderobo-widget-popup-list-item-swap-container-list {
        flex-wrap: wrap;
        overflow-y: auto;
    }

    .garderobo-widget-popup-list-item-swap-container-item {
        max-width: unset;
        width: 50%;
    }

    .garderobo-widget-popup-list-item-swap-container-item:first-child {
        max-width: unset;
    }

    .garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
        transform: scale(1);
        right: 8px;
        top: 0;
    }
}

.garderobo-set-size-panel-close-button {
    color: #000;
    right: 15px;
    top: 15px;
    position: absolute;
    font-size: 26px;
    line-height: 15px;
    z-index: 100;
    padding: 10px;
    cursor: pointer;
    transition: transform 0.1s ease-in-out;
}

.garderobo-set-size-panel-close-button::before {
    content: '';
    width: 2px;
    height: 100%;
    background-color: #333;
    position: absolute;
    top: 0;
    left: 50%;
    transform: rotate(45deg);
}

.garderobo-set-size-panel-close-button::after {
    content: '';
    width: 2px;
    height: 100%;
    background-color: #333;
    position: absolute;
    top: 0;
    left: 50%;
    transform: rotate(-45deg);
}

.garderobo-widget-sizes-custom-button {
    padding: 10px 0;
    margin-bottom: 10px;
    margin-top: 10px;
    width: 100%;
    border-top: 1px solid #d7d7d7;
    border-bottom: 1px solid #d7d7d7;
    cursor: pointer;
    position: relative;
}

.garderobo-widget-sizes-custom-button::after {
    content: "\E927";
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    position: absolute;
    right: 0px;
    font-size: 20px;
}