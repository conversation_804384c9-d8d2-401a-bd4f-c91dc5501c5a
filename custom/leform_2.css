/* LEFORM */
.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed header {
    color: #000;
    white-space: nowrap;
    text-align: left;
    font-family: HelveticaNeueCyr, sans-serif;
    font-weight: 500;
    font-size: 26px;
    margin: 22px 0 38px;
}

.header_sticky {
    position: sticky;
    top: 20px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 16.66%;
    padding: 0 5px;
}

.garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    flex-flow: column wrap;
    justify-content: flex-start;
    height: auto;
    user-select: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    font-family: Formular, sans-serif;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: auto;
    width: 100%;
    padding-top: 150%;
    background-size: contain;
    margin-bottom: 20px;
    background-repeat: no-repeat;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner {
    flex-direction: column;
    flex-wrap: nowrap;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    flex-direction: column !important;
    font-size: 12px;
    align-items: flex-start !important;
    justify-content: space-between;
    width: 100% !important;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    text-align: left;
    font-size: 20px;
    font-weight: 500;
    color: #000;
    letter-spacing: 0.01em;
    width: 100%;
    overflow-wrap: break-word;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name:hover {
    color: rgb(128, 128, 128);
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    display: none;
    /*width: 100%;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-like,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-dislike {
    /*width: 50%;*/
    /*height: 40px;*/
    /*display: inline-block;*/
    /*background-color: gray;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    column-gap: 12px;
    width: 100%;
    font-family: HelveticaNeueCyr, sans-serif;
    font-size: 16px;
    color: #000;
    margin-top: 8px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    text-decoration: line-through;
    color: rgb(128, 128, 128);
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header {
    height: 20px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
    width: 30px;
    height: 30px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: -30px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: -30px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    width: 30px;
    height: 30px;
    content: '';
    display: block;
    position: absolute;
    left: 0;

    font-family: 'slick';
    font-size: 33px;
    font-weight: bold;
    line-height: 1;
    opacity: 0.75;
    color: #000;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    content: '←';
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    content: '→';
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:hover::before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:hover::before {
    opacity: 1;
}

.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-scroll .garderobo-widget-feed-container .garderobo-widget-add-to-cart-btn {
    line-height: 30px;
    text-transform: uppercase;
    text-align: center;
    letter-spacing: 0.1em;
    font-weight: 500;
    cursor: pointer;
    box-sizing: border-box;
    border: none;
    background-color: rgb(0, 0, 0);
    color: rgb(255, 255, 255);
    transition: opacity 0.3s ease 0s;
    width: 100%;
    height: 70px;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-size: 14px;
    padding: 18px 0 14px;
    max-width: 175px;
    position: relative;
}

.garderobo-widget-product-price {
    line-height: 20px;
    letter-spacing: 0.01em;
}

.garderobo-shopthemodellook__image {
    position: sticky;
    top: 90px;
}

@media screen and (max-width: 1440px) {
    .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 20%;
    }
}

@media screen and (max-width: 992px) {
    .header_sticky {
        position: static;
    }
}

@media screen and (max-width: 901px) {
    .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33.3%;
    }
}

@media screen and (max-width: 770px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right,
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        display: none;
    }
}

@media screen and (max-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 24px;
    }
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed {
        justify-content: center;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -10px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -10px;
    }

    .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33.3%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: space-between;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        font-size: 14px;
        font-weight: 500;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
        font-size: 12px;
    }
}

@media screen and (max-width: 760px) {
    .garderobo-widget-container .garderobo-widget-feed {
        padding: 0px 20px;
    }
}

@media screen and (max-width: 600px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 16px;
    }
}

@media screen and (max-width: 425px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33.3%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -18px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -18px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: space-between;
    }
}