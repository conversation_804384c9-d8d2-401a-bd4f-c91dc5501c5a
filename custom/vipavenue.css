/* KENGURU */
.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 20px;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 24px;
    font-weight: 600;
    color: #000;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: calc(100% - 75px);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 20%;
    border: 1px solid white;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    height: auto;
    align-content: flex-start;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item:hover {
    border: 1px solid #d9d9d9;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header-content {
    margin: 0 4.5% 10px;
    padding: 5px;
    border-bottom: 2px #ccc solid;
    text-align: center;
    font-size: 14px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 240px;
    width: 91%;
    padding: 20px 10px 0;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-y: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: auto;
    flex-direction: column !important;
    padding: 10px 10px 20px 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: block;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    margin: 5px 0 !important;
    line-height: 16px;
    font-size: 12px;
    color: #241f1f;
    text-align: center;
    border: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    display: none;
    /*width: 100%;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-like,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-dislike {
    /*width: 50%;*/
    /*height: 40px;*/
    /*display: inline-block;*/
    /*background-color: gray;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    padding: 0 4px;
    color: #241f1f;
    font-size: 12px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price+.garderobo-widget-product-price {
    text-transform: uppercase;
    font-size: 12px;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    font-size: 10px;
    padding: 0 2px 0 5px;
    order: 2;
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    order: 3;
    text-decoration: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    text-indent: -99999px;
    display: block;
    width: 13px !important;
    height: 22px !important;
    transform: translate(0, -50%);
    cursor: pointer;
    color: transparent;
    border: 0;
    outline: 0;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAsBAMAAABMNS/cAAAAMFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABHcEx/v/MDAAAAEHRSTlP/+1dHd19n94T1o09tQAMAS5BP0wAAAKRJREFUGNNjeKX/Hwg+MCwQhdCBjPFg+pMAG5j+r8iQD6Y/CfCA6f8bGfzB9OcCFjD935zBH0x/nsABpv/fZGhApmHiUHUwfVBzYOZC7YHZC3MHzF0wd4LI/1oPwPRXgQVgOpAxAER/YxACyycw6oPoPwxCYPUOjPtB9BeGYrB+B3Z7EP2DYTLYvAbO+yg0TBymDq4PZg7cXJg9cHth7oC7C+hOAJlK4MmSzflFAAAAAElFTkSuQmCC');
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
    background-position: 0 -22px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-popup-content .garderobo-widget-control-right {
    right: 15px !important;
}

.garderobo-widget-popup-content .garderobo-widget-control-left {
    left: 15px !important;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    content: '';
    font-family: slick;
    font-size: 20px;
    line-height: 1;
    opacity: 0.75;
    color: #fff;
    -webkit-font-smoothing: antialiased;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    content: '';
    font-family: slick;
    font-size: 20px;
    line-height: 1;
    opacity: 0.75;
    color: #fff;
    -webkit-font-smoothing: antialiased;
}

.garderobo-widget-look-product--left-top.garderobo-widget-look-product--layer-1_top,
.garderobo-widget-look-product--right-top.garderobo-widget-look-product--layer-1_top {
    height: 30%;
    width: 25%;
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed {
        margin-bottom: 0;
    }

    .garderobo-widget-container .garderobo-widget-feed {
        justify-content: center;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 400px;
        width: 100%;
        max-width: 240px;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 17px;
        margin-top: 24px;
        margin-bottom: 0;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        line-height: 13px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
        flex-wrap: wrap;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
        text-transform: none;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        /* background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg id='Стрелка_xA0_Изображение_1_' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 110.4 173.6'%3e%3cswitch%3e%3cg%3e%3cpath fill='%239D9D9D' d='M89.9,162c-3.5-3.2-82.1-75.3-85.4-77.7c-0.8-0.6-0.7-1.5,0-2.3C7,79.5,64.1,27.8,89.2,4.9 c0.2-0.2,0.4-0.3,0.7-0.6c2.9,3.2,5.7,6.3,8.7,9.6C73.2,37,47.9,60,22.5,83.1c25.4,23.1,50.7,46.1,76.1,69.2 C95.7,155.6,92.8,158.7,89.9,162z'/%3e%3c/g%3e%3c/switch%3e%3c/svg%3e"); */
        background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMzQiIHZpZXdCb3g9IjAgMCAyMCAzNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIKICAgICAgICBkPSJNMTkuMTgxNiAxN0wzLjMzOTcxIDMyLjkzNjhDMi43NTcwNCAzMy41MjMgMS44MDg2MyAzMy41MjMgMS4yMjU5NiAzMi45MzY4QzAuNjQ4MTk0IDMyLjM1NTYgMC42NDgxOTIgMzEuNDE2OSAxLjIyNTk2IDMwLjgzNTZMMTQuOTc5MiAxN0wxLjIyNTk3IDMuMTY0MzZDMC42NDgxOTUgMi41ODMxMyAwLjY0ODE5NSAxLjY0NDQzIDEuMjI1OTcgMS4wNjMyQzEuODA4NjQgMC40NzcwMzkgMi43NTcwNCAwLjQ3NzA0IDMuMzM5NzEgMS4wNjMyTDE5LjE4MTYgMTdaIgogICAgICAgIGZpbGw9IiMyMjIyMjIiLz4KPC9zdmc+Cg==);
        background-position: unset;
        transform: unset;
        background-size: cover;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        transform: rotate(180deg);
    }
}

@media screen and (max-width: 425px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: calc(100% - 20px);
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 165px;
    }
}

.garderobo-widget-look-product--shoes {
    height: 20%;
}

.garderobo-widget-look-product--bag {
    width: 21%;
    height: 21%;
}

.garderobo-widget-look-product--left-center-bag {
    top: 54%;
}

.garderobo-widget-look-product--gloves {
    width: 12%;
    height: 20%;
    top: 30%;
    background-position-y: bottom;
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
        width: 32% !important;
    }
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    /*border: 1px solid #eee !important;*/
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    border: none !important;
    width: 100% !important;
    height: 100%;
    padding-bottom: 0 !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 20px !important;
    max-height: 100%;
    width: auto;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed {
    margin: 0 !important;
}

.garderobo-widget-look-container {
    padding: 7px;
}

.garderobo-widget-look-container div[data-product-type="gloves"] {
    display: none;
}

.garderobo-widget-look-container div[data-product-type="socks"] {
    display: none;
}

.garderobo-widget-look-container div[data-product-type="accessory"] {
    display: none;
}

.garderobo-widget-look-container div[data-product-type="belt"] {
    display: none;
}

.garderobo-grid-look__product_img_with_positions {
    display: block !important;
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        border: none !important;
    }
}

.garderobo-grid-look-2 p {
    z-index: 50;
    text-transform: uppercase;
    line-height: 11px;
    font-size: 10px;
    display: block;
    align-items: center; /* Выравнивание по вертикали по центру */
    flex-wrap: wrap;
    gap: 0;
    text-align: center;
    opacity: 0.7;
    border-radius: 10px;
    font-family: "Jost",sans-serif;
}

.garderobo-grid-look-2 p strong {
    width: 100%;
    display: block;
}

.garderobo-main-block header {display: none;}
.garderobo-main-block .garderobo-widget-look__label-look-name {display: block !important;
    position: absolute;
    bottom: -40px;
    font-family: Roboto-medium;
    font-size: 18px;
}

.garderobo-main-block .garderobo-widget-look__label-look-link {display: block !important;
    position: absolute;
    transform: translate(-50%);
    left: 200px !important;
}

.garderobo-widget-popup .garderobo-widget-look__label-look-name, .garderobo-widget-popup .garderobo-widget-look__label-look-link {display: none !important;}
.garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {width: calc(100% - 100px);}
.garderobo-main-block .garderobo-widget-look__btn-buy, .garderobo-main-block .garderobo-widget-look__label-look-link {
    border: none; border-bottom: 1px solid #212529;
    font-weight: normal; padding: 0;
    left: 30px;
    font-size: 16px;
    letter-spacing: 2px;
    font-family: Roboto-light;
    text-transform: lowercase;
    padding-bottom: 4px;
    color: #212529;
    bottom: -70px;
}

.garderobo-main-block {height: unset;}
.garderobo-main-block .garderobo-widget-look__btn-buy:hover {background: none;}
.garderobo-main-block .garderobo-widget-popup-list-item-img, .garderobo-main-block .garderobo-widget-popup-list-item-swap-item-img {width: auto !important;}
.garderobo-main-block .garderobo-widget-feed {margin-bottom: 0;}
.garderobo-main-block .garderobo-widget-feed-item-look {margin-top: 0;}
.garderobo-main-block .garderobo-widget-feed-item-look .garderobo-widget-feed-item {padding-bottom: 98% !important; width: 100% !important;}

.garderobo-widget-popup {
    padding: 20px;
}

@media screen and (min-width: 1024px) {
    .garderobo-main-block .garderobo-widget-popup {
        max-width: 75%;
        min-height: 600px;
    }
}

@media screen and (max-width: 768px) {
    .garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-main-block .garderobo-widget-look__label-look-name {display: block !important;
        position: absolute;
        bottom: -40px;
        font-family: Roboto-regular;
        font-size: 16px;
    }

    .garderobo-main-block .garderobo-widget-look__btn-buy, .garderobo-main-block .garderobo-widget-look__label-look-link {
        left: 22px;
        font-size: 14px;
        font-family: Roboto-light;
        letter-spacing: unset;
    }

    .garderobo-main-block .garderobo-widget-look__label-look-link {display: block !important;
        left: 140px !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-look__label-look-name, .garderobo-widget-popup-content .garderobo-widget-look__label-look-link {display: none !important;}
}

.garderobo-widget-sizes-buttons-wrapper {
    display: block;
}

.garderobo-widget-sizes-buttons {
    display: none;
    flex-wrap: wrap;
    margin: 12px 0;
}

.garderobo-widget-sizes-buttons div {
    border: 1px solid #000;
    padding: 8px 12px;
    cursor: pointer;
    margin-right: -1px;
    margin-top: -1px;
}

.garderobo-widget-popup-list-item-swap-button {
    display: block !important;
    border: 1px solid #BABBC1;
    transition: all .2s linear;
}

.garderobo-widget-popup-list-item-swap-button:hover {
    background: #000;
    color: #fff;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    background: #222 !important;
    color: #fff !important;
    margin-bottom: 10px !important;
    border-color: #222;
    font-size: 16px;
    letter-spacing: .5px;
    line-height: 17px;
    transition: all .2s linear;
}

.garderobo-widget-popup-list-item-text-cart-btn:hover {
    background: #000;
}

.garderobo-widget-popup-list-item-text-cart-btn::before {
    content: "Добавить ";
}

.garderobo-widget-popup-list-item-text-cart-btn-link-state::before {
    content: "";
}


.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-sizes {
    width: 100%;
    border: 1px solid #EAEAEA;
    border-radius: 3px;
    font-size: 18px !important;
    line-height: 20px;
    font-weight: 400;
    margin-right: 0 !important;
    height: 44px;
    padding: 10px !important;
    position: relative;
    margin-bottom: 10px;
}

.garderobo-widget-popup-actions-controls::before {
    content: "Размер";
    font-family: Jost;
    font-size: 16px;
    font-weight: 400;
    line-height: 23px;
    letter-spacing: 0px;
    text-align: left;
    margin-bottom: 12px;
}

.garderobo-widget-popup-actions-controls button, .garderobo-widget-popup-list-item-swap-button {
    width: 100%;
    height: 48px;
    padding: 0;
    text-transform: uppercase;
    border-radius: 3px;
    font-family: Jost;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    margin: 0;
    text-align: center;
    cursor: pointer;
}

.garderobo-widget-popup-list-item-bottom-block {
    display: flex;
    flex-direction: column-reverse;
}

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 0;
    font-size: 20px;
    font-weight: 300;
    line-height: 26px;
    letter-spacing: 1px;
    text-transform: uppercase !important;
}

.garderobo-widget-popup-list-item-text-new-price {
    font-size: 20px;
    font-weight: 500;
    line-height: 100%;
    color: #222;
    order: 2;
}

.garderobo-widget-popup-list-item-text-discount {
    font-size: 18px;
    font-weight: 500;
    line-height: 18px;
    color: #838383;
    margin-right: 8px;
}

.garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 24px;
    margin-top: 10px;
    display: flex;
}

.garderobo-widget-sizes-button-active {
    background-color: #bbb;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-swap-container .garderobo-widget-popup-list-item-swap-back-button {
    display: block !important;
    position: fixed;
    background: white;
    margin-top: -22px;
    cursor: pointer;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: fit-content;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    width: calc(33% - 20px);
}

.garderobo-widget-popup-list-item-swap-container {
    height: unset;
    padding-bottom: 15px;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    height: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 50px;
    margin-top: 12px;
}

.garderobo-widget-popup-list-item-swap-container-list {
    gap: 25px;
    overflow-x: unset;
    display: flex;
    flex-wrap: wrap;
}


.garderobo-widget-popup-list-item-swap-container-item--not-available {
    position: relative;
}

.garderobo-widget-popup-list-item-swap-container-item--not-available::after {
    content: "Данный товар закончился, но у нас есть похожее";
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    color: #000;
    display: flex;
    padding: 0 17px;
    text-transform: uppercase;
    text-align: center;
    padding-top: 75px;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 0 !important;
    position: relative;
    width: 100%;
    border: none !important;
    justify-content: start;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: flex !important;
    align-items: flex-start;
    width: 100%;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    row-gap: 6px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes::before {
    content: 'Размеры:';
    display: block;
    font-size: 13px;
    font-weight: 400;
    letter-spacing: .5px;
    line-height: 20px;
    color: #222;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    font-size: 13px;
    font-weight: 400;
}

.garderobo-widget-feed-header {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    padding: 0;
    height: 154px;
}

.garderobo-widget-popup-list-item-swap-item-img {
    max-height: 200px;
}

.garderobo-widget-popup-list-item-brand {
    width: 100%;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 500 !important;
    line-height: 20px;
    letter-spacing: .5px;
    line-height: 21px;
    text-transform: uppercase;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block !important;
    margin-top: 10px !important;
    margin-bottom: 5px;
}

.garderobo-widget-popup-list-item-text-brand {
    display: none !important;
    margin: 0 !important;
    font-weight: 500 !important;
    text-transform: uppercase;
    font-size: 20px;
    line-height: 28px;
}

.garderobo-widget-popup-list-item-swap-item-price {
    font-size: 13px;
    font-weight: 400;
    line-height: 19px;
    letter-spacing: 0.5px;
    color: #222 !important;
    text-decoration: none;
    padding: 0;
    text-align: left;
    display: flex;
    white-space: nowrap;
    gap: 10px;
    flex-flow: row-reverse;
    justify-content: flex-end;
}

.garderobo-widget-popup-list-item-swap-container-item--selected .garderobo-widget-popup-list-item-swap-item-price {
    padding: 0;
}

.garderobo-widget-popup-list-item-swap-item-price--sale .garderobo-widget-popup-list-item-swap-item-price--old {
    display: block !important;
    text-decoration: line-through;
    color: #838383;
}

.garderobo-widget-popup-list-item-swap-item-price {
    position: unset;
    margin-bottom: 5px;
}

.garderobo-widget-popup-collage-container {
    padding: 48px 0;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}

.garderobo-widget-feed-item {
    padding: 0 !important;
    margin: 0 !important;
    height: 100%;
}

.garderobo-widget-feed-item-min-width {
    padding: 0 !important;
    margin: 0 !important;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
    height: 100%;
}

.garderobo-widget-feed-container {
    align-items: center;
}

.garderobo-widget-feed-items {
    height: 100%;
}

.garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1/1;
    width: 100%;
    max-width: 100%;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    align-self: center;
    height: 100%;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 24px 12px;
    width: unset;
    gap: 24px;
    border-bottom: 1px solid #e0e0e0;
    border-top: 0;
}

.garderobo-widget-popup-list-item:first-child {
    border-top: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 300px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    background: #fff;
    border-radius: 8px;
    opacity: 0.7;
}

.look-loading-state::after {
    content: "Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 1;
}

.garderobo-grid-look__product_img_with_positions {
    position: relative;
}

.garderobo-grid-look__product-disabled-layout img {
    opacity: 0.2;
}

.garderobo-grid-look__product-disabled-layout::before {
    content: "Нет в наличии";
    display: flex;
    position: absolute;
    justify-content: center;
    height: 100%;
    width: 50%;
    left: 25%;
    text-transform: uppercase;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    text-align: center;
}

@media screen and (min-width: 767px) {
    .garderobo-widget-popup {
        height: 80vh !important;
        max-width: 1300px;
        max-height: 1000px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 50%;
        padding-top: 20px;
        height: calc(100% + 20px);
    }

    .garderobo-widget-popup-list-content {
        padding: 0 40px !important;
    }
}

@media screen and (max-width: 766px) {
    .garderobo-widget-popup-collage-container .garderobo-widget-feed-container {
        height: 80vw;
    }
}

/* PLATFORM */
.modatech-look-widgets img {
    cursor: pointer;
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.modatech-article-content .modatech-look-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(calc(50% - 12px), 1fr));
}

.modatech-article-content .modatech-look-widget-container {
    max-width: unset;
}

.modatech-look-widgets {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 24px;
}

.collage .modatech-look-widgets {
    height: 100%;
    position: relative;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    flex-wrap: nowrap !important;
    gap: 0 !important;
    justify-content: unset !important;
}

.modatech-look-widget-container {
    flex: calc(33% - 24px);
    max-width: calc(33% - 12px);
    border: 1px solid #BABBC1;
}

.modatech-look-widget-container {
    position: relative;
    padding: 20px;
}

.collage {
    max-width: 850px !important;
}

.collage .modatech-look-widgets .modatech-look-widget-container {
    flex: 0 0 auto !important;
    max-width: unset !important;
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    transition: transform 0.5s ease;
    border: none;
    padding: 0;
}

.collage .modatech-look-widget-container > img {
    height: 100%;
}

.modatech-platform-overlay {
    display: flex;
    position: absolute;
    width: 100%;
    height: 100%;
    background: #000;
    top: 0;
    left: 0;
    align-items: center;
    opacity: 0.2;
}

.modatech-look-widgets-thumbnails {
    display: flex !important;
    gap: 8px;
    position: absolute;
    bottom: 0;
    right: 50%;
    transform: translateX(  50%);
}

.modatech-look-widgets-thumbnails-item {
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background-color: #BABBC1;
    cursor: pointer;
}

.modatech-look-widgets .garderobo-widget-popup-content .garderobo-widget-feed-item-look .garderobo-widget-feed-items {
    gap: 10px !important;
    -webkit-scroll-snap-type: x mandatory;
    -moz-scroll-snap-type: x mandatory;
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory;
    -webkit-scroll-behavior: smooth;
    -moz-scroll-behavior: smooth;
    -ms-scroll-behavior: smooth;
    scroll-behavior: smooth;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
}

.modatech-look-widgets .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    border: none !important;
    padding-bottom: 0px !important;
    -webkit-scroll-snap-align: start;
    -moz-scroll-snap-align: start;
    -ms-scroll-snap-align: start;
    scroll-snap-align: start;
    -webkit-scroll-snap-stop: normal;
    -moz-scroll-snap-stop: normal;
    -ms-scroll-snap-stop: normal;
    scroll-snap-stop: normal;
}

.modatech-look-widgets-thumbnails-item.active {
    background-color: #222;
}

.modatech-platform-overlay p {
    color: #fff;
    padding-left: 10px;
    font-size: 14px;
    text-align: center;
}

.modatech-platform-loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #000;
    border-radius: 50%;
    min-width: 30px;
    min-height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.garderobo-widget-wrapper {}

.modatech-platform-look-like-state-button, .garderobo-widget-popup-list-item-like-button, .garderobo-widget-popup-list-swap-item-like-button {
    width: 20px;
    height: 17px;
    position: absolute;
    right: 0px;
    top: 16px;
    cursor: pointer;
    background: url('https://static.modatech.ru/images/ico-like-bordered.svg') no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
    z-index: 1;
}

.garderobo-widget-popup-list-swap-item-like-button {
    top: 11px;
}

.garderobo-widget-popup-list-header {
    border-bottom: none;
    display: none;
}

.garderobo-widget-popup-list-item-not-available {
    opacity: 1;
}

.modatech-platform-look-like-state-button:hover, .garderobo-widget-popup-list-swap-item-like-button:hover, .modatech-platform-look-like-state-button-liked, .garderobo-like-button-liked-state, .garderobo-widget-popup-list-item-like-button:hover {
    width: 20px;
    height: 17px;
    background: url('https://static.modatech.ru/images/ico-like-filled.svg') no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
}

/*
.modatech-platform-look-like-state-button-liked:hover {
    filter: brightness(0.8) hue-rotate(50deg);
}
*/


/* ARTICLE */
.modatech-platform {
    font-family: Jost;
    font-weight: 300;
    color: #222;
}

.modatech-platform h1 {
    font-weight: 300;
    font-size: 48px;
    line-height: 69px;
    text-align: center;
}

.modatech-platform p {
    font-weight: 300;
}

.modatech-article-header {
    display: flex;
    gap: 100px;
    padding-bottom: 36px;
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 40px;
    justify-content: flex-end;
}

.modatech-article-title__info {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    text-align: center;
}

.modatech-article-header h1 {
    padding-bottom: 30px;
    position: relative;
    margin-bottom: 12px;
}

.modatech-article-header h1::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: #222;
}

.modatech-article-title__categories {
    font-size: 14px;
    line-height: 20px;
    text-transform: uppercase;
}

.modatech-article-title__author {
    font-size: 14px;
    font-weight: 600 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    margin-bottom: 6px;
}

.modatech-article-title__social-share {
    height: 25px;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 36px;
    margin-top: 70px;
}

.modatech-article-title__social-share a {
    display: block;
    align-self: center;
    cursor: pointer;
}

.modatech-article-title__image {
    flex: 0 0 calc(50% - 50px);
    aspect-ratio: 1/1;
}

.modatech-article-title__image img {
    width: 100%;
    height: unset;
    object-fit: cover;
    aspect-ratio: 1/1;
}

.modatech-article-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    gap: 60px;
}

.modatech-article-content {
    max-width: 670px;
    flex: 1;
    margin: 0 auto;
    font-size: 18px;
    line-height: 31px;
}

.modatech-article-content img {
    max-width: 100%;
}

.modatech-article-excerpt {
    font-style: italic;
    margin-top: 0 !important;
}

.modatech-article-content h2 {
    font-weight: 400;
    font-size: 32px;
    line-height: 54px;
    margin-top: 40px;
}

.modatech-article-content h3 {
    font-size: 20px;
    line-height: 28px;
    margin-bottom: 8px;
    font-weight: 300;
    text-transform: uppercase;
}

.modatech-article-content p {
    margin-bottom: 12px;
    margin-top: 8px;
}

.modatech-article-sidebar {
    min-width: 400px;
    max-width: 400px;
}

.modatech-article-sidebar-content {
    border-top: 1px solid #222;
    border-bottom: 1px solid #222;
    position: sticky;
    top: 87px;
    padding: 19px 0;
}

.modatech-article-sidebar-content h2 {
    font-size: 32px;
    font-weight: 300;
    line-height: 46px;
    margin-bottom: 36px;
}

.modatech-articles-list {
    display: flex;
    flex-wrap: wrap;
    gap: 36px;
}

.modatech-articles-list-item {
    display: flex;
    gap: 64px;
    position: relative;
    width: 100%;
    padding-bottom: 36px;
    border-bottom: 1px solid #E0E0E0
}

.modatech-related-articles-list .modatech-articles-list-item {
    gap: 24px;
}

.modatech-related-articles-list .modatech-articles-list-item-excerpt {
    display: none;
}

.modatech-related-articles-list .modatech-articles-list-item {
    padding-bottom: 0;
    border-bottom: none;
}

.modatech-related-articles-list {
    gap: 36px;
}

.modatech-articles-list-item:hover h3 {
    color: #aa7e5b;
}

.modatech-articles-list-item-categories {
    font-size: 12px;
    line-height: 17px;
    margin-bottom: 12px;
    text-transform: uppercase;
}

.modatech-articles-list-item h3 {
    font-size: 18px;
    font-weight: 400;
    line-height: 26px;
    position: relative;
}

.modatech-related-articles-list .modatech-articles-list-item h3 {
    font-weight: 300;
}

.modatech-articles-list-item img {
    width: 322px;
    min-width: 322px;
    height: 322px;
    object-fit: cover;
}

.modatech-related-articles-list img {
    width: 80px;
    min-width: 80px;
    height: 80px;
}

.modatech-articles-list-item-info {
    flex: 1;
    align-self: center;
}

.modatech-articles-list-item-info-footer {
    display: flex;
    flex-wrap: nowrap;
    gap: 14px;
    font-size: 12px !important;
    line-height: 17px !important;
    padding-top: 8px;
    margin-top: 24px;
    position: relative;
}

.modatech-articles-list-item-author {
    font-weight: 500;
    text-transform: uppercase;
}

.modatech-articles-list-item-info-footer::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 44px;
    height: 1px;
    background-color: #222;
}

.modatech-articles-list-item-author::after {
    content: "\2022";
    font-weight: 900;
    margin-left: 10px;
}

.modatech-articles-list-item-excerpt {
    margin-top: 12px;
    font-size: 17px;
    line-height: 30px;
    font-weight: 300;
}

/* BLOGGERS */

.modatech-bloggers-list {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.modatech-bloggers-list-item {
    flex: 1 0 calc(25% - 24px);
    max-width: calc(25% - 12px);
    margin-bottom: 96px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    color: #222;
}

.modatech-bloggers-list-item-info {
    color: #222;
    text-decoration: none;
}

.modatech-bloggers-list-item img {
    width: 100%;
}

.modatech-subscribe-button {
    margin-top: 24px;
    text-align: center;
    padding: 16px;
    width: 100%;
    text-transform: uppercase;
    border: 1px solid #222;
    border-radius: 24px;
    height: 48px;
    align-self: flex-end;
    max-width: 320px;
    cursor: pointer;
}

.modatech-subscribe-button:hover {
    background: #222;
    color: #fff;
}

.modatech-blogger-header {
    display: flex;
    gap: 75px;
    padding-bottom: 36px;
    border-bottom: 1px solid #E0E0E0
}

.modatech-blogger-header .modatech-article-title__social-share {
    justify-content: left;
}

.modatech-blogger-header__image {
    flex: 1 0 calc(50%);
    aspect-ratio: 1/1;
}

.modatech-blogger-header__image img {
    width: 100%;
    height: unset;
    object-fit: cover;
}

.modatech-blogger-header__texts h1 {
    text-align: left;
}

.modatech-blogger-header__texts {
    align-self: flex-end;
}

.modatech-blogger-header__info {
    display: flex;
    flex-wrap: wrap;
}

.modatech-subscribe-button-hide-state {
    display: none;
}

.modatech-article-title__social-share {
    align-self: flex-end;
}

.modatech-blogger-title, .modatech-blogger-header__title {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 1px;
    margin-top: 12px;
}

.modatech-bloggers-list-item h2 {
    font-size: 24px;
    font-weight: 400;
    line-height: 35px;
    margin-top: 12px;
    margin-bottom: 4px;
}

.modatech-blogger-bio {
    font-size: 14px;
    font-weight: 300;
    line-height: 20px;
}

.modatech-blogger-header__bio {
    font-size: 18px;
    line-height: 26px;
    margin-top: 24px;
}

.modatech-blogger-looks {
    padding: 36px 0;
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 36px;
}

.modatech-blogger-looks h2 {
    font-size: 24px;
    font-weight: 400;
    line-height: 35px;
    letter-spacing: 1.5px;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 36px;
}

.modatech-button-next-page {
    border: 1px solid #222;
    padding: 12px 74px;
    text-transform: uppercase;
    font-weight: 400;
    font-size: 13px;
    line-height: 17px;
    margin: 0 auto;
    cursor: pointer;
}

.modatech-button-next-page:hover {
    background: #222;
    color: #fff;
}

.modatech-lk-bloggers-list .modatech-bloggers-list-item {
    flex: 1 0 calc(33% - 24px);
    max-width: calc(33% - 12px);
}

.modatech-lk-bloggers-list {
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 36px;
}

.modatech-lk-look-widgets {
    justify-content: left;
    margin-bottom: 36px;
}

.garderobo-widget-popup-list-item-swap-item-discount, .garderobo-widget-popup-list-item-discount {
    display: block !important;
    position: absolute;
    left: 0;
    top: 0;
    font-size: 12px;
    line-height: 14px;
    font-weight: 400;
    color: #E00018;
}

.garderobo-widget-popup-list-item-swap-item-discount::before, .garderobo-widget-popup-list-item-discount::before {
    content: "–";
}

.garderobo-widget-popup-list-item-discount {
    font-size: 14px;
    left: 10px;
    top: 10px;
    background: #fff;
    padding: 2px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
}

@media (max-width: 992px) {
    .modatech-article-container {
        flex-wrap: wrap;
    }

    .modatech-article-content {
        max-width: unset;
        flex: 100%;
    }

    .modatech-article-sidebar {
        max-width: unset;
        min-width: unset;
    }

    .modatech-bloggers-list-item {
        flex: 1 0 calc(33% - 24px);
        max-width: calc(33% - 12px);
        margin-bottom: 48px;
    }

    .modatech-lk-bloggers-list .modatech-bloggers-list-item {
        flex: 1 0 calc(50% - 24px);
        max-width: calc(50% - 12px);
    }

    .modatech-lk-articles-list .modatech-articles-list-item img {
        width: 250px;
        min-width: 250px;
        height: 250px;
    }

    .modatech-lk-articles-list .modatech-articles-list-item {
        gap: 36px;
    }
}

@media (max-width: 900px) {
    .modatech-article-header, .modatech-blogger-header {
        flex-wrap: wrap;
        gap: unset;
        row-gap: 24px;
    }

    .modatech-article-title__image {
        flex: 100%;
    }

    .modatech-bloggers-list-item {
        flex: 1 0 calc(50% - 24px);
        max-width: calc(50% - 12px);
    }

    .modatech-look-widget-container {
        flex: calc(50% - 36px);
        max-width: calc(50% - 18px);
        border: 1px solid #BABBC1;
    }
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed {
    border: none;
    border-right: 1px solid #E0E0E0;
    margin: 0 !important;
    height: 100%;
}

.garderobo-widget-popup-content {
    border: none !important;
}

.platform-widget-control-left, .platform-widget-control-right {
    display: block;
    width: 20px;
    height: 34px;
    cursor: pointer;
    position: absolute;
}

.platform-widget-control-left {
    left: 0;
    top: 50%;
    transform: rotate(180deg) translateY(50%);
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMzQiIHZpZXdCb3g9IjAgMCAyMCAzNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIKICAgICAgICBkPSJNMTkuMTgxNiAxN0wzLjMzOTcxIDMyLjkzNjhDMi43NTcwNCAzMy41MjMgMS44MDg2MyAzMy41MjMgMS4yMjU5NiAzMi45MzY4QzAuNjQ4MTk0IDMyLjM1NTYgMC42NDgxOTIgMzEuNDE2OSAxLjIyNTk2IDMwLjgzNTZMMTQuOTc5MiAxN0wxLjIyNTk3IDMuMTY0MzZDMC42NDgxOTUgMi41ODMxMyAwLjY0ODE5NSAxLjY0NDQzIDEuMjI1OTcgMS4wNjMyQzEuODA4NjQgMC40NzcwMzkgMi43NTcwNCAwLjQ3NzA0IDMuMzM5NzEgMS4wNjMyTDE5LjE4MTYgMTdaIgogICAgICAgIGZpbGw9IiMyMjIyMjIiLz4KPC9zdmc+Cg==);
}

.platform-widget-control-right {
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMzQiIHZpZXdCb3g9IjAgMCAyMCAzNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIKICAgICAgICBkPSJNMTkuMTgxNiAxN0wzLjMzOTcxIDMyLjkzNjhDMi43NTcwNCAzMy41MjMgMS44MDg2MyAzMy41MjMgMS4yMjU5NiAzMi45MzY4QzAuNjQ4MTk0IDMyLjM1NTYgMC42NDgxOTIgMzEuNDE2OSAxLjIyNTk2IDMwLjgzNTZMMTQuOTc5MiAxN0wxLjIyNTk3IDMuMTY0MzZDMC42NDgxOTUgMi41ODMxMyAwLjY0ODE5NSAxLjY0NDQzIDEuMjI1OTcgMS4wNjMyQzEuODA4NjQgMC40NzcwMzkgMi43NTcwNCAwLjQ3NzA0IDMuMzM5NzEgMS4wNjMyTDE5LjE4MTYgMTdaIgogICAgICAgIGZpbGw9IiMyMjIyMjIiLz4KPC9zdmc+Cg==);
}

@media (max-width: 576px) {
    .modatech-platform h1 {
        font-size: 36px;
        line-height: 52px;
    }

    .modatech-article-title__social-share {
        margin-top: 40px;
    }

    .modatech-article-content p {
        font-size: 14px;
        line-height: 22px;
    }

    .modatech-bloggers-list-item {
        flex: 100%;
        max-width: unset;
    }

    .modatech-look-widget-container {
        flex: 100%;
        max-width: unset;
    }

    .modatech-articles-list-item {
        gap: 20px;
        flex-wrap: wrap;
    }

    .modatech-articles-list-item img, .modatech-lk-articles-list .modatech-articles-list-item img {
        width: 100%;
        min-width: unset;
        height: 100%;
        aspect-ratio: 1/1;
    }

    .garderobo-widget-popup {
        max-height: 100vh;
        padding: 10px;
    }

    .garderobo-widget-sizes {
        height: 40px;
        font-size: 14px !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
        max-height: 200px;
    }

    .garderobo-widget-popup-list-item-like-button {
        right: 0;
    }

    .modatech-lk-bloggers-list .modatech-bloggers-list-item {
        flex: 1 0 calc(100% - 24px);
        max-width: calc(100% - 12px);
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: 140px;
    }

    .modatech-related-articles-list img {
        width: 80px !important;
        min-width: 80px !important;
        height: 80px !important;
    }

    .modatech-article-content .modatech-look-widgets {
        grid-template-columns: repeat(auto-fit, minmax(100%, 1fr));
    }

    .modatech-subscribe-button {
        max-width: unset;
    }

    .garderobo-widget-popup-list-content {
        margin-right: 10px !important;
        margin-bottom: 10px !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        border: none !important;
        margin-left: 0 !important;
    }

    .garderobo-widget-popup-list-header {
        display: none;
    }

    .garderobo-widget-popup-list-item-swap-button, .garderobo-widget-popup-actions-controls button {
        font-size: 12px;
    }

    .garderobo-widget-popup {
        background: #fff !important;
    }

    .modatech-article-content h3 {
        font-size: 16px;
        line-height: 20px;
    }

    .garderobo-widget-popup {
        max-height: calc(100vh - 20px);
        padding: 10px;
        border-radius: 10px 10px 0 0;
        position: fixed;
        top: 100%;
        transition: top 0.6s ease;
    }

    .garderobo-widget-popup-container--opened .garderobo-widget-popup {
        top: 20px;
    }

    .garderobo-widget-popup-container {
        top: 100%;
        display: unset;
        z-index: 99999;
    }
    .garderobo-widget-popup-container--opened {
        top: 0;
    }

    .platform-widget-control-left, .platform-widget-control-right {
        background-size: contain;
        background-repeat: no-repeat;
        width: 17px;
        height: 22px;
    }

    .garderobo-widget-look-container {
        max-height: 100%;
    }

    .garderobo-widget-popup-content .garderobo-widget-look-container img {
        height: 100%;
    }

    .garderobo-widget-popup-list-item-swap-container {
        position: fixed;
        height: 353px;
        top: 0;
        transition: top 0.7s ease;
        background-color: rgba(0, 0, 0, 0.08);
        height: 100%;
        display: flex;
        align-items: flex-end;
    }

    .garderobo-widget-popup-list-content .garderobo-widget-popup-list-item-swap-container-list {
        gap: 12px;
        padding: 24px 20px 40px;
        height: fit-content;
        overflow-x: auto;
        flex-wrap: nowrap;
        position: absolute;
        bottom: 0;
        width: 100%;
    }

    .garderobo-widget-popup-list-item-swap-container-item {
        min-width: 148px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-swap-container .garderobo-widget-popup-list-item-swap-back-button {
        z-index: 2;
        border-top: 1px solid #222;
        width: 100%;
        padding: 10px;
        position: absolute;
        bottom: 0;
        cursor: pointer;
    }
}

.garderobo-widget-popup-list-content {
    height: 100% !important;
    margin: 0 !important;
    border: none !important;
}

.garderobo-widget-popup-collage-container {
    border-right: none !important;
}

.garderobo-widget-popup-list-header {
    height: 40px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 40px 0;
}

.garderobo-widget-popup-collage-container {
    padding: 0 !important;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    align-self: unset;
    margin-top: 0;
    margin-right: 0 !important;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    display: none;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-popup__btn-close {
    padding: 8px;
    right: 20px;
}

.garderobo-widget-sizes-custom-dropdown {
    width: 100%;
    position: relative;
    margin-bottom: 10px;
}

.garderobo-widget-sizes-custom-dropdown-selected {
    border: 1px solid #eaeaea;
    border-radius: 3px;
    cursor: pointer;
    padding: 16px 20px;
    font-size: 14px;
    color: #333;
    font-family: Jost;
}

.garderobo-widget-sizes-custom-dropdown-icon {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNyIgdmlld0JveD0iMCAwIDEwIDciIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik01LjQ1MDU5IDMuNTE0MDhDNS43Mjk5OSAzLjI3OTMxIDYuMTc4OSAyLjkyODU2IDYuMTc4OSAyLjkyODU2QzYuOTM5MzIgMi4zMTcyMyA4LjA1MjAxIDEuNjUyNjIgOS41MzEyNSAwLjkzNzAxMkw5LjUzMTI1IDEuNDgyMzZDNy41ODk1MiAzLjEwMzQyIDYuMTEyNyA0LjgwODA0IDUuMTE0MyA2LjU5ODg3TDQuODkwNDUgNi41OTg4N0MzLjkxNTMyIDQuODA2MDcgMi40MzY2NiAzLjEwMTAzIDAuNDcwMTk2IDEuNDgwNjhMMC40NzAxOTYgMC45MzcwMTFDMS45NDk0NCAxLjY1MjYyIDMuMDYyMTMgMi4zMTcyMyAzLjgyMjU0IDIuOTI4NTZDMy44MjI1NCAyLjkyODU2IDQuMjcxNDYgMy4yNzkzMSA0LjU1MDg2IDMuNTE0MDhDNC43Mjg2MiAzLjY2MzQ1IDUuMDAwNzIgMy45MDI4IDUuMDAwNzIgMy45MDI4QzUuMDAwNzIgMy45MDI4IDUuMjcyODMgMy42NjM0NSA1LjQ1MDU5IDMuNTE0MDhaIiBmaWxsPSIjQjJCMkIyIi8+Cjwvc3ZnPgo=);
    background-repeat: no-repeat;
    width: 9px;
    height: 6px;
    position: absolute;
    right: 20px;
    top: 22px;
    transition: 0.15s cubic-bezier(1,0.5,0.8,1);
}

.garderobo-widget-sizes-custom-dropdown-icon-rotate {
    transform: rotate(180deg);
}

.garderobo-widget-sizes-custom-dropdown-items {
    position: absolute;
    top: 48px;
    background-color: #fff;
    border-radius: 0 0 4px 4px;
    border: 1px solid rgba(60, 60, 60, .26);
    border-top: none;
    z-index: 99;
    width: calc(100% - 2px);
    max-height: 350px;
    overflow-y: auto;
    font-size: 14px;
    color: #333;
    font-family: Jost;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .15);
}

.garderobo-widget-sizes-custom-dropdown-item {
    padding: 10px 20px;
    cursor: pointer;
}

.garderobo-widget-sizes-custom-dropdown-item:hover {
    background-color: #E6E6E6;
}

.garderobo-widget-sizes-custom-dropdown-item-selected {
    background-color: #E6E6E6;
}

.garderobo-widget-sizes-custom-dropdown-items-hidden {
    display: none;
}

.gw-folder-title {
    font-size: 24px;
    margin-bottom: 24px;
}