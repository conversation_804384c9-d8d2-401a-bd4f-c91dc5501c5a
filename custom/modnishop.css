#garderobo-widgets, #garderobo-platform {
    max-width: 1300px;
    margin: auto;
}

.garderobo-widget-container {
    font-family: "<PERSON><PERSON>" ,sans-serif;
}

.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 20px;
    flex: 1;
    align-self: center;
    height: 100%;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-similar {
    margin-bottom: 0;
}

.garderobo-widget-feed {
    background-color: #fff;
    padding-bottom: 24px;
}

.garderobo-widget-feed.garderobo-widget-feed-similar {
    padding-bottom: 0;
}

.garderobo-widget-feed-container.garderobo-widget-feed-item-look {
    padding-top: 24px;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: calc(var(--font-heading-scale) * 2.4rem);
    margin-bottom: 3rem;
    font-family: var(--font-heading-family);
    font-style: var(--font-heading-style);
    font-weight: var(--font-heading-weight);
    letter-spacing: calc(var(--font-heading-scale) * .06rem);
    color: rgb(var(--color-foreground));
    line-height: calc(1 + .3 / max(1, var(--font-heading-scale)));
    word-break: break-word;
}

.garderobo-widget-popup header {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    align-items: center;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-popup-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: calc(100% - 75px);
}

.garderobo-widget-popup-container {
    z-index: 9999999999;
}

.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-similar .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 10px;
    background-position: 0 -22px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 10px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right, .platform-widget-control-left, .platform-widget-control-right {
    text-indent: -99999px;
    display: block;
    width: 13px;
    height: 22px;
    transform: translate(0, -50%);
    cursor: pointer;
    color: transparent;
    border: 0;
    outline: 0;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAsBAMAAABMNS/cAAAAMFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABHcEx/v/MDAAAAEHRSTlP/+1dHd19n94T1o09tQAMAS5BP0wAAAKRJREFUGNNjeKX/Hwg+MCwQhdCBjPFg+pMAG5j+r8iQD6Y/CfCA6f8bGfzB9OcCFjD935zBH0x/nsABpv/fZGhApmHiUHUwfVBzYOZC7YHZC3MHzF0wd4LI/1oPwPRXgQVgOpAxAER/YxACyycw6oPoPwxCYPUOjPtB9BeGYrB+B3Z7EP2DYTLYvAbO+yg0TBymDq4PZg7cXJg9cHth7oC7C+hOAJlK4MmSzflFAAAAAElFTkSuQmCC);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    height: 100%;
    width: 100%;
}

.garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    width: 32%;
    padding-bottom: 33.33%;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}

/* STANDART LIST */
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 25%;
    padding: 0;
    margin: 0;
    height: initial;
}

.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-similar .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    border: none;
    transition: color .1s ease-in, background-color .1s ease-in;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    width: 91%;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    aspect-ratio: 1 / 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: auto;
    flex-direction: column;
    align-items: unset;
    padding-bottom: 1.7rem;
    padding-top: 1.7rem;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: block;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    color: #444;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    font-family: var(--font-heading-family);
    font-size: calc(var(--font-heading-scale) * 1.3rem);
    font-style: var(--font-heading-style);
    font-weight: var(--font-heading-weight);
    letter-spacing: calc(var(--font-heading-scale) * .06rem);
    color: rgb(var(--color-foreground));
    line-height: calc(1 + .3 / max(1, var(--font-heading-scale)));
    word-break: break-word;
    padding-right: 6px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name:hover {
    text-decoration: underline;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    margin-top: .7rem;
    color: rgb(var(--color-foreground));
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price, .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    padding: 0 4px 0 0;
    color: #444;
    font-size: 14px;
    font-weight: 400;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
    font-size: 1.6rem;
    letter-spacing: .1rem;
    line-height: calc(1 + .5 / var(--font-body-scale));
}

.garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1 / 1;
    max-width: 100%;
    width: 100%;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 0;
}

/* COLLAGES POPUP */
.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    height: 100%;
    margin: 0;
    border: none;
    border-right: 1px solid #E0E0E0;
    align-self: unset;
}

.garderobo-widget-popup__btn-close {
    padding: 8px;
    right: 20px;
}

.garderobo-widget-popup-content {
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
    padding: 0;
    border-right: none;
}

.garderobo-widget-popup .garderobo-widget-feed {
    border: none;
    align-self: unset;
    margin-top: 0;
    margin-right: 0;
    height: 100%;
    flex: 1;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    height: unset !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-bottom: 0 !important;
}

.garderobo-widget-popup-list-header {
    display: none;
}

.garderobo-widget-popup-list-content {
    height: calc(100% - 24px);
    margin: 0;
    border: none;
    padding: 0 40px;
    margin-left: 20px !important;
    border-left: 1px solid #e0e0e0 !important;
}

.garderobo-widget-popup-list-item:first-child {
    border-top: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 40px 0;
    width: unset;
    gap: 24px;
    border-top: 1px solid #e0e0e0;
    margin-bottom: 10px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
    border-bottom: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 300px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
}

.garderobo-widget-popup-list-item-like-button, .garderobo-widget-popup-list-swap-item-like-button {
    width: 20px;
    height: 17px;
    position: absolute;
    right: 16px;
    top: 16px;
    cursor: pointer;
    background: url(https://platform-static.modatech.ru/like-bordered.svg) no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
    z-index: 1;
    display: none;
}

.garderobo-widget-popup-list-item-text-brand {
    display: block !important;
    margin: 0 !important;
    font-weight: 500 !important;
    text-transform: uppercase;
    font-size: 20px;
    line-height: 28px;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 0;
    font-size: 16px;
    line-height: 19px;
    font-family: "Unica One", sans-serif;
    font-style: normal;
    font-weight: 400;
    letter-spacing: .1em;
    text-transform: uppercase;
    color: #333;
}

.garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 24px;
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-sizes {
    width: 100%;
    height: 48px !important;
    font-size: 17px !important;
    line-height: 20px;
    font-weight: 400;
    margin-right: 0 !important;
    height: 44px;
    padding: 13px 0 13px 15px !important;
    position: relative;
    margin-bottom: 10px;
    background-color: #f4f4f4;
    border-color: transparent !important;
}

.garderobo-widget-sizes:focus-visible {
    box-shadow: unset;
}

.garderobo-widget-popup-actions-controls button {
    width: 100%;
    height: 48px;
    border-radius: 3px;
    border-color: transparent;
    font-size: .88235em;
    font-weight: 700;
    text-transform: none;
    transition: color .1s ease-in, background-color .1s ease-in;
    margin: 0;
}

.garderobo-widget-popup-action-buttons button {
    height: 48px;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    margin-bottom: 10px !important;
    color: #fff;
}

.garderobo-widget-popup-list-item-swap-button {
    display: block !important;
    border: 1px solid #BABBC1;
    background-color: #F4F4F4;
    color: #333;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

/* SWAP CONTAINER */
.garderobo-widget-popup-list-item-swap-container {
    padding-bottom: 8px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 170px;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    display: none;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: relative;
    height: 80%;
    width: 100%;
    border: none !important;
    justify-content: start;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
    padding: 10px;
}

.garderobo-widget-popup-list-item-swap-item-img {
    max-height: 160px;
}

.garderobo-widget-popup-list-item-name {
    width: 100%;
    margin: 0 !important;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 300 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    text-align: center;
    margin-top: 10px !important;
    display: block !important;
    max-height: 65px;
    height: 70px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    margin-left: -1px;
    margin-top: -1px;
    padding: 2px 4px;
    font-size: 12px;
    line-height: 14px;
    border: 1px solid #ccc;
}

.garderobo-widget-popup-list-item-swap-item-price {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    height: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-look__btn-buy, .garderobo-widget-popup-list-item-text-cart-btn {
    background-color: #b8983a;
    border-radius: 3px;
    border-color: transparent;
    height: 48px;
    font-size: .88235em;
    font-weight: 700;
    color: #fff;
    text-transform: none;
    transition: color .1s ease-in, background-color .1s ease-in;
}

.garderobo-widget-look__btn-buy:hover, .garderobo-widget-popup-list-item-text-cart-btn:hover, .garderobo-scroll-button:hover, .garderobo-multiple-btn:hover {
    background-color: #91782e;
}

.garderobo-widget-look__btn-buy {
    bottom: -70px;
}

.garderobo-widget-feed-similar {
    background-color: #fff;
}

.garderobo-scroll-button {
    background-color: #b8983a;
    right: -100px;
    left: unset;
    transition: color .1s ease-in, background-color .1s ease-in;
}

/* SIMPLE LOOKS POPUP */
.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-img-v2 {
    object-fit: contain;
    max-height: 220px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    padding: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-cart-btn {
    width: 100%;
    margin: 0 !important;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-content  {
    height: calc(100% - 80px);
    border-bottom: 1px solid #e0e0e0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-bottom > div {
    width: 100%;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 20px 0;
}

.garderobo-multiple-btn {
    background-color: #b8983a;
    border-radius: 3px;
    border-color: transparent;
    height: 48px;
    width: 168px;
    font-size: .88235em;
    font-weight: 700;
    color: #fff;
    transition: color .1s ease-in, background-color .1s ease-in;
}

/* FOR ARABIC LANG */
html[lang="ar"] .garderobo-widget-sizes {
    padding: 13px 28px 13px 15px !important;
}

html[lang="ar"] .garderobo-widget-popup-list-item-text-brand,
html[lang="ar"] .garderobo-widget-popup-list-item-text-title {
    text-align: right;
}

html[lang="ar"] .garderobo-scroll-button {
    left: -130px;
    right: unset;
}

.garderobo-widget-popup-container .garderobo-grid-look__platform-product img {
    width: 100%;
    object-fit: contain;
}

/* PLATFORM */
.modatech-look-widgets {
    position: relative;
    height: 100%;
    display: flex;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    padding: 0 24px 0 10px;
}

.modatech-look-widgets-centered {
    padding: 0 10px;
}

.platform-widget-control-left, .platform-widget-control-right {
    background-position: unset;
    position: absolute;
}

.platform-widget-control-left {
    left: 2px;
    transform: rotate(180deg);
    top: calc(50% - 11px);
}

.platform-widget-control-right {
    right: 0;
    top: 50%;
}

.modatech-look-widget-container {
    flex: 0 0 calc(33.33% - 19px);
    height: auto;
    transition: transform 0.5s ease;
    border: 1px solid rgba(0, 0, 0, 0.12);
    margin: 0 12px;
}

.modatech-look-widget-container img {
    cursor: pointer;
}

.modatech-look-widgets-title {
    text-align: center;
    margin-bottom: 0;
    padding-bottom: 30px;
}

.main-content .modatech-look-widgets-title {
    font-family: "Playfair Display";
    font-size: 35px;
    font-weight: 500;
    letter-spacing: unset;
    text-transform: unset;
    line-height: 1.3em;
    color: #222;
}

.product-recommendations .modatech-look-widgets-title {
    font-size: calc(var(--font-heading-scale) * 2.4rem);
    margin-bottom: 3rem;
    font-family: var(--font-heading-family);
    font-style: var(--font-heading-style);
    font-weight: var(--font-heading-weight);
    letter-spacing: calc(var(--font-heading-scale) * .06rem);
    color: rgb(var(--color-foreground));
    line-height: calc(1 + .3 / max(1, var(--font-heading-scale)));
    word-break: break-word;
    padding: 0;
}

.modatech-look-widgets.modatech-look-widgets-centered {
    justify-content: center;
}

#garderobo-platform .modatech-look-widgets .garderobo-widget-container {
    position: absolute;
}

.gw-view-look-btn {
    display: block !important;
    background-color: #b8983a;
    border-radius: 3px;
    color: #fff;
    height: 48px;
    width: 100%;
    font-size: 15px;
    font-weight: 700;
    font-family: "Karla" ,sans-serif;
    transition: color .1s ease-in, background-color .1s ease-in;
}

.gw-view-look-btn:hover {
    background-color: #91782e;
}

.garderobo-widget-sizes-list {
    display: flex !important;
    flex-wrap: wrap;
    gap: 4px;
    padding: 0;
    list-style: none;
    margin-top: 16px;
    margin-bottom: 0;
    padding-right: 8px;
}

.garderobo-widget-sizes-list-item {
    border: 1px solid #f0f0f0;
    padding: 6px;
    font-size: 14px;
    min-width: 38px;
    text-align: center;
}

.garderobo-grid-look__product-disabled-layout img {
    opacity: 0.2;
}

.garderobo-grid-look__product-disabled-layout::before {
    content: attr(data-message);
    display: flex;
    position: absolute;
    justify-content: center;
    height: 100%;
    width: 50%;
    left: 25%;
    text-transform: uppercase;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    text-align: center;
}

.garderobo-widget-popup-list-item-swap-container-item--not-available {
    position: relative;
}

.garderobo-widget-popup-list-item-swap-container-item--not-available::after {
    content: attr(data-message);
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    color: #000;
    display: flex;
    text-transform: uppercase;
    text-align: center;
    padding-top: 75px;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    justify-content: center;
}

/* DESKTOP */
@media screen and (min-width: 769px) {
    .garderobo-widget-popup {
        height: 80vh;
        padding: 20px;
        max-width: 1300px;
        max-height: 1000px;
    }

    .garderobo-looks-simple-popup {
        max-width: 600px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 50%;
        padding-top: 20px;
        height: calc(100% + 20px);
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 100%;
        max-width: 100%;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-content {
        margin-left: 0 !important;
        padding: 0;
    }


    #garderobo-platform {
        padding: 0 0 60px;
    }

}

/* MOBILE */
@media screen and (max-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed {
        margin-bottom: 0;
        justify-content: center;
        height: 100%;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed {
        border: none !important;
        margin-left: 0 !important;
    }

    .garderobo-widget-popup-list-content {
        margin-left: 0 !important;
        padding: 0;
        border: 1px solid #e0e0e0 !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        padding: 12px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: 150px;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        margin: 0 !important;
    }

    .garderobo-widget-popup-collage-container {
        height: 368px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
        flex: unset;
    }

    .garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: 0;
        top: 50%;
    }

    .garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: 0;
        top: 50%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }

    #garderobo-widgets, #garderobo-platform {
        padding-inline: 15px;
    }

    #garderobo-platform {
        margin-bottom: 40px;
    }

    .garderobo-widget-feed-similar header {
        padding-top: 20px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: 6px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: 6px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text {
        padding-right: 10px;
    }

    .modatech-look-widgets.modatech-look-widgets-centered {
        justify-content: flex-start;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: calc(var(--font-heading-scale) * 2rem);
    }
}


@media screen and (max-width: 576px) {
    .garderobo-widget-popup {
        max-height: calc(100vh - 20px) !important;
        width: auto !important;
        padding: 10px;
        border-radius: 10px 10px 0 0;
        top: 100%;
        transition: top 0.7s ease;
        position: fixed !important;;
    }

    .garderobo-widget-popup-container--opened .garderobo-widget-popup {
        top: 20px;
        position: relative !important;
    }

    .garderobo-widget-popup-container {
        top: 100% !important;
        display: unset !important;
        z-index: 99999 !important;
    }

    .garderobo-widget-popup-container.garderobo-widget-popup-container--opened {
        top: 0 !important;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
        width: 100%;
    }

    .garderobo-scroll-button {
        left: -20px;
        top: 0;
        right: unset;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-header {
        padding: 0;
        margin-top: 24px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-content  {
        border-top: none !important;
    }

    .garderobo-multiple-controls {
        gap: 10px;
    }

    .garderobo-multiple-btn {
        width: 134px;
        line-height: 14px;
    }

    .modatech-look-widgets {
        gap: 0;
        border: 1px solid rgba(0, 0, 0, 0.12);
        padding: 0;
    }

    .modatech-look-widget-container {
        flex: 0 0 auto !important;
        width: 100%;
        border: unset;
        margin: 0;
    }

    .modatech-look-widget-container img {
        margin: auto;
    }

    .modatech-look-widgets .platform-widget-control-left {
        left: 4px;
    }

    .modatech-look-widgets .platform-widget-control-right {
        right: 4px;
    }

    .main-content .modatech-look-widgets-title {
        font-size: 30px;
    }

    .product-recommendations .modatech-look-widgets-title {
        font-size: calc(var(--font-heading-scale) * 2rem);
        text-align: start;
    }

    .garderobo-widget-feed-similar .garderobo-widget-control-left, .garderobo-widget-feed-similar .garderobo-widget-control-right {
        display: none !important;
    }

    .garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-looks .garderobo-widget-feed-container {
        width: 100%;
    }
}