/* Finn<PERSON>lare */
#garderobo {
    max-width: 1280px;
    margin: 48px auto;
    padding: 0 48px;
}

.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 20px;
    font-weight: 300;
    line-height: 1.2;
    color: #364087;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 35px;
    text-transform: uppercase;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 16.6%;
    height: auto;
    position: relative;
    padding: 11px 0;
    margin-top: 3px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item::before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item::after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -3px;
    transition: 0.8s 0.2s;
    opacity: 0.4;
    z-index: -1;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item::before {
    top: 0;
    left: 50%;
    width: 0;
    height: 100%;
    border-top: 1px solid;
    border-bottom: 1px solid;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item:hover::before {
    left: 0;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item::after {
    top: 50%;
    left: 0;
    width: 100%;
    height: 0;
    border-left: 1px solid;
    border-right: 1px solid;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item:hover::after {
    top: 0;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0s all;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 250px;
    width: 91%;
    max-width: 167px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: 86px;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: flex-end !important;
    height: 65px !important;
    width: 91% !important;
    max-width: 167px;
    line-height: 20px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    padding-top: 10px;
    text-align: center;
    margin-bottom: 5px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name:hover {
    text-decoration: underline;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    display: none;
    /*width: 100%;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-like,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-dislike{
    /*width: 50%;*/
    /*height: 40px;*/
    /*display: inline-block;*/
    /*background-color: gray;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    justify-content: center;
    font-size: 20px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container > * {
    margin: 0 7px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    position: relative;
    font-family: ProbaMedium;
    font-size: 18px;
    order: 2;
    opacity: 0.6;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price::after {
    content: '';
    display: inline-block;
    position: absolute;
    top: 45%;
    right: -5%;
    width: 110%;
    border-bottom: 1px solid;
    transform: rotate(-7deg);
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price + .garderobo-widget-product-price {
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
    -webkit-transition: all .3s;
    transition: all .3s;
    outline: none;
    opacity: 0.5;

    background: url(https://cdn.finnflare.com/upload/images/icons/arrow.png);
    width: 21px;
    height: 33px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:hover,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:hover {
    opacity: 1;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: -30px;
    transform: scaleX(-1);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: -30px;
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed {
        justify-content: center;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: calc(100% - 40px);
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -10px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -10px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 400px;
        width: 100%;
        max-width: 240px;
    }
}

@media screen and (max-width: 425px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: calc(100% - 20px);
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -18px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -18px;
    }
}

.garderobo-widget-look-product--center {
    left: 35% !important;
    width: 30% !important;
}

.garderobo-widget-look-product--right-top-3 {
    left: auto !important;
    right: 0 !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--layer-3_vest {
    width: 25% !important;
}

.garderobo-widget-look-product--bottom_shorts {
    width: 26% !important;
}

.garderobo-widget-look-product--right-bottom {
    min-width: 22% !important;
}

.garderobo-widget-look-product--center.garderobo-widget-look-product--center-layer-full {
    width: 35%;
}

