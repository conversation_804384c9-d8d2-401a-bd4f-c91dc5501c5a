.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 20px;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 24px;
    font-weight: 600;
    color: #000;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 10px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: calc(100% - 75px);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 20%;
    border: 1px solid white;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    height: auto;
    align-content: flex-start;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item:hover {
    border: 1px solid #d9d9d9;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header-content {
    margin: 0 4.5% 10px;
    padding: 5px;
    border-bottom: 2px #ccc solid;
    text-align: center;
    font-size: 14px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 240px;
    width: 91%;
    padding: 20px 10px 0;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-y: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: auto;
    flex-direction: column !important;
    padding: 10px 10px 20px 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: block;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    margin: 5px 0 !important;
    line-height: 16px;
    font-size: 12px;
    color: #241f1f;
    text-align: center;
    border: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    display: none;
    /*width: 100%;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-like,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-dislike {
    /*width: 50%;*/
    /*height: 40px;*/
    /*display: inline-block;*/
    /*background-color: gray;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    padding: 0 4px;
    color: #241f1f;
    font-size: 12px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price+.garderobo-widget-product-price {
    text-transform: uppercase;
    font-size: 12px;
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    font-size: 10px;
    padding: 0 2px 0 5px;
    order: 2;
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    order: 3;
    text-decoration: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    text-indent: -99999px;
    display: block;
    width: 13px;
    height: 22px;
    transform: translate(0, -50%);
    cursor: pointer;
    color: transparent;
    border: 0;
    outline: 0;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAsBAMAAABMNS/cAAAAMFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABHcEx/v/MDAAAAEHRSTlP/+1dHd19n94T1o09tQAMAS5BP0wAAAKRJREFUGNNjeKX/Hwg+MCwQhdCBjPFg+pMAG5j+r8iQD6Y/CfCA6f8bGfzB9OcCFjD935zBH0x/nsABpv/fZGhApmHiUHUwfVBzYOZC7YHZC3MHzF0wd4LI/1oPwPRXgQVgOpAxAER/YxACyycw6oPoPwxCYPUOjPtB9BeGYrB+B3Z7EP2DYTLYvAbO+yg0TBymDq4PZg7cXJg9cHth7oC7C+hOAJlK4MmSzflFAAAAAElFTkSuQmCC');
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
    background-position: 0 -22px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-popup-content .garderobo-widget-control-right {
    right: 15px !important;
}

.garderobo-widget-popup-content .garderobo-widget-control-left {
    left: 15px !important;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    content: '';
    font-family: slick;
    font-size: 20px;
    line-height: 1;
    opacity: 0.75;
    color: #fff;
    -webkit-font-smoothing: antialiased;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    content: '';
    font-family: slick;
    font-size: 20px;
    line-height: 1;
    opacity: 0.75;
    color: #fff;
    -webkit-font-smoothing: antialiased;
}

.garderobo-widget-look-product--left-top.garderobo-widget-look-product--layer-1_top,
.garderobo-widget-look-product--right-top.garderobo-widget-look-product--layer-1_top {
    height: 30%;
    width: 25%;
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed {
        margin-bottom: 0;
        justify-content: center;
        height: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 400px;
        width: 100%;
        max-width: 240px;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 17px;
        margin-top: 50px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        line-height: 13px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
        flex-wrap: wrap;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
        text-transform: none;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg id='Стрелка_xA0_Изображение_1_' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 110.4 173.6'%3e%3cswitch%3e%3cg%3e%3cpath fill='%239D9D9D' d='M89.9,162c-3.5-3.2-82.1-75.3-85.4-77.7c-0.8-0.6-0.7-1.5,0-2.3C7,79.5,64.1,27.8,89.2,4.9 c0.2-0.2,0.4-0.3,0.7-0.6c2.9,3.2,5.7,6.3,8.7,9.6C73.2,37,47.9,60,22.5,83.1c25.4,23.1,50.7,46.1,76.1,69.2 C95.7,155.6,92.8,158.7,89.9,162z'/%3e%3c/g%3e%3c/switch%3e%3c/svg%3e");
        background-position: unset;
        transform: unset;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        transform: rotate(180deg);
    }
}

@media screen and (max-width: 425px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: calc(100% - 20px);
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        height: 165px;
    }
}

.garderobo-widget-look-product--shoes {
    height: 20%;
}

.garderobo-widget-look-product--bag {
    width: 21%;
    height: 21%;
}

.garderobo-widget-look-product--left-center-bag {
    top: 54%;
}

.garderobo-widget-look-product--gloves {
    width: 12%;
    height: 20%;
    top: 30%;
    background-position-y: bottom;
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
        width: 32% !important;
    }
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    /*border: 1px solid #eee !important;*/
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    border: none !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 20px !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed {
    margin: 0 !important;
}

.garderobo-widget-look-container {
    padding: 7px;
}

.garderobo-widget-look-container div[data-product-type="gloves"] {
    display: none;
}

.garderobo-widget-look-container div[data-product-type="socks"] {
    display: none;
}

.garderobo-widget-look-container div[data-product-type="accessory"] {
    display: none;
}

.garderobo-widget-look-container div[data-product-type="belt"] {
    display: none;
}

.garderobo-grid-look__product_img_with_positions {
    display: block !important;
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        border: none !important;
    }
}

.garderobo-grid-look-2 p {
    z-index: 50;
    text-transform: uppercase;
    line-height: 11px;
    font-size: 10px;
    display: block;
    align-items: center; /* Выравнивание по вертикали по центру */
    flex-wrap: wrap;
    gap: 0;
    text-align: center;
    opacity: 0.7;
    border-radius: 10px;
}

.garderobo-grid-look-2 p strong {
    width: 100%;
    display: block;
}

.garderobo-widget-popup .garderobo-widget-look__label-look-name, .garderobo-widget-popup .garderobo-widget-look__label-look-link {display: none !important;}

.garderobo-widget-popup {
    padding: 20px;
}

@media screen and (max-width: 768px) {
    .garderobo-widget-popup-content .garderobo-widget-look__label-look-name, .garderobo-widget-popup-content .garderobo-widget-look__label-look-link {display: none !important;}
}

.garderobo-widget-popup-list-item-brand,
.garderobo-widget-popup-list-item-brand-hide {
    display: -webkit-box !important;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    min-height: 31px;
    margin-block: 5px;
    font-size: 10px;
    line-height: 1;
    text-align: center;
    overflow: hidden;
}

.garderobo-widget-sizes-buttons-wrapper {
    display: block;
}

.garderobo-widget-sizes-buttons {
    display: none;
    flex-wrap: wrap;
    margin: 12px 0;
}

.garderobo-widget-sizes-buttons div {
    border: 1px solid #000;
    padding: 8px 12px;
    cursor: pointer;
    margin-right: -1px;
    margin-top: -1px;
}

.garderobo-widget-popup-list-item-swap-button {
    display: block !important;
    border: 1px solid #BABBC1;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    background: #2A2A2A;
    color: #fff;
    margin-bottom: 10px !important;
}

.garderobo-widget-popup-list-item-text-cart-btn-link-state::before {
    content: "";
}


.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-sizes {
    width: 100%;
    border: 1px solid #EAEAEA;
    border-radius: 3px;
    font-size: 18px !important;
    line-height: 20px;
    font-weight: 400;
    margin-right: 0 !important;
    height: 44px;
    padding: 10px !important;
    position: relative;
    margin-bottom: 10px;
}

.garderobo-widget-popup-actions-controls button {
    width: 100%;
    padding: 12px 0;
    text-transform: uppercase;
    border-radius: 3px;
    font-weight: 400;
    font-size: 14px;
    font-size: 17px;
    margin: 0;
    text-align: center;
    cursor: pointer;
}

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 0;
    font-size: 16px;
    font-weight: 300;
    line-height: 19px;
    letter-spacing: 1px;
}

.garderobo-widget-popup-list-item-text-new-price {
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
    color: #222;
}

.garderobo-widget-popup-list-item-text-discount {
    font-size: 16px;
    font-weight: 500;
    line-height: 26px;
    color: #838383;
    margin-right: 8px;
}

.garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 24px;
}

.garderobo-widget-sizes-button-active {
    background-color: #bbb;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 170px;
}

.garderobo-widget-popup-list-item-swap-container {
    padding-bottom: 8px;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    height: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}



.garderobo-widget-popup-list-item-swap-container-item--not-available {
    position: relative;
}

.garderobo-widget-popup-list-item-swap-container-item--not-available::after {
    content: "This item is out of stock, but we have a similar item available";
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    color: #000;
    display: flex;
    padding: 0;
    text-transform: uppercase;
    text-align: center;
    padding-top: 75px;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: relative;
    height: 80%;
    width: 100%;
    border: none !important;
    justify-content: start;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    margin-left: -1px;
    margin-top: -1px;
    padding: 2px 4px;
    font-size: 12px;
    line-height: 14px;
    border: 1px solid #ccc;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-content .garderobo-widget-feed-header {
    display: none;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
    padding: 10px;
}

.garderobo-widget-popup-list-item-swap-item-img {
    max-height: 160px;
}

.garderobo-widget-popup-list-item-name {
    width: 100%;
    margin: 0 !important;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 300 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    text-align: center;
    margin-top: 10px !important;
    display: block !important;
    max-height: 65px;
    height: 70px;
}

.garderobo-widget-popup-list-item-brand {
    width: 100%;
    margin: 0 !important;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 500 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    height: 20px;
    text-align: center;
    display: none !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 10px !important;
}

.garderobo-widget-popup-list-item-text-brand {
    display: block !important;
    margin: 0 !important;
    font-weight: 500 !important;
    text-transform: uppercase;
    font-size: 20px;
    line-height: 28px;
}

.garderobo-widget-popup-list-item-swap-item-price {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0.5px;
    color: #222 !important;
    text-decoration: none;
    padding: 0;
    text-align: left;
    display: flex;
    white-space: nowrap;
    gap: 10px;
    flex-flow: row-reverse;
    justify-content: center;
}

.garderobo-widget-popup-list-item-swap-container-item--selected .garderobo-widget-popup-list-item-swap-item-price {
    padding: 0;
}

.garderobo-widget-popup-list-item-swap-item-price--sale .garderobo-widget-popup-list-item-swap-item-price--old {
    display: block !important;
    font-size: 12px;
    text-decoration: line-through;
    color: #838383;
}

.garderobo-widget-popup-list-item-swap-item-price {
    position: unset;
}

.garderobo-widget-popup-collage-container {
    padding: 48px 0;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}

.garderobo-widget-feed-item {
    padding: 0 !important;
    margin: 0 !important;
    height: 100%;
}

.garderobo-widget-feed-item-min-width {
    padding: 0 !important;
    margin: 0 !important;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
    height: 100%;
}

.garderobo-widget-feed-container {
    align-items: center;
}

.garderobo-widget-feed-items {
    height: 100%;
}

.garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1/1;
    max-width: 100%;
    width: 100%;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    align-self: center;
    height: 100%;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 24px 12px;
    width: unset;
    gap: 24px;
    border-top: 1px solid #e0e0e0;
    margin-bottom: 10px;
}

.garderobo-widget-popup-list-item:first-child {
    border-top: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 150px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
    border-bottom: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 300px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    background: #fff;
    border-radius: 8px;
    opacity: 0.7;
}

.look-loading-state::after {
    content: "Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 1;
}

.garderobo-grid-look__product_img_with_positions {
    position: relative;
}

.garderobo-grid-look__product-disabled-layout img {
    opacity: 0.2;
}

.garderobo-grid-look__product-disabled-layout::before {
    content: "Not Available";
    display: flex;
    position: absolute;
    justify-content: center;
    height: 100%;
    width: 50%;
    left: 25%;
    text-transform: uppercase;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    text-align: center;
}

@media screen and (min-width: 767px) {
    .garderobo-widget-popup {
        height: 80vh !important;
        max-width: 1300px;
        max-height: 1000px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 50%;
        padding-top: 20px;
        height: calc(100% + 20px);
    }

    .garderobo-widget-popup-list-content {
        padding: 0 40px;
        margin-left: 20px !important;
        border-left: 1px solid #e0e0e0 !important;
    }
}

@media screen and (max-width: 766px) {
    .garderobo-widget-feed-header {
        display: block;
    }
}

/* PLATFORM */
.modatech-look-widgets img {
    cursor: pointer;
    width: 100%;
    object-fit: contain;
}

.modatech-article-content .modatech-look-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(calc(50% - 12px), 1fr));
}

.modatech-article-content .modatech-look-widget-container {
    max-width: unset;
}

.modatech-look-widgets {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 24px;
}

.modatech-look-widget-container {
    flex: calc(33% - 24px);
    max-width: calc(33% - 12px);
    border: 1px solid #BABBC1;
}

.modatech-look-widget-container {
    position: relative;
    padding: 0px;
}

.modatech-platform-overlay {
    display: flex;
    position: absolute;
    width: 100%;
    height: 100%;
    background: #000;
    top: 0;
    left: 0;
    align-items: center;
    opacity: 0.5;
}

.modatech-platform-overlay p {
    color: #fff;
    padding-left: 10px;
    font-size: 14px;
    text-align: center;
}

.modatech-platform-loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #000;
    border-radius: 50%;
    min-width: 30px;
    min-height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.garderobo-widget-wrapper {}

.modatech-platform-look-like-state-button, .garderobo-widget-popup-list-item-like-button, .garderobo-widget-popup-list-swap-item-like-button {
    width: 20px;
    height: 17px;
    position: absolute;
    right: 16px;
    top: 16px;
    cursor: pointer;
    background: url('https://testplatform-static.modatech.ru/like-bordered.svg') no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
    z-index: 1;
}

.garderobo-widget-popup-list-swap-item-like-button {
    top: 19px;
}

.garderobo-widget-popup-list-header {
    border-bottom: none;
    display: none;
}

.garderobo-widget-popup-list-item-not-available {
    opacity: 0;
}

.modatech-platform-look-like-state-button:hover, .garderobo-widget-popup-list-swap-item-like-button:hover, .modatech-platform-look-like-state-button-liked, .garderobo-like-button-liked-state, .garderobo-widget-popup-list-item-like-button:hover {
    background: url('https://testplatform-static.modatech.ru/like-filled.svg') no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
}

/*
.modatech-platform-look-like-state-button-liked:hover {
    filter: brightness(0.8) hue-rotate(50deg);
}
*/


/* ARTICLE */
.modatech-platform {
    font-weight: 300;
    color: #222;
}

.modatech-platform h1 {
    font-weight: 300;
    font-size: 48px;
    line-height: 69px;
    text-align: center;
}

.modatech-platform p {
    font-weight: 300;
}

.modatech-article-header {
    display: flex;
    gap: 100px;
    padding-bottom: 36px;
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 40px;
    justify-content: flex-end;
}

.modatech-article-title__info {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.modatech-article-header h1 {
    padding-bottom: 30px;
    position: relative;
    margin-bottom: 12px;
}

.modatech-article-header h1::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 1px;
    background-color: #222;
}

.modatech-article-title__categories {
    font-size: 14px;
    line-height: 20px;
    text-transform: uppercase;
}

.modatech-article-title__author {
    font-size: 14px;
    font-weight: 600 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    margin-bottom: 6px;
}

.modatech-article-title__social-share {
    height: 25px;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 36px;
    margin-top: 70px;
}

.modatech-article-title__social-share a {
    display: block;
    align-self: center;
    cursor: pointer;
}

.modatech-article-title__image {
    flex: 0 0 calc(50% - 50px);
    aspect-ratio: 1/1;
}

.modatech-article-title__image img {
    width: 100%;
    height: unset;
    object-fit: cover;
    aspect-ratio: 1/1;
}

.modatech-article-container {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    gap: 60px;
}

.modatech-article-content {
    max-width: 670px;
    flex: 1;
    margin: 0 auto;
    font-size: 18px;
    line-height: 31px;
}

.modatech-article-content img {
    max-width: 100%;
    max-height: 100%;
}

.modatech-article-excerpt {
    font-style: italic;
    margin-top: 0 !important;
}

.modatech-article-content h2 {
    font-weight: 400;
    font-size: 32px;
    line-height: 54px;
    margin-top: 40px;
}

.modatech-article-content h3 {
    font-size: 20px;
    line-height: 28px;
    margin-bottom: 8px;
    font-weight: 300;
    text-transform: uppercase;
    margin-bottom: 32px;
}

.modatech-article-content p {
    margin-bottom: 12px;
    margin-top: 8px;
}

.modatech-article-sidebar {
    min-width: 400px;
    max-width: 400px;
}

.modatech-article-sidebar-content {
    border-top: 1px solid #222;
    border-bottom: 1px solid #222;
    position: sticky;
    top: 87px;
    padding: 19px 0;
}

.modatech-article-sidebar-content h2 {
    font-size: 32px;
    font-weight: 300;
    line-height: 46px;
    margin-bottom: 36px;
}

.modatech-articles-list {
    display: flex;
    flex-wrap: wrap;
    gap: 36px;
}

.modatech-articles-list-item {
    display: flex;
    gap: 64px;
    position: relative;
    width: 100%;
    padding-bottom: 36px;
    border-bottom: 1px solid #E0E0E0
}

.modatech-related-articles-list .modatech-articles-list-item {
    gap: 24px;
}

.modatech-related-articles-list .modatech-articles-list-item-excerpt {
    display: none;
}

.modatech-related-articles-list .modatech-articles-list-item {
    padding-bottom: 0;
    border-bottom: none;
}

.modatech-related-articles-list {
    gap: 36px;
}

.modatech-articles-list-item:hover h3 {
    color: #aa7e5b;
}

.modatech-articles-list-item-categories {
    font-size: 12px;
    line-height: 17px;
    margin-bottom: 12px;
    text-transform: uppercase;
}

.modatech-articles-list-item h3 {
    font-size: 18px;
    font-weight: 400;
    line-height: 26px;
    position: relative;
}

.modatech-related-articles-list .modatech-articles-list-item h3 {
    font-weight: 300;
}

.modatech-articles-list-item img {
    width: 322px;
    min-width: 322px;
    height: 322px;
    object-fit: cover;
}

.modatech-related-articles-list img {
    width: 80px;
    min-width: 80px;
    height: 80px;
}

.modatech-articles-list-item-info {
    flex: 1;
    align-self: center;
}

.modatech-articles-list-item-info-footer {
    display: flex;
    flex-wrap: nowrap;
    gap: 14px;
    font-size: 12px !important;
    line-height: 17px !important;
    padding-top: 8px;
    margin-top: 24px;
    position: relative;
}

.modatech-articles-list-item-author {
    font-weight: 500;
    text-transform: uppercase;
}

.modatech-articles-list-item-info-footer::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 44px;
    height: 1px;
    background-color: #222;
}

.modatech-articles-list-item-author::after {
    content: "\2022";
    font-weight: 900;
    margin-left: 10px;
}

.modatech-articles-list-item-excerpt {
    margin-top: 12px;
    font-size: 17px;
    line-height: 30px;
    font-weight: 300;
}

/* BLOGGERS */

.modatech-bloggers-list {
    display: flex;
    gap: 24px;
    flex-wrap: wrap;
}

.modatech-bloggers-list-item {
    flex: 1 0 calc(25% - 24px);
    max-width: calc(25% - 12px);
    margin-bottom: 96px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    color: #222;
}

.modatech-bloggers-list-item-info {
    color: #222;
    text-decoration: none;
}

.modatech-bloggers-list-item img {
    width: 100%;
    aspect-ratio: 1/1;
    object-fit: cover;
}

.modatech-subscribe-button {
    margin-top: 24px;
    text-align: center;
    padding: 16px;
    width: 100%;
    text-transform: uppercase;
    border: 1px solid #222;
    border-radius: 24px;
    height: 48px;
    align-self: flex-end;
    max-width: 320px;
    cursor: pointer;
}

.modatech-subscribe-button:hover {
    background: #222;
    color: #fff;
}

.modatech-blogger-header {
    display: flex;
    gap: 75px;
    padding-bottom: 36px;
    border-bottom: 1px solid #E0E0E0
}

.modatech-blogger-header .modatech-article-title__social-share {
    justify-content: left;
}

.modatech-blogger-header__image {
    flex: 1 0 calc(50%);
    aspect-ratio: 1/1;
}

.modatech-blogger-header__image img {
    width: 100%;
    height: unset;
    object-fit: cover;
}

.modatech-blogger-header__texts h1 {
    text-align: left;
}

.modatech-blogger-header__texts {
    align-self: flex-end;
}

.modatech-blogger-header__info {
    display: flex;
    flex-wrap: wrap;
}

.modatech-subscribe-button-hide-state {
    display: none;
}

.modatech-article-title__social-share {
    align-self: flex-end;
}

.modatech-blogger-title, .modatech-blogger-header__title {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 1px;
    margin-top: 12px;
}

.modatech-bloggers-list-item h2 {
    font-size: 24px;
    font-weight: 400;
    line-height: 35px;
    margin-top: 12px;
    margin-bottom: 4px;
}

.modatech-blogger-bio {
    font-size: 14px;
    font-weight: 300;
    line-height: 20px;
}

.modatech-blogger-header__bio {
    font-size: 18px;
    line-height: 26px;
    margin-top: 24px;
}

.modatech-blogger-looks {
    padding: 36px 0;
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 36px;
}

.modatech-blogger-looks h2 {
    font-size: 24px;
    font-weight: 400;
    line-height: 35px;
    letter-spacing: 1.5px;
    text-align: center;
    text-transform: uppercase;
    margin-bottom: 36px;
}

.modatech-button-next-page {
    border: 1px solid #222;
    padding: 12px 74px;
    text-transform: uppercase;
    font-weight: 400;
    font-size: 13px;
    line-height: 17px;
    margin: 0 auto;
    cursor: pointer;
}

.modatech-button-next-page:hover {
    background: #222;
    color: #fff;
}

.modatech-lk-bloggers-list .modatech-bloggers-list-item {
    flex: 1 0 calc(33% - 24px);
    max-width: calc(33% - 12px);
}

.modatech-lk-bloggers-list {
    border-bottom: 1px solid #E0E0E0;
    margin-bottom: 36px;
    padding: 24px;
}

.modatech-lk-look-widgets {
    justify-content: left;
    margin-bottom: 36px;
    padding: 24px;
}

.garderobo-widget-popup-list-item-swap-item-discount, .garderobo-widget-popup-list-item-discount {
    display: block !important;
    position: absolute;
    left: 0;
    top: 0;
    font-size: 12px;
    line-height: 14px;
    font-weight: 400;
    color: #E00018;
}

.garderobo-widget-popup-list-item-swap-item-discount::before, .garderobo-widget-popup-list-item-discount::before {
    content: "–";
}

.garderobo-widget-popup-list-item-discount {
    font-size: 14px;
    left: 10px;
    top: 10px;
    background: #fff;
    padding: 2px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
}

@media (max-width: 992px) {
    .modatech-article-container {
        flex-wrap: wrap;
    }

    .modatech-article-content {
        max-width: unset;
        flex: 100%;
    }

    .modatech-article-sidebar {
        max-width: unset;
        min-width: unset;
    }

    .modatech-bloggers-list-item {
        flex: 1 0 calc(33% - 24px);
        max-width: calc(33% - 12px);
        margin-bottom: 48px;
    }

    .modatech-lk-bloggers-list .modatech-bloggers-list-item {
        flex: 1 0 calc(50% - 24px);
        max-width: calc(50% - 12px);
    }

    .modatech-lk-articles-list .modatech-articles-list-item img {
        width: 250px;
        min-width: 250px;
        height: 250px;
    }

    .modatech-lk-articles-list .modatech-articles-list-item {
        gap: 36px;
    }
}

@media (max-width: 900px) {
    .modatech-article-header, .modatech-blogger-header {
        flex-wrap: wrap;
        gap: unset;
        row-gap: 24px;
    }

    .modatech-article-title__image {
        flex: 100%;
    }

    .modatech-bloggers-list-item {
        flex: 1 0 calc(50% - 24px);
        max-width: calc(50% - 12px);
    }

    .modatech-look-widget-container {
        flex: calc(50% - 36px);
        max-width: calc(50% - 18px);
        border: 1px solid #BABBC1;
    }
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed {
    border: none;
    border-right: 1px solid #E0E0E0;
    margin: 0 !important;
    height: 100%;
}

.garderobo-widget-popup-content {
    border: none;
}

@media (max-width: 576px) {
    .modatech-platform h1 {
        font-size: 36px;
        line-height: 52px;
    }

    .modatech-article-title__social-share {
        margin-top: 40px;
    }

    .modatech-article-content p {
        font-size: 14px;
        line-height: 22px;
    }

    .modatech-bloggers-list-item {
        flex: 100%;
        max-width: unset;
    }

    .modatech-look-widget-container {
        flex: 100%;
        max-width: unset;
    }

    .modatech-articles-list-item {
        gap: 20px;
        flex-wrap: wrap;
    }

    .modatech-articles-list-item img, .modatech-lk-articles-list .modatech-articles-list-item img {
        width: 100%;
        min-width: unset;
        height: 100%;
        aspect-ratio: 1/1;
    }

    .garderobo-widget-popup {
        max-height: 100vh;
        padding: 10px;
    }

    .garderobo-widget-sizes {
        height: 40px;
        font-size: 14px !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
        max-height: 200px;
    }

    .garderobo-widget-popup-list-item-like-button {
        right: 0;
    }

    .modatech-lk-bloggers-list .modatech-bloggers-list-item {
        flex: 1 0 calc(100% - 24px);
        max-width: calc(100% - 12px);
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: 140px;
    }

    .modatech-related-articles-list img {
        width: 80px !important;
        min-width: 80px !important;
        height: 80px !important;
    }

    .modatech-article-content .modatech-look-widgets {
        grid-template-columns: repeat(auto-fit, minmax(100%, 1fr));
    }

    .modatech-subscribe-button {
        max-width: unset;
    }

    .garderobo-widget-popup-list-content {
        margin-right: 10px !important;
        margin-bottom: 10px !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        border: none !important;
        margin-left: 0 !important;
    }

    .garderobo-widget-popup-list-header {
        display: none;
    }

    .garderobo-widget-popup-list-item-swap-button, .garderobo-widget-popup-actions-controls button {
        font-size: 12px;
    }

    .garderobo-widget-popup {
        background: #fff !important;
    }

    .modatech-article-content h3 {
        font-size: 16px;
        line-height: 20px;
    }

    .content-container {
        width: 100vw;
    }

    #menu {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
        flex-direction: row;
    }
}

.garderobo-widget-popup-list-content {
    height: 100%;
    margin: 0;
    border: none;
}

.garderobo-widget-popup-collage-container {
    border-right: none;
}

.garderobo-widget-popup-list-header {
    height: 40px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 40px 0;
}

.garderobo-widget-popup-collage-container {
    padding: 0;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    align-self: unset;
    margin-top: 0;
    margin-right: 0 !important;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    display: none;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-popup__btn-close {
    padding: 8px;
    right: 20px;
}

.garderobo-widget-popup-list-item-swap-item-price {
    display: none;
}

.modatech-platform {
    padding: 24px;
}

#menu {
    margin-top: 24px !important;
}

.modatech-bloggers-list-item-info:hover {
    text-decoration: none;
}

.modatech-lk-articles-list {
    padding: 24px;
}

.garderobo-widget-popup-list-item-like-button {
    display: none;
}

.garderobo-widget-popup-list-swap-item-like-button {
    display: none;
}

.garderobo-widget-popup .garderobo-widget-look__label-look-name {display: none !important;}
.garderobo-widget-popup .garderobo-widget-feed {border: none !important;}

.garderobo-widget-popup-list-content {
    height: calc(100% - 24px);
}

.garderobo-widget-sizes {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url(https://www.keng.ru/local/templates/desktop18/assets/imgs/str5.png);
    background-position: right 10px center;
    background-repeat: no-repeat;
}

@media screen and (min-width: 768px) {
    .garderobo-widget-popup .garderobo-widget-popup-content .garderobo-widget-control-right {
        right: 0 !important;
    }

    .garderobo-widget-popup .garderobo-widget-popup-content .garderobo-widget-control-left {
        left: 0 !important;
    }
}

@media screen and (max-width: 768px) {

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        top: 60%;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed-item-look {
        height: 90vw;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed-header {
        display: block;
    }

    .garderobo-widget-look-container {
        max-height: 100%;
    }
}


/* MAIN PAGE */
.garderobo-main-block {
    height: 100%;
}

.garderobo-main-block .garderobo-widget-feed-header {
    display: none;
}

.garderobo-main-block .garderobo-widget-container, .garderobo-main-block .garderobo-widget-feed {
    height: 100%;
    margin: 0;
}

.garderobo-main-block .garderobo-widget-look-container {
    position: unset;
    height: 100%;
    width: unset;
}

.digi-body_mobile .garderobo-main-block .garderobo-widget-look-container {
    position: unset;
    height: unset;
    width: 100%;
}

.garderobo-main-block .garderobo-widget-feed-item-look {
    margin-top: 0;
}

.garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
    justify-content: unset;
}

.garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
    align-items: flex-start !important;
    position: relative;
}

.garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items {
}

.garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 100% !important;
    border: 1px solid #eee !important;
    display: flex;
    aspect-ratio: 1.228 / 1;
    height: unset;
    padding: 0 !important;
    position: relative;
}

.digi-body_mobile .garderobo-main-block .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    aspect-ratio: 1 / 1.24;
    height: unset;
}

.garderobo-main-block .garderobo-widget-control-right, .garderobo-main-block .garderobo-widget-control-left {
    top: 40% !important;
}

.garderobo-main-block .garderobo-widget-control-right {
    right: 10px !important;
}

.garderobo-main-block .garderobo-widget-control-left {
    left: 10px !important;
}

.garderobo-main-block .garderobo-widget-look__btn-buy {
    border: none; border-bottom: 1px solid #212529;
    font-weight: normal;
    padding: 0;
    left: 21px;
    font-size: 14px;
    letter-spacing: normal;
    font-family: Roboto-light;
    text-transform: lowercase;
    padding-bottom: 4px;
    color: #212529;
    bottom: -70px;
}

.garderobo-main-block .garderobo-widget-look__btn-buy:hover {
    background: none;
}

.garderobo-main-block .garderobo-widget-look__label-look-name {display: block !important;
    position: absolute;
    bottom: -30px;
    font-family: Roboto-light;
    font-size: 16px;
    width: 100%;
    left: 0;
    text-align: left;
    text-wrap: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

@media screen and (min-width: 911px) {
    .garderobo-main-block .garderobo-widget-look__label-look-name {
        font-family: Roboto-medium;
    }

    .garderobo-main-block .garderobo-widget-look__btn-buy {
        font-size: 16px;
        letter-spacing: 2px;
        bottom: -62px;
        left: 30px;
    }
}

@media screen and (max-width: 1150px) {
    .garderobo-widget-sizes {
        font-size: 12px !important;
    }

    .garderobo-widget-popup-actions-controls button {
        font-size: 12px;
    }
}

@media screen and  (min-width: 768px) and (max-width: 1024px) {
    .garderobo-widget-popup-collage-container {
        display: none !important;
    }

    .garderobo-widget-popup-list-container {
        width: 100% !important;
        max-width: unset !important;
    }

    .garderobo-widget-popup {
        max-width: 650px;
    }

    .garderobo-widget-popup-list-content {
        border-left: none !important;
        padding: 0 20px;
    }

}

@media screen and (min-width: 1270px) {
    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: 200px;
    }
}

