/* LEFORM */
.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 40px;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 20px;
    font-weight: bold;
    color: #000;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 40px;
    text-transform: uppercase;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 16.666666667%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    height: auto;
    flex-direction: column;
    justify-content: flex-start;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 216px;
    width: 91%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    flex-direction: column !important;
    font-size: 12px;
    align-items: flex-start !important;
    width: 91% !important;
    line-height: 20px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    padding-top: 10px;
    text-align: left;
    margin-bottom: 5px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    display: none;
    /*width: 100%;*/
}

.garderobo-widget-container
    .garderobo-widget-feed-item
    .garderobo-widget-product-footer
    .garderobo-widget-product-control-container
    .garderobo-widget-product-control-like,
.garderobo-widget-container
    .garderobo-widget-feed-item
    .garderobo-widget-product-footer
    .garderobo-widget-product-control-container
    .garderobo-widget-product-control-dislike {
    /*width: 50%;*/
    /*height: 40px;*/
    /*display: inline-block;*/
    /*background-color: gray;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    text-decoration: line-through;
    color: #434a54;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header {
    height: 20px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: block;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: -30px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: -30px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    color: #616667;
    width: 24px;
    height: 24px;
    content: '';
    display: block;
    position: absolute;
    top: 12px;
    left: 12px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-left-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(14.000000, 14.000000) rotate(-180.000000) translate(-14.000000, -14.000000) translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group' transform='translate(0.000000, -0.000000)'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' id='icon--jumbotron-right-b' viewBox='0 0 28 28' width='100%25' height='100%25'%3E%3C!-- Generator: Sketch 3.8.2 (29753) - http://www.bohemiancoding.com/sketch --%3E%3Ctitle%3ESlice 1%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' stroke-linecap='square'%3E%3Cg id='Next' transform='translate(2.000000, 2.000000)' stroke-width='3' stroke='%2319181d'%3E%3Cg id='Group'%3E%3Cpolyline id='Line' transform='translate(12.000000, 12.000000) rotate(-315.000000) translate(-12.000000, -12.000000) ' points='4 4 20 4 20 20'%3E%3C/polyline%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-scroll .garderobo-widget-feed-container .garderobo-widget-add-to-cart-btn {
    line-height: 30px;
    text-transform: uppercase;
    text-align: center;
    letter-spacing: 0.1em;
    font-weight: 500;
    cursor: pointer;
    box-sizing: border-box;
    border: none;
    background-color: rgb(0, 0, 0);
    color: rgb(255, 255, 255);
    transition: opacity 0.3s ease 0s;
    width: 100%;
    height: 70px;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-size: 14px;
    padding: 18px 0 14px;
    max-width: 175px;
    position: relative;
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed {
        justify-content: center;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: calc(100% - 40px);
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -10px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -10px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }

    .garderobo-widget-container
        .garderobo-widget-feed
        .garderobo-widget-feed-container
        .garderobo-widget-feed-items
        .garderobo-widget-feed-item
        .garderobo-widget-product-image {
        height: 400px;
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: space-between;
    }
}

@media screen and (max-width: 425px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: calc(100% - 20px);
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: -18px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -18px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: space-between;
    }
}
