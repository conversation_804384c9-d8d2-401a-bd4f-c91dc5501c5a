.garderobo-widget-container {
    font-family: "Proxima Nova Condensed";
}

.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 20px;
    flex: 1;
    align-self: center;
    height: 100%;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed header {
    font-size: 24px;
    font-family: "Druk Wide Cyr", Arial, sans-serif;
    font-weight: 600;
    line-height: 30px;
    text-transform: uppercase;
    color: #000;
    white-space: nowrap;
    text-align: center;
    margin-bottom: 40px;
}

.garderobo-widget-popup-content .garderobo-widget-feed header  {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: calc(100% - 75px);
    align-items: center;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-popular .garderobo-widget-feed-container {
    width: 100% !important;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
    background-position: 0 -22px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right, .platform-widget-control-left, .platform-widget-control-right {
    width: 40px;
    height: 40px;
    background-color: #000;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right::after, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left::after, .platform-widget-control-left::after, .platform-widget-control-right::after {
    content: "";
    width: 20px;
    height: 20px;
    display: block;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3E%3Cpath%20d='M12%204L6%2010L12%2016'%20stroke='white'%20stroke-width='2'/%3E%3C/svg%3E"); 
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right::after {
    transform: rotate(180deg);   
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    height: 100%;
    width: 100%;
}

.garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    width: 32%;
    padding-bottom: 33.33%;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}

/* STANDART LIST */
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 25%;
    border: 1px solid white;
    padding: 0;
    margin: 0;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 342px;
    width: 342px;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: auto;
    flex-direction: column-reverse;
    padding: 10px 10px 20px 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: none;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    margin: 6px 0 0 !important;
    line-height: 24px;
    font-size: 16px;
    color: #000;
    text-align: center;
    border: none;
}

.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    line-height: 20px;
    font-size: 16px;
    color: #000;
}


.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item:hover {
    border-color: transparent;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    align-items: center;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price, .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    color: #000;
    font-size: 12px;
}

.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-feed-item .garderobo-widget-product-price {
    color: #e6233a;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    font-family: "Proxima Nova Condensed";
    margin-right: 12px;
    padding: 0;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    font-size: 16px;
    padding: 0 0 0 12px;
    order: 2;
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-feed-item .garderobo-widget-product-old-price {
    font-family: "Proxima Nova Condensed";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding: 0;
    color: #000;
    height: fit-content;
}

.garderobo-widget-container .garderobo-widget-feed-looks .garderobo-widget-product-price-container {
    align-items: center;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
    font-size: 20px;
    color: #e6233a;
    font-weight: 600;
    line-height: 24px;
}

.garderobo-widget-product-price-container:has(div:nth-child(1):last-child) div {
    color: #000 !important;
}

.garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1 / 1;
    max-width: 100%;
    width: 100%;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 0;
}

/* COLLAGES POPUP */
.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    height: 100%;
    margin: 0;
    border: none;
    border-right: 1px solid #E0E0E0;
    align-self: unset;
}

.garderobo-widget-popup-content {
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
    padding: 0;
    border-right: none;
}

.garderobo-widget-popup .garderobo-widget-feed {
    border: none;
    align-self: unset;
    margin-top: 0;
    margin-right: 0;
    height: 100%;
    flex: 1;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    height: unset !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-bottom: 0 !important;
}

.garderobo-widget-popup-list-header {
    display: none;
}

.garderobo-widget-popup-list-content {
    height: calc(100% - 24px);
    margin: 0;
    border: none;
    padding: 0 40px;
    margin-left: 20px !important;
    border-left: 1px solid #e0e0e0 !important;
}

.garderobo-widget-popup-list-item:first-child {
    border-top: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 40px 0;
    width: unset;
    gap: 24px;
    border-top: 1px solid #e0e0e0;
    margin-bottom: 10px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
    border-bottom: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 300px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
}

.garderobo-widget-popup-list-item-like-button, .garderobo-widget-popup-list-swap-item-like-button {
    width: 20px;
    height: 17px;
    position: absolute;
    right: 16px;
    top: 16px;
    cursor: pointer;
    background: url(https://platform-static.modatech.ru/like-bordered.svg) no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
    z-index: 1;
}

.garderobo-widget-popup-list-item-text-brand {
    display: none !important;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 0;
    font-size: 16px;
    font-weight: normal;
    line-height: 20px;
    font-style: normal;
}

.garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 24px;
}

.garderobo-widget-popup-list-item-text-prices {
    font-weight: 600;
}

.garderobo-widget-popup-list-item-text-new-price {
    color: #e6233a;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    font-family: "Proxima Nova Condensed";
    margin-right: 12px;
    padding: 0;
}

.garderobo-widget-popup-list-item-text-discount {
    font-family: "Proxima Nova Condensed";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding: 0;
    color: #000;
    height: fit-content;
}

.garderobo-widget-popup-list-item-text-prices {
    flex-direction: row-reverse;
    align-items: center;
    gap: 0; 
    padding: 0;
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-sizes {
    font-family: "Proxima Nova Condensed";
    width: 100%;
    border-radius: 8px;
    font-size: 16px !important;
    line-height: 20px;
    font-weight: 400;
    margin-right: 0 !important;
    height: 48px;
    padding: 10px !important;
    position: relative;
    margin-bottom: 10px;
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-popup-actions-controls button {
    width: 100%;
    height: 48px;
    padding: 12px 0;
    border-radius: 8px;
    border: none;
    font-family: "Proxima Nova Condensed", Arial, sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    cursor: pointer;
    transition: .3s;
    color: #fff;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    text-transform: none;
    background-color: #e6233a;
    margin-top: 0;
    margin-bottom: 10px;
    width: 100%;
    height: 48px;
    padding: 12px 0;
    border-radius: 8px;
    border: none;
    font-family: "Proxima Nova Condensed", Arial, sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    cursor: pointer;
    transition: .3s;
    color: #fff;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text {
    padding-right: 20px;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-cart-btn {
    margin-bottom: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-prices {
    width: fit-content;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-bottom > div {
    width: 100%;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-img-v2 {
    object-fit: contain;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 20px 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    padding: 0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-content {
    height: calc(100% - 80px);
    border-bottom: 1px solid #e0e0e0;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-header {
    font-size: 20px;
    font-family: "Druk Wide Cyr", Arial, sans-serif;
    font-weight: 600;
    line-height: 26px;
    text-transform: uppercase;
    color: #000;
    white-space: nowrap;
    text-align: center;
}

.garderobo-widget-popup-list-item-swap-button {
    display: block !important;
    background-color: #000;
}

.garderobo-widget-popup-list-item-swap-button:hover {
    background-color: #19190A;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

/* SWAP CONTAINER */
.garderobo-widget-popup-list-item-swap-container {
    padding-bottom: 8px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 170px;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    display: none;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: relative;
    height: 80%;
    width: 100%;
    border: none !important;
    justify-content: start;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
    padding: 10px;
}

.garderobo-widget-popup-list-item-swap-item-img {
    max-height: 160px;
}

.garderobo-widget-popup-list-item-name {
    width: 100%;
    margin: 0 !important;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 300 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    text-align: center;
    margin-top: 10px !important;
    display: block !important;
    max-height: 65px;
    height: 70px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
    height: calc(100% - 20px);
    overflow-y: auto;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 14px;
    border: 1px solid #ccc;
    display: flex;
    align-items: center;
    height: fit-content;
}

.garderobo-widget-popup-list-item-swap-item-price {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    height: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-similar {
    display: none;;
}

.garderobo-widget-look__btn-buy {
    height: 48px;
    transition: .3s;
    color: #fff;
    border: none;
    border-radius: 8px;
    background: #e6233a;
    box-shadow: none;
    font-family: "Proxima Nova Condensed", Arial, sans-serif;
    font-size: 16px;
    line-height: 20px;
    font-weight: 400;
    text-transform: capitalize;
    padding-inline: 25px;
    bottom: -68px;
}

.garderobo-widget-look__btn-buy:hover, .garderobo-widget-popup-list-item-text-cart-btn:hover, .garderobo-multiple-btn:hover {
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.1) 100%), #e6233a;
}

.garderobo-grid-look__product_img[data-product-type="socks"] {
    display: none;
}

.garderobo-grid-look__product_img[data-product-type="hat"][data-already-in-collage="1"], .garderobo-grid-look__product_img[data-product-type="bag"][data-already-in-collage="1"] {
    display: none;
}

.garderobo-scroll-button, .garderobo-scroll-button:hover {
    background-color: #000;
    z-index: 1;
    right: 52px;
    left: unset;
}

.garderobo-multiple-btn {
    width: 198px;
    height: 48px;
    border-radius: 8px;
    border: none;
    font-family: "Proxima Nova Condensed", Arial, sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    cursor: pointer;
    transition: .3s;
    color: #fff;
    background-color: #e6233a;
}

.garderobo-multiple-controls {
    margin-top: 12px;
}

.modatech-platform-look-like-state-button-liked, .garderobo-like-button-liked-state {
    background: url('https://testplatform-static.modatech.ru/like-filled.svg') no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
}

/* PLATFORM */
.modatech-looks {
    margin-bottom: 40px;
}

.modatech-look-widgets {
    position: relative;
    height: 100%;
    display: flex;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    padding: 0 24px 0 10px;
}

.modatech-look-widgets-centered {
    padding: 0 10px;
}

.platform-widget-control-left, .platform-widget-control-right {
    position: absolute;
}

.platform-widget-control-left::after, .platform-widget-control-right::after {
    margin: auto;
}

.platform-widget-control-left {
    left: 0;
    top: 50%;
}

.platform-widget-control-right {
    right: 0;
    top: 50%;
    transform: rotate(180deg);
}

.modatech-look-widget-container {
    flex: 0 0 calc(33.33% - 19px);
    height: auto;
    transition: transform 0.5s ease;
    margin: 0 12px;
}

.modatech-look-widget-container img {
    cursor: pointer;
}

.modatech-look-widgets-title {
    text-align: center;
    margin-bottom: 0;
    padding-bottom: 30px;
}

.modatech-look-widgets.modatech-look-widgets-centered {
    justify-content: center;
}

.garderobo-widget-popup-container .garderobo-grid-look__platform-product img {
    width: 100%;
    object-fit: contain;
}


/* DESKTOP */
@media screen and (min-width: 1280px) {
    .garderobo-widget-container {
        max-width: 1506px;
        padding: 0 20px;
        margin: 0 auto;
    }

    .catalog-modatech-widgets .garderobo-widget-container {
        padding: 0 !important;
    }

    .garderobo-widget-container.garderobo-widget-container-for-popup {
        max-width: unset;
        padding: unset;
        margin: unset;
    }

    .garderobo-widget-popup__btn-close {
        padding: 10px;
    }

    .garderobo-widget-popup__btn-close {
        right: 20px;
        top: 20px;
    }
}

@media screen and (min-width: 769px) {
    .garderobo-widget-popup {
        height: 80vh;
        padding: 20px;
        max-width: 1300px;
        max-height: 1000px;
    }

    .garderobo-widget-popup.garderobo-looks-simple-popup {
        max-width: 650px;
        max-height: 650px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 50%;
        padding-top: 20px;
        height: calc(100% + 20px);
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 100%;
        max-width: 100%;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-content {
        margin-left: 0 !important;
        padding: 0;
    }

    .garderobo-widget-popup-list-item-like-button:hover, .modatech-platform-look-like-state-button:hover, .garderobo-widget-popup-list-swap-item-like-button:hover {
        background: url('https://testplatform-static.modatech.ru/like-filled.svg') no-repeat;
        background-size: contain;
        transition: background 0.3s ease;
    }
}

@media screen and (max-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-popular .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33.33%;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
        background-position-y: center;
        background-origin: content-box;
        aspect-ratio: 1 / 1;
        height: unset;
    }
}

/* MOBILE */
@media screen and (max-width: 768px) {
    .garderobo-widget-popup-list-item-text-title {
        padding-right: 20px;
    }

    .garderobo-widget-container .garderobo-widget-feed {
        margin-bottom: 0;
        justify-content: center;
        height: 100%;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed {
        border: none !important;
        margin-left: 0 !important;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-family: "Druk Wide Cyr";
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
        margin-bottom: 20px;
        white-space: wrap;
        text-align: start;
    }

    .garderobo-widget-feed.garderobo-widget-feed-popular header {
        text-align: center !important;
        font-size: 14px;
    }

    .garderobo-widget-popup-list-content {
        margin-left: 0 !important;
        padding: 0;
        border: 1px solid #e0e0e0 !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        padding: 12px;
    }

    .garderobo-widget-popup-list-item-like-button, .garderobo-widget-popup-list-swap-item-like-button {
        top: 12px;
        right: 12px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        max-width: 140px;
        padding: 0 0 0 10px;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        margin: 0 !important;
    }

    .garderobo-widget-popup-collage-container {
        height: 368px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
        flex: unset;
    }

    .garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: 0;
        top: 50%;
    }

    .garderobo-widget-container .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: 0;
        top: 50%;
    }

    .garderobo-multiple-controls {
        flex-direction: column;
        gap: 10px;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text {
        padding-right: 10px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        gap: 16px;
    }
}

@media screen and (max-width: 576px) {
    .garderobo-widget-popup {
        max-height: calc(100vh - 20px) !important;
        width: auto !important;
        padding: 10px;
        border-radius: 10px 10px 0 0;
        top: 100%;
        transition: top 0.7s ease;
        position: fixed !important;;
    }

    .garderobo-widget-popup-container--opened .garderobo-widget-popup {
        top: 20px;
        position: relative !important;
    }

    .garderobo-widget-popup-container {
        top: 100% !important;
        display: unset !important;
        z-index: 99999 !important;
    }

    .garderobo-widget-popup-container.garderobo-widget-popup-container--opened {
        top: 0 !important;
    }

    .garderobo-looks-simple-popup .garderobo-widget-popup-list-header {
        padding: 0;
        margin-top: 24px;
    }

    .garderobo-widget-container .garderobo-multiple .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-popular .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }

    .garderobo-widget-feed-popular .garderobo-widget-control-right {
        right: 0 !important;
    }

    .garderobo-widget-feed-popular .garderobo-widget-control-left {
        left: 0 !important;
    }

    .catalog-modatech-widgets .garderobo-widget-container {
        padding: 0 20px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
        flex-wrap: wrap;
        justify-content: center;
    }

    .modatech-look-widgets {
        gap: 0;
        padding: 0;
    }

    .modatech-look-widget-container {
        flex: 0 0 auto !important;
        width: 100%;
        margin: 0;
    }

    .modatech-look-widget-container img {
        margin: auto;
    }

    .modatech-look-widgets .platform-widget-control-left {
        left: 0;
    }

    .modatech-look-widgets .platform-widget-control-right {
        right: 0;
    }

    .modatech-look-widgets.modatech-look-widgets-centered {
        justify-content: unset;
    }
}

.garderobo-widget-look-container div[data-category-collage="hat_cap"] {
    display: none;
}