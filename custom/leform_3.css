/* LEFORM */
.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 80px;
}

.garderobo-widget-container .garderobo-widget-feed header {
    color: #000;
    white-space: nowrap;
    text-align: left;
    font-family: HelveticaNeueCyr, sans-serif;
    font-weight: 500;
    font-size: 26px;
    margin: 48px 0 38px;
}

.garderobo-shopthemodellook {
    z-index: 1 !important;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    flex-flow: column wrap;
    justify-content: flex-start;
    height: auto;
    user-select: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    font-family: Formular, sans-serif;
}

.garderobo-widget-feed-item-look .garderobo-widget-feed-items {
    display: flex;
    gap: 10px !important;
    -webkit-scroll-snap-type: x mandatory;
    -moz-scroll-snap-type: x mandatory;
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory;
    -webkit-scroll-behavior: smooth;
    -moz-scroll-behavior: smooth;
    -ms-scroll-behavior: smooth;
    scroll-behavior: smooth;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    flex: 0 0 calc((100% / 6) - (5 * 10px / 6));
    padding: 0 !important;
    width: auto !important;
    -webkit-scroll-snap-align: start;
    -moz-scroll-snap-align: start;
    -ms-scroll-snap-align: start;
    scroll-snap-align: start;
    -webkit-scroll-snap-stop: normal;
    -moz-scroll-snap-stop: normal;
    -ms-scroll-snap-stop: normal;
    scroll-snap-stop: normal;
    background: linear-gradient(0.06deg, rgba(136, 136, 136, 0.08) 0.08%, rgba(136, 136, 136, 0) 141.24%);
    cursor: pointer;
    margin-bottom: 0 !important;
}

.garderobo-widget-container .garderobo-widget-popup-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items,
.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-similar .garderobo-widget-feed-container .garderobo-widget-feed-items {
    gap: 0;
}

.garderobo-widget-feed-similar .garderobo-widget-feed-items, 
.garderobo-widget-feed-recommendations .garderobo-widget-feed-items,
.garderobo-widget-feed-popular .garderobo-widget-feed-items {
    display: flex;
    gap: 20px !important;
    -webkit-scroll-snap-type: x mandatory;
    -moz-scroll-snap-type: x mandatory;
    -ms-scroll-snap-type: x mandatory;
    scroll-snap-type: x mandatory;
    -webkit-scroll-behavior: smooth;
    -moz-scroll-behavior: smooth;
    -ms-scroll-behavior: smooth;
    scroll-behavior: smooth;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
}

.garderobo-widget-feed-similar .garderobo-widget-feed-items .garderobo-widget-feed-item,
.garderobo-widget-feed-recommendations .garderobo-widget-feed-items .garderobo-widget-feed-item,
.garderobo-widget-feed-popular .garderobo-widget-feed-items .garderobo-widget-feed-item {
    flex: 0 0 calc((100% / 5) - (4 * 20px / 5));
    padding: 0 !important;
    width: auto !important;
    -webkit-scroll-snap-align: start;
    -moz-scroll-snap-align: start;
    -ms-scroll-snap-align: start;
    scroll-snap-align: start;
    -webkit-scroll-snap-stop: normal;
    -moz-scroll-snap-stop: normal;
    -ms-scroll-snap-stop: normal;
    scroll-snap-stop: normal;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-look__btn-view {
    display: block !important;
    position: absolute;
    right: 8px;
    bottom: 8px;
    background-color: #F8F8F8;
    height: 30px;
    width: 88px;
    font-size: 14px;
    font-weight: 400;
    line-height: 30px;
    font-family: HelveticaNeueCyr, sans-serif;
    color: #000 !important;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-look__btn-view:hover {
    background-color: #fff;
}

.garderobo-widget-feed-item-look-centered .garderobo-widget-feed-items {
    justify-content: center !important;
}

.garderobo-widget-popup-content .garderobo-widget-feed-item-look-centered .garderobo-widget-feed-items {
    justify-content: space-between !important;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: auto;
    width: 100%;
    padding-top: 150%;
    background-size: contain;
    margin-bottom: 20px;
    background-repeat: no-repeat;
}

.garderobo-widget-container .garderobo-widget-feed-item_inner {
    flex-direction: column;
    flex-wrap: nowrap;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    flex-direction: column !important;
    font-size: 12px;
    align-items: flex-start !important;
    justify-content: space-between;
    width: 100% !important;
    height: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    text-align: left;
    font-size: 20px;
    font-weight: 500;
    color: #000;
    letter-spacing: 0.01em;
    width: 100%;
    overflow-wrap: break-word;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name:hover {
    color: rgb(128, 128, 128);
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    column-gap: 12px;
    width: 100%;
    font-family: HelveticaNeueCyr, sans-serif;
    font-size: 16px;
    color: #000;
    margin-top: 8px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    text-decoration: line-through;
    color: rgb(128, 128, 128);
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header {
    height: 20px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    top: initial;
    bottom: -50px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    background-color: #000;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.garderobo-widget-popup-content .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-popup-content .garderobo-widget-feed .garderobo-widget-control-right {
    top: calc(50% - 24px);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 5px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 5px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    width: 18px;
    height: 18px;
    content: '';
    display: block;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTEuOTc0OSAxNkw0Ljk0OTc3IDguOTc0ODVMMTEuOTc0OSAxLjk0OTc0IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiPjwvcGF0aD48L3N2Zz4=);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTEuOTc0OSAxNkw0Ljk0OTc3IDguOTc0ODVMMTEuOTc0OSAxLjk0OTc0IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiPjwvcGF0aD48L3N2Zz4=);
    transform: rotate(180deg);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:hover::before,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:hover::before {
    opacity: 1;
}

.garderobo-widget-container .garderobo-widget-feed.garderobo-widget-scroll .garderobo-widget-feed-container .garderobo-widget-add-to-cart-btn {
    line-height: 30px;
    text-transform: uppercase;
    text-align: center;
    letter-spacing: 0.1em;
    font-weight: 500;
    cursor: pointer;
    box-sizing: border-box;
    border: none;
    background-color: rgb(0, 0, 0);
    color: rgb(255, 255, 255);
    transition: opacity 0.3s ease 0s;
    width: 100%;
    height: 70px;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    -webkit-box-pack: center;
    justify-content: center;
    font-size: 14px;
    padding: 18px 0 14px;
    max-width: 175px;
    position: relative;
}

.garderobo-scroll-button {
    position: absolute;
    left: unset;
    top: unset;
    right: 12px;
    bottom: 12px;
    border-radius: 0;
    background: #F8F8F8 !important;
    width: auto;
    height: 38px;
    padding: 0 16px;
    display: flex;
    flex-direction: row-reverse;
    font-size: 14px;
    line-height: 30px;
    font-family: HelveticaNeueCyr, sans-serif;
    color: rgba(0, 0, 0, 0.8);
    cursor: pointer;
    gap: 10px;
    text-transform: none;
}

.garderobo-scroll-button:hover {
    background-color: #fff;
}

.garderobo-scroll-button span {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIiIGhlaWdodD0iMjIiIHZpZXdCb3g9IjAgMCAyMiAyMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyLjM4OTUgMTAuODU3M0wyMC40MzkxIDE1LjExNjFDMjEuMzYwOSAxNS42MDM4IDIxLjAxNDMgMTcgMTkuOTcxNSAxN0gxLjYyNzY5QzAuNjEyMTc0IDE3IDAuMjQzMjI4IDE1LjY2MTYgMS4xMTUzMiAxNS4xNDEyTDEwLjcxMjkgOS40MTQ4NEMxMS44MjMzIDguNzUyMjggMTMuMDI3NSA3Ljk3ODc2IDEzLjMzOTUgNi43MjM4NkMxNC4xMDMyIDMuNjUyMjQgOC43MzQwNCAyLjU2NzA0IDguNzM0MDQgNi43MzYyMiIgc3Ryb2tlPSJibGFjayIgc3Ryb2tlLXdpZHRoPSIxLjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4=');
    background-repeat: no-repeat;
    background-size: contain;
    border: none !important;
    transform: unset !important;
    width: 22px !important;
    height: 22px !important;
}
  

.garderobo-widget-product-price {
    line-height: 20px;
    letter-spacing: 0.01em;
}

.garderobo-shopthemodellook__image {
    position: sticky;
    top: 0;
}

.garderobo-shopthemodellook__sizes_text {
    padding-top: 1px;
}

.garderobo-shopthemodellook .garderobo-disabled-select,
.garderobo-shopthemodellook .garderobo-disabled-select select {
    background-color: #f0f0f0;
}

.garderobo-leform-uni {
    display: none;
}

.garderobo-shopthemodellook {
    margin-bottom: 40px !important;
}
.garderobo-widget-feed-similar {
    margin-bottom: 72px;
    order: 1;
}

.garderobo-widget-look__btn-buy {
    display: none;
}

.garderobo-widget-feed-item--special .garderobo-widget-look-container {
    border: none;
}

.garderobo-shopthemodellook__sizes_wrap {
    height: 64px;
    box-sizing: border-box;
}

.garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-look-container {
    padding: 0 !important;
    width: 90%;
    margin: auto;
    aspect-ratio: 1 / 1;
}

.garderobo-widget-feed-item--special .garderobo-widget-look-container .garderobo-widget-look__badge-special {
    display: none;
}
/* ===================== Custom Select ==================== */

.garderobo-custom-selected-value {
    font-size: 14px;
    line-height: 20px;
    font-weight: 700;
    letter-spacing: 0.01em;
    color: #000;
}

.garderobo-widget-sizes {
    width: calc(100% + 2px);
    left: -1px;
}

.garderobo-custom-select-icon {
    background: url('./icons/arrow-down.svg') center / contain no-repeat;
    position: absolute;
    right: 12px;
}

.garderobo-custom-selected-txt {
    color: #808080;
    line-height: 30px;
    font-weight: 500;
    padding-right: 8px;
}

/* ================== Shared looks ==================== */
.modatech-look-widgets {
    display: flex;
    gap: 16px !important;
    flex-wrap: wrap !important;
    border: none;
    padding: 40px;
}

.modatech-look-widget-container {
    max-width: calc(25% - 14px) !important;
    position: relative;
    cursor: pointer;
}

.modatech-platform-overlay {
    display: flex;
    position: absolute;
    width: 100%;
    height: 100%;
    background: #000;
    top: 0;
    left: 0;
    align-items: center;
    opacity: 0.5;
}

.modatech-platform-overlay p {
    color: #fff;
    padding-left: 10px;
    font-size: 14px;
    text-align: center;
}

.modatech-platform-loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #000;
    border-radius: 50%;
    min-width: 30px;
    min-height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.gw-look-widgets-shared .garderobo-widget-popup-collage-container .garderobo-widget-feed {
    margin-right: 20px !important;
    margin-left: 0 !important;
}

.garderobo-grid-look__product-disabled-layout img {
    opacity: 0.2;
}

.garderobo-grid-look__product-disabled-layout::before {
    content: "Нет в наличии";
    display: flex;
    position: absolute;
    justify-content: center;
    height: 100%;
    width: 50%;
    left: 25%;
    text-transform: uppercase;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    text-align: center;
}

.garderobo-widget-popup {
    padding: 20px;
    box-sizing: border-box;
}

.garderobo-widget-popup__btn-close {
    padding: 7px !important;
    right: 10px !important;
    top: 10px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    padding: 0;
    border-right: none !important;
    background: linear-gradient(0.06deg, rgba(136, 136, 136, 0.08) 0.08%, rgba(136, 136, 136, 0) 141.24%);
    width: calc(50% - 42px);
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-container {
    background: rgba(248, 248, 248, 1);
}

.garderobo-widget-popup-list-header, .garderobo-widget-popup-content .garderobo-widget-feed-header {
    display: none;
}

.garderobo-widget-popup-list-content {
    height: 100% !important;
    margin: 0;
    border: none;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 0;
    margin-bottom: 12px;
    padding-top: 0;
    width: unset;
    gap: 12px;
    border-top: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item:first-child {
    margin-top: 16px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 150px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
    border-bottom: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 230px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    letter-spacing: 1px;
}

.garderobo-widget-popup-list-item-text {
    margin: 0 !important;
    flex: 1;
    font-family: Formular, sans-serif;
    overflow: unset;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    width: 100%;
    height: 50px;
    font-size: 14px !important;
    line-height: 30px;
    font-weight: 550 !important;
    color: #fff !important;
    letter-spacing: 0.1em;
    background-color: #000 !important;
    text-transform: uppercase !important;
    text-align: center !important;
    padding: 0 !important;
    margin: 8px 0 !important;
    font-family: "HelveticaNeueCyr",sans-serif;
}

.garderobo-widget-popup-list-item-swap {
    display: none !important;
}

.garderobo-widget-popup-list-item-text-cart-btn--disabled {
    opacity: 0.5;
}

.garderobo-widget-popup-list-item-text-prices {
    margin: 0;
    padding: 0;
    font-family: "HelveticaNeueCyr",sans-serif;
    gap: 15px;
    display: flex;
    align-items: end;
}

.garderobo-widget-popup-list-item-text-bottom {
    margin: 4px 0 24px !important;
}

.garderobo-widget-popup-container .garderobo-widget-sizes {
    height: 50px;
    border: 1px solid #C7C7C7;
    background-color: transparent;
    width: 100%;
    border-radius: 0 !important;
    margin: 0;
    color: #808080;
    text-transform: uppercase;
    font-size: 12px;
    line-height: 30px;
    font-weight: 500;
}
.garderobo-widget-sizes-custom-dropdown {
    position: relative;
}

.garderobo-widget-sizes-custom-dropdown-icon {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC44OTkgMC45NDk3NDdMNS45NDkyMiA1Ljg5OTQ5TDAuOTk5NDcxIDAuOTQ5NzQ3IiBzdHJva2U9IiNCM0IzQjMiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K');
    background-repeat: no-repeat;
    width: 12px;
    height: 7px;
    transition: 0.15s cubic-bezier(1,0.5,0.8,1);
}

.garderobo-widget-sizes-custom-dropdown-icon-rotate {
    transform: rotate(-180deg);
}

.garderobo-widget-sizes-custom-dropdown-selected {
    border: 1px solid #C7C7C7;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
    cursor: pointer;
}

.garderobo-widget-sizes-custom-dropdown-items {
    position: absolute;
    top: 52px;
    background-color: #fff;
    border: 1px solid #C7C7C7;
    z-index: 99;
    width: calc(100% - 2px);
    max-height: 168px;
    overflow-y: auto;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #000;
}

.garderobo-widget-sizes-custom-dropdown-selected-text {
    color: #808080;
    font-size: 12px;
    line-height: 30px;
    font-weight: 500;
    font-family: "HelveticaNeueCyr",sans-serif;
    text-transform: uppercase;
}

.garderobo-widget-sizes-custom-dropdown-selected-text span {
    color: #808080;
    font-size: 12px;
    line-height: 30px;
    font-weight: 500;
    padding-right: 8px;
}

.garderobo-widget-sizes-custom-dropdown-items::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.garderobo-widget-sizes-custom-dropdown-items::-webkit-scrollbar-thumb {
    border-radius: 4px;
    background-color: #DADFE9;
}

.garderobo-widget-sizes-custom-dropdown-item {
    padding: 10px 20px;
    cursor: pointer;
}

.garderobo-widget-sizes-custom-dropdown-item:hover {
    background-color: #F6F6F6;
}

.garderobo-widget-sizes-custom-dropdown-item-selected {
    background-color: #F6F6F6;
}

.garderobo-widget-sizes-custom-dropdown-items-hidden {
    display: none;
}

.garderobo-custom-selected {
    justify-content: flex-start;
    gap: 0;
}

.garderobo-widget-popup-list-item-text-discount {
    color: #808080;
    font-size: 15px;
}

.garderobo-widget-popup-list-item-text-new-price {
    color: #000 !important;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed {
    border: none;
    border-right: 1px solid #E0E0E0;
    margin: 0 0 !important;
    height: 100%;
}
.garderobo-widget-popup-content .garderobo-widget-feed {
    align-self: unset;
    margin-top: 0 ;
    margin-right: 0 !important;
}

.garderobo-widget-popup .garderobo-widget-feed {
    border: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-feed-item-look {
    width: calc(100% - 75px) !important;
    aspect-ratio: 1 / 1;
    height: 100%;
}
.garderobo-widget-popup-content .garderobo-widget-feed-items {
    height: 100%;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset !important;
    padding: unset !important;
    min-width: 140px;
}

.garderobo-widget-popup-list-item-swap-container-item:not(.garderobo-widget-popup-list-item-swap-container-item--not-available) {
    position: unset !important;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px 0 !important;
    position: relative !important;
    height: 83% !important;
    width: 100% !important;
    border: none !important;
    justify-content: start !important;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset !important;
    height: 140px!important;
    padding: 10px !important;
    background-color: #fff;
}

.garderobo-widget-popup-list-item-name {
    margin: 0 !important;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 400 !important;
    line-height: 18px;
    letter-spacing: 0.5px;
    text-align: left;
    margin-top: 10px !important;
    display: block !important;
    font-family: Formular, sans-serif;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: auto;
}

.garderobo-multiple {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-item--not-available {
    position: relative !important;
}

.garderobo-widget-popup-list-item-swap-container-item--not-available::after {
    content: "Данный товар закончился, но у нас есть похожее";
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    color: #000;
    display: flex;
    padding: 0;
    text-transform: uppercase;
    text-align: center;
    padding-top: 75px;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    justify-content: center;
    top: 0;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-swap-button {
    display: block !important;
    width: 100%;
    border: 1px solid #000000;
    height: 50px;
    font-weight: 500;
    font-size: 14px;
    text-transform: uppercase;
    transition: .3s ease background-color,color;
    color: #000;
    font-family: "HelveticaNeueCyr",sans-serif;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-swap-button:hover {
    background-color: #000000;
    color: #FFFFFF;
}

.garderobo-widget-popup-list-item-swap-container-list {
    background-color: #F8F8F8;
    gap: 3px;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    height: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin-left: 0;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    display: flex !important;
    align-items: center;
    width: 100%;
    margin-top: 4px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    column-gap: 15px;
    max-height: 40px;
    overflow-y: scroll;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    font-size: 12px;
    line-height: 20px;
    color: #8E8E8E;
    font-weight: 400;
    font-family: "HelveticaNeueCyr",sans-serif;
}

.garderobo-widget-popup-content .garderobo-widget-control-left {
    left: 12px !important;
}

.garderobo-widget-popup-content .garderobo-widget-control-right {
    right: 12px !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    border: none !important;
    padding: 0 !important;
    -webkit-scroll-snap-align: start;
    -moz-scroll-snap-align: start;
    -ms-scroll-snap-align: start;
    scroll-snap-align: start;
    -webkit-scroll-snap-stop: normal;
    -moz-scroll-snap-stop: normal;
    -ms-scroll-snap-stop: normal;
    scroll-snap-stop: normal;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1 / 1;
    max-width: 100%;
    max-height: 100%;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed {
    margin: 0 !important;
}

.garderobo-widget-popup .garderobo-widget-look__label-look-name, .garderobo-widget-popup .garderobo-widget-look__label-look-link {display: none !important;}

.garderobo-widget-popup {
    padding: 32px 0 0;
    box-sizing: border-box;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    padding-bottom: 0 !important;
    margin-bottom: 0 !important;
    height: 100%;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    width: auto !important;
    flex: 0 0 100%;
}

.garderobo-widget-popup-content {
    border: none;
    gap: 12px;
}

.garderobo-widget-popup-container .garderobo-grid-look__platform-product img {
    cursor: pointer;
    width: 100%;
    object-fit: contain;
}

.gw-folder-title {
    color: #000;
    white-space: nowrap;
    text-align: center;
    font-family: HelveticaNeueCyr, sans-serif;
    font-weight: 500;
    font-size: 26px;
}

.garderobo-widget-popup-list-item-swap-item-price {
    color: #000;
    font-family: "HelveticaNeueCyr",sans-serif;
    text-align: start;
    padding: 0 !important;
    text-decoration: none;
    display: flex;
    font-size: 12px;
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 4px;
}

.garderobo-widget-popup-list-item-swap-item-price--old {
    display: block !important;
    color: #808080;
    text-decoration: line-through;
}

@media screen and (max-width: 1440px) {
    .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 20%;
    }
}

@media screen and (max-width: 1024px) {
    .garderobo-widget-feed-item-look-centered .garderobo-widget-feed-items {
        justify-content: unset !important;
    }

    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        height: 320px;
        flex: 0 0 calc((100% / 4) - (3 * 10px / 4))
    }
}

@media screen and (max-width: 901px) {
    .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33.3%;
    }

    .garderobo-widget-feed-similar .garderobo-widget-feed-items .garderobo-widget-feed-item,
    .garderobo-widget-feed-recommendations .garderobo-widget-feed-items .garderobo-widget-feed-item,
    .garderobo-widget-feed-popular .garderobo-widget-feed-items .garderobo-widget-feed-item {
        flex: 0 0 calc((100% / 3) - (2 * 16px / 3));
    }

    .garderobo-widget-feed-similar .garderobo-widget-feed-items,
    .garderobo-widget-feed-recommendations .garderobo-widget-feed-items,
    .garderobo-widget-feed-popular .garderobo-widget-feed-items {
        gap: 16px !important;
    }
}

@media screen and (max-width: 770px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: 15px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: 15px;
    }

    .garderobo-widget-popup .garderobo-widget-feed .garderobo-widget-control-right,
    .garderobo-widget-popup .garderobo-widget-feed .garderobo-widget-control-left {
        display: flex;
    }
}

@media screen and (max-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 24px;
    }

    .modatech-look-widget-container {
        max-width: calc(50% - 10px) !important;
    }

    .modatech-look-widgets {
        padding: 30px 20px 30px;
    }

    .garderobo-widget-popup {
        max-height: calc(100vh - 20px) !important;
        padding: 15px;
        border-radius: 10px 10px 0 0;
        position: fixed !important;
        top: 100% !important;
        transition: top 0.7s ease;
    }

    .garderobo-widget-popup-container--opened .garderobo-widget-popup {
        top: 20px !important;
    }

    .garderobo-widget-popup-container {
        top: 100% !important;
        display: unset !important;
        z-index: 99999 !important;
    }

    .garderobo-widget-popup-container--opened {
        top: 0 !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed-item-look {
        height: 90vw;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed {
        width: 100%;
        margin: 0 !important;
        padding: 0 !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
        max-height: 100%;
        width: auto;
        height: unset;
    }
}

@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed {
        justify-content: center;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 33.3%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item .garderobo-widget-product-image {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: space-between;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        font-size: 14px;
        font-weight: 500;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
        font-size: 12px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
        width: 100%;
        min-height: 502px;
        display: flex;
        align-items: center;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-content {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        flex-direction: column;
        width: calc(50% - 12px);
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        background-color: #fff;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        background: linear-gradient(0.06deg, rgba(136, 136, 136, 0.08) 0.08%, rgba(136, 136, 136, 0) 141.24%);
        width: 100%;
        height: 198px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item:first-child {
        margin-top: 0;
    }

    .garderobo-widget-popup-list-item-text {
        width: auto;
    }

    .garderobo-widget-popup-list-item-text-title {
        font-size: 14px;
        line-height: 17px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: auto;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item {
        gap: 8px;
    }

    .garderobo-widget-popup-list-item-text-prices {
        font-size: 12px;
        gap: 8px;
    }

    .garderobo-widget-popup-list-item-text-bottom {
        margin: 8px 0 16px !important;
    }


    .garderobo-widget-popup-list-item-text-discount {
        font-size: 12px;
    }

    .garderobo-widget-popup-list-item-swap-container {
        position: fixed;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
    }

    .garderobo-widget-popup-list-item-swap-container--hidden {
        display: none;
    }

    .garderobo-widget-popup-list-item-swap-container-content {
        max-height: 480px;
        background-color: #fff;
        height: 100%;
        padding: 32px 8px 0;
        position: relative;
    }

    .garderobo-widget-popup-list-item-swap-container-list {
        height: calc(100% - 52px);
        flex-wrap: wrap;
        overflow-y: auto;
        overflow-x: hidden;
        gap: 12px;
        padding: 0 18px;
    }

    .garderobo-widget-popup-list-item-swap-container-item {
        width: calc(50% - 6px);
    }

    .garderobo-widget-popup-list-item-swap-container-item-sizes {
        column-gap: 12px;
    }

    .garderobo-widget-popup-list-item-swap-item-content {
        padding: 0 !important;
        height: 80% !important;
    }

    .garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
        width: 4px;
        background: rgba(136, 136, 136, 0.08);
    }
    
    .garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
        background-color: #BABBC1;
        border-radius: 0;
    }

    .garderobo-widget-popup-list-item-swap-container-title {
        font-size: 26px;
        font-weight: 500;
        line-height: 28px;
        margin: 0;
        text-align: center;
        font-family: HelveticaNeueCyr, sans-serif;
        padding: 12px 0;
        background-color: #F8F8F8;
    }

    .garderobo-widget-popup-list-item-swap-container-close-icon {
        background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTQiIHZpZXdCb3g9IjAgMCAxNCAxNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE0IDEuNDFMMTIuNTkgMEw3IDUuNTlMMS40MSAwTDAgMS40MUw1LjU5IDdMMCAxMi41OUwxLjQxIDE0TDcgOC40MUwxMi41OSAxNEwxNCAxMi41OUw4LjQxIDdMMTQgMS40MVoiIGZpbGw9ImJsYWNrIi8+Cjwvc3ZnPgo=');
        background-repeat: no-repeat;
        width: 14px;
        height: 14px;
        position: absolute;
        right: 8px;
        top: 9px;
        cursor: pointer;
    }

    .garderobo-widget-popup__btn-close {
        right: 20px !important;
        top: 20px;
        padding: 10px !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-control-left {
        left: 6px !important;
    }
    
    .garderobo-widget-popup-content .garderobo-widget-control-right {
        right: 6px !important;
    }

    .garderobo-widget-look-container {
        margin: 0;
    }

    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        height: 320px;
        padding-bottom: 0 !important;
        flex: 0 0 calc((100% / 2) - (12px / 2));
    }

    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-look__btn-view {
        right: 2px;
        bottom: 2px;
        width: 80px;
        font-size: 12px;
    }

    .garderobo-widget-feed {
        padding: 0px 15px;
    }

    .modatech-look-widgets {
        padding: 25px 20px;
    }

    .garderobo-shopthemodellook__sizes_wrap {
        height: 42px;
    }

    .garderobo-widget-feed-similar .garderobo-widget-feed-items .garderobo-widget-feed-item,
    .garderobo-widget-feed-recommendations .garderobo-widget-feed-items .garderobo-widget-feed-item,
    .garderobo-widget-feed-popular .garderobo-widget-feed-items .garderobo-widget-feed-item {
        flex: 0 0 calc((100% / 2) - (12px / 2));
    }

    .garderobo-widget-feed-similar .garderobo-widget-feed-items,
    .garderobo-widget-feed-recommendations .garderobo-widget-feed-items,
    .garderobo-widget-feed-popular .garderobo-widget-feed-items {
        gap: 12px !important;
    }

    .garderobo-widget-popup-list-item-swap-container-item {
        height: 260px !important;
    }

    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items {
        gap: 12px;
    }
}

@media screen and (max-width: 500px) {
    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        height: 265px;
    }
}

@media screen and (max-width: 600px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 16px;
    }

    .garderobo-custom-selected-value {
        font-size: 12px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
        max-height: 104px;
    }

    .garderobo-widget-popup-list-item-text-cart-btn, .garderobo-widget-popup-list-item-swap-button {
        font-size: 12px !important;
    }
}

@media screen and (max-width: 425px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
        padding: 0 10px 0 0;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: space-between;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed-item-look {
        width: calc(100% - 20px) !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed-item {
        width: 100% !important;
    }
}

@media screen and (min-width: 767px) {
    .garderobo-widget-popup {
        height: 80vh !important;
        max-width: 920px;
        max-height: 610px !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 491px;
        max-width: 491px;
        padding-top: 0;
        height: 100%;
    }

    .garderobo-widget-popup-list-content {
        padding: 0 32px 0 16px !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        margin: 0 0 !important;
    }
}

@media screen and (min-width: 768px) {
    .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        margin: 0 0 !important;
    }
}


@media screen and (min-width: 1024px) {
    .garderobo-widget-container:not(.garderobo-widget-container-for-popup) .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
        padding-bottom: 0 !important;
        min-height: 446px;
    }
}


@media screen and (min-width: 1270px) {
    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        min-width: 183px;
        min-height: 274px;
    }
}

@media screen and (min-width: 1366px) {
    .garderobo-shopthemodellook__main-block {width: 35.3%;}
}

@media screen and (min-width: 1440px) {
    .garderobo-shopthemodellook__main-block {width: 33%;}
}

@media screen and (min-width: 1636px) {
    .garderobo-shopthemodellook__main-block {width: 31.1%;}
}

@media screen and (min-width: 1920px) {
    .garderobo-shopthemodellook__main-block {width: 29.4%;}
}


/* 404 PAGE */

.garderobo-404-page {
    padding: 0 40px;
}

.garderobo-404-page .garderobo-widget-control-left, .garderobo-404-page .garderobo-widget-control-right {
    display: none !important;
}

@media screen and (min-width: 1920px) {
    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items {
        flex-wrap: wrap;
        gap: 50px;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(20% - 50px);
        padding: 0;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 44px;
        font-weight: 500;
    }
}

@media screen and (min-width: 1200px) and (max-width: 1919px) {
    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items {
        flex-wrap: wrap;
        gap: 10px;
        row-gap: 32px;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(25% - 10px);
        padding: 0;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 44px;
        font-weight: 500;
    }
}

@media screen and (min-width: 768px) and (max-width: 1199px) {
    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items {
        flex-wrap: wrap;
        gap: 10px;
        row-gap: 32px;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(33.3% - 10px);
        padding: 0;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 30px;
        font-weight: 500;
    }

    .garderobo-404-page {
        padding: 0 20px;
    }
}

@media screen and (max-width: 767px) {
    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items {
        flex-wrap: wrap;
        gap: 15px;
        row-gap: 24px;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(50% - 10px);
        padding: 0;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 20px;
        font-weight: 500;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
        margin-top: 0;
    }

    .garderobo-404-page {
        padding: 0;
    }

    .garderobo-404-page .garderobo-widget-container .garderobo-widget-feed header {
        margin: 22px 0 24px;
    }

    .gw-look-widgets-shared .garderobo-widget-popup-collage-container .garderobo-widget-feed {
        margin: 0 !important;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        margin: 32px 0 32px;
    }

    .garderobo-widget-container .garderobo-widget-feed.garderobo-widget-feed-similar {
        margin-bottom: 50px;
    }

    .garderobo-widget-container .garderobo-widget-feed:not(.garderobo-shopthemodellook) .garderobo-widget-feed-container .garderobo-widget-feed-items {
        gap: 0;
    }

    .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item { 
        padding-right: 0 !important;
    }
}

.garderobo-widget-look__bottom-panel {
    display: none;
}