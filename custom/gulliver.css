.garderobo-widget-container {
    font-family: "Graphik LCG", Arial, sans-serif;
    margin-bottom: 100px;
}

.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 100px;
}

.garderobo-widget-feed.garderobo-widget-feed-similar {
    margin-bottom: 0;
}

.garderobo-scroll-button {
    z-index: 1;
    left: unset;
    right: 10px;
    background: #3F3F46;
    width: 60px;
    height: 60px;
    font-size: 9px;
    top: unset;
    bottom: 10px;
}

.garderobo-scroll-button:hover {
    background: #3F3F46 !important;
}

.garderobo-widget-container .garderobo-widget-feed header {
    color: #525665;
    font-size: 2.8125em;
    font-style: normal;
    font-weight: 400;
    letter-spacing: -.02em;
    line-height: 1em;
    margin-bottom: 20px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container { 
    width: calc(100% - 75px);
    align-items: center;
    height: 100%;
}
.garderobo-widget-container .garderobo-widget-feed-similar .garderobo-widget-feed-container {
    width: 100% !important;
}
.modatech-platform-look-like-state-button:hover, .garderobo-widget-popup-list-swap-item-like-button:hover, .modatech-platform-look-like-state-button-liked, .garderobo-like-button-liked-state, .garderobo-widget-popup-list-item-like-button:hover {
    background: url('https://testplatform-static.modatech.ru/like-filled.svg') no-repeat;
    background-size: contain;
}

.garderobo-like-button-liked-state {
    background: url('https://testplatform-static.modatech.ru/like-filled.svg') no-repeat !important;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
    background-position: 0 -22px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    text-indent: -99999px;
    display: block;
    width: 13px;
    height: 22px;
    transform: translate(0, -50%);
    cursor: pointer;
    color: transparent;
    border: 0;
    outline: 0;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAsBAMAAABMNS/cAAAAMFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABHcEx/v/MDAAAAEHRSTlP/+1dHd19n94T1o09tQAMAS5BP0wAAAKRJREFUGNNjeKX/Hwg+MCwQhdCBjPFg+pMAG5j+r8iQD6Y/CfCA6f8bGfzB9OcCFjD935zBH0x/nsABpv/fZGhApmHiUHUwfVBzYOZC7YHZC3MHzF0wd4LI/1oPwPRXgQVgOpAxAER/YxACyycw6oPoPwxCYPUOjPtB9BeGYrB+B3Z7EP2DYTLYvAbO+yg0TBymDq4PZg7cXJg9cHth7oC7C+hOAJlK4MmSzflFAAAAAElFTkSuQmCC);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
    height: 100%;
    width: 100%;
}

.garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    width: 32%;
    padding-bottom: 33.33%;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}

/* STANDART LIST */
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 22%;
    margin: 0 20px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    aspect-ratio: 1/1.2;
    width: 100%;
    display: block;
    padding: 20px 10px 0;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-y: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    flex-direction: column;
    align-items: flex-start;
    padding-top: 10px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    margin: 5px 0 !important;
    line-height: 17px;
    font-size: 13px;
    color: #525665;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price, .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    color: #525665;
    font-size: 16px;
    font-weight: 500;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    font-size: 10px;
    padding: 0 2px 0 5px;
    order: 2;
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
}

.garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1 / 1;
    max-width: 100%;
    width: 100%;
}

.garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-look-container  {
    position: relative;
}

.garderobo-widget-popup-content .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-look-container {
    position: absolute;
}
.garderobo-widget-feed-items .garderobo-widget-feed-item {
    padding: 0 0 20px !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 0;
}

/* COLLAGES POPUP */
.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    height: 100%;
    margin: 0;
    border: none;
    border-right: 1px solid #E0E0E0;
    align-self: unset;
}

.garderobo-widget-popup__btn-close {
    padding: 8px;
    right: 20px;
}

.garderobo-widget-popup-content {
    border: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
    padding: 0;
    border-right: none;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-header {
    display: none;
}

.garderobo-widget-popup .garderobo-widget-feed {
    border: none;
    align-self: unset;
    margin-top: 0;
    margin-right: 0;
    height: 100%;
    flex: 1;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    height: unset !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-bottom: 0 !important;
}

.garderobo-widget-popup-list-header {
    display: none;
}

.garderobo-widget-popup-list-content {
    height: calc(100% - 24px);
    margin: 0;
    border: none;
    padding: 0 40px;
    margin-left: 20px !important;
    border-left: 1px solid #e0e0e0 !important;
}

.garderobo-widget-popup-list-item:first-child {
    border-top: none !important;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 40px 0;
    width: unset;
    gap: 24px;
    border-top: 1px solid #e0e0e0;
    margin-bottom: 10px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
    border-bottom: none;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 300px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-swap {
    display: none;
}

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
}

.garderobo-widget-popup-list-item-like-button, .garderobo-widget-popup-list-swap-item-like-button {
    width: 20px;
    height: 20px;
    position: absolute;
    right: 0;
    top: 10px;
    cursor: pointer;
    background: url(https://platform-static.modatech.ru/like-bordered.svg) no-repeat;
    background-size: contain;
    transition: background 0.3s ease;
    z-index: 1;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 0;
    font-size: 16px;
    font-weight: 300;
    line-height: 19px;
    letter-spacing: 1px;
}

.garderobo-widget-popup-list-item-text-prices {
    margin-bottom: 24px;
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-sizes {
    width: 100%;
    border-radius: 3px;
    font-size: 18px !important;
    line-height: 20px;
    font-weight: 400;
    margin-right: 0 !important;
    height: 44px;
    padding: 10px !important;
    position: relative;
    margin-bottom: 10px;
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 0;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-popup-actions-controls button {
    width: 100%;
    padding: 12px 0;
    text-transform: unset;
    border-radius: 3px;
    font-weight: 400;
    font-size: 17px;
    margin: 0;
    text-align: center;
    cursor: pointer;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 3.125em;
    padding: .875em 1.875em;
    transition: .3s ease, color .3s ease;
    background-color: #3F3F46 !important;
    color: #fff;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    background: #2A2A2A;
    color: #fff;
    margin-bottom: 10px !important;
}

.garderobo-widget-popup-list-item-swap-button {
    display: block !important;
    border: 1px solid #BABBC1;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar {
    width: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-content::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}

/* SWAP CONTAINER */
.garderobo-widget-popup-list-item-swap-container {
    padding-bottom: 8px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 170px;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    display: none;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: relative;
    height: 80%;
    width: 100%;
    border: none !important;
    justify-content: start;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 75%;
    padding: 10px;
}

.garderobo-widget-popup-list-item-swap-item-img {
    max-height: 160px;
}

.garderobo-widget-popup-list-item-name {
    width: 100%;
    margin: 0 !important;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 300 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
    text-align: center;
    margin-top: 10px !important;
    display: block !important;
    max-height: 65px;
    height: 70px;
}

.garderobo-widget-look__btn-buy {
    border: none;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 3.125em;
    padding: .875em 1.875em;
    transition: background .3s ease, color .3s ease;
    background-color: #3F3F46;
    color: #fff;
    text-transform: capitalize;
    font-weight: 400;
    bottom: -85px;
}
.garderobo-widget-look__btn-buy:hover {
    background-color: #3F3F46;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    margin-left: -1px;
    margin-top: -1px;
    padding: 2px 4px;
    font-size: 12px;
    line-height: 14px;
    border: 1px solid #ccc;
}

.garderobo-widget-popup-list-item-swap-item-price {
    display: none;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar {
    height: 4px;
    background: #F6F6F6;
}

.garderobo-widget-popup-list-item-swap-container-list::-webkit-scrollbar-thumb {
    background-color: #BABBC1;
    border-radius: 2px;
}
.garderobo-widget-popup-list-item-text-like-btn {
    display: none;
}

/* DESKTOP */
@media screen and (min-width: 769px) {
    .garderobo-widget-popup {
        height: 80vh;
        padding: 20px;
        max-width: 1300px;
        max-height: 1000px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 50%;
        padding-top: 20px;
        height: calc(100% + 20px);
    }
}

/* MOBILE */
@media screen and (max-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 1.875em;
        font-style: normal;
        font-weight: 400;
        line-height: 1;
    }

    .garderobo-widget-container .garderobo-widget-feed:first-child {
        margin-bottom: 70px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 44%;
        margin: 0 10px;
    }

    .garderobo-widget-container.garderobo-widget-container-for-popup {
        transform: none !important;
    }

    .garderobo-widget-popup-collage-container {
        height: 440px !important;
        flex: unset !important;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: 0 !important;
        top: calc(50% - -45px);
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: 0 !important;
        top: calc(50% - -45px);
    }

    .garderobo-widget-popup-list-content {
        margin: 70px 20px 20px;
        border-left: none !important;
        padding: 0;
    }

    .garderobo-widget-popup-collage-container .garderobo-widget-feed-header {
        display: block;
        text-align: center;
        margin-top: 50px;
        font-weight: 500;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: 140px !important;
    }

    .garderobo-widget-popup-action-buttons button,
    .garderobo-widget-sizes {
        font-size: 12px !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed-container.garderobo-widget-feed-item-look {
        width: 100%;
    }

    .garderobo-widget-popup-list-item-text-price {
        font-size: 18px;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed-items .garderobo-widget-feed-item {
        min-height: unset !important;
        max-height: unset !important;
    }

    .garderobo-scroll-button {
        bottom: 30px;
    }
}

.garderobo-grid-look__product[data-category-collage="belt"] {
    display: none;
}