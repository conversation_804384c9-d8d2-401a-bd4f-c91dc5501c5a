/* RAINBOW */
.garderobo-widget-container .garderobo-widget-feed {
    max-width: 1170px;
    width: 100%;
    margin-right: auto;
    margin-left: auto;
    margin-bottom: 2.5rem;
}

.garderobo-widget-popup-list-item-img {
    object-fit: contain;
}

.garderobo-widget-popup-list-item-img-wrapper {
    height: 90px;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: 100%;
}

.garderobo-widget-popup-list-item-swap-item-img {
    object-fit: contain;
}

.garderobo-widget-container .garderobo-widget-feed header {
    min-height: 44px;
    font-family: system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif, roboto, helvetica, arial, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    font-size: 1.25rem;
    line-height: 1.1;
    margin: 0 auto;
    padding-bottom: 0.875rem;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-popup-content .garderobo-widget-feed-container {
    margin-top: 80px !important;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    width: 100%;
    height: auto;
    align-content: flex-start;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header-content {
    margin: 0 4.5% 10px;
    padding: 5px;
    border-bottom: 2px #ccc solid;
    text-align: center;
    font-size: 14px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    /* height: 330px; */
    width: 100%;
    padding-bottom: 136.8421052632%;
    border-bottom: none;
    background-size: contain;
    background-repeat: no-repeat;
    background-position-y: 0px;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
    height: auto;
    flex-direction: column !important;
    padding-top: 0.4375rem;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-brand-name {
    display: none;
}

p {
    font-family: system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, sans-serif, roboto, helvetica, arial, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    max-width: 100%;
    padding-top: 0.4375rem;
    font-size: .75rem;
    margin: 0;
    min-height: 0.8125rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container {
    display: none;
    /*width: 100%;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-like,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-control-container .garderobo-widget-product-control-dislike {
    /*width: 50%;*/
    /*height: 40px;*/
    /*display: inline-block;*/
    /*background-color: gray;*/
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    display: flex;
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price {
    order: 1;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    color: #000;
    font-size: .9375rem;
    line-height: 1.5;
    font-weight: 700;
    height: 1.375rem;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price,
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    padding: 0 2px 0 5px;
    order: 2;
    font-weight: 500;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-discount-percent {
    order: 3;
    text-decoration: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    text-indent: -99999px;
    display: block;
    width: 13px;
    height: 22px;
    transform: translate(0, -50%);
    cursor: pointer;
    color: transparent;
    border: 0;
    outline: 0;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAsBAMAAABMNS/cAAAAMFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABHcEx/v/MDAAAAEHRSTlP/+1dHd19n94T1o09tQAMAS5BP0wAAAKRJREFUGNNjeKX/Hwg+MCwQhdCBjPFg+pMAG5j+r8iQD6Y/CfCA6f8bGfzB9OcCFjD935zBH0x/nsABpv/fZGhApmHiUHUwfVBzYOZC7YHZC3MHzF0wd4LI/1oPwPRXgQVgOpAxAER/YxACyycw6oPoPwxCYPUOjPtB9BeGYrB+B3Z7EP2DYTLYvAbO+yg0TBymDq4PZg7cXJg9cHth7oC7C+hOAJlK4MmSzflFAAAAAElFTkSuQmCC');
    margin-top: 40px;
    scale: 0.6;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    left: 0;
    background-position: 0 -22px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left:before {
    content: '';
    font-family: slick;
    font-size: 20px;
    line-height: 1;
    opacity: 0.75;
    color: #fff;
    -webkit-font-smoothing: antialiased;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right:before {
    content: '';
    font-family: slick;
    font-size: 20px;
    line-height: 1;
    opacity: 0.75;
    color: #fff;
    -webkit-font-smoothing: antialiased;
}

.garderobo-widget-feed-item-look {
    margin-top: 15px;
}

.garderobo-widget-look-product--left-top.garderobo-widget-look-product--layer-1_top,
.garderobo-widget-look-product--right-top.garderobo-widget-look-product--layer-1_top {
    height: 30%;
    width: 25%;
}


.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    display: none;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items:not(.garderobo-widget-feed-less-than-per-page) {
    /* display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr; */
    /* column-gap: 0.5rem; */
}

.garderobo-widget-container .garderobo-widget-feed-item_inner .garderobo-widget-product-footer {
    align-items: flex-start;
}

.garderobo-multiple-btn {
    padding: 12px 42px;
    font-size: 15px;
    color: #fff;
    background-color: #000;
    border-radius: 25px;
}

.garderobo-multiple-btn:hover {
    background-color: #000;
}

.garderobo-widget-container .garderobo-multiple .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    margin-bottom: 20px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: 0.5s all;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    width: 25%;
}

.garderobo-widget-container {
    padding-right: 0.8125rem;
    padding-left: 0.8125rem;
}

#garderobo-recommendations .garderobo-widget-container {
    padding-right: 0;
    padding-left: 0;
}

.garderobo-widget-product-old-price {
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price+.garderobo-widget-product-price {
    color: #eb0000;
}

.garderobo-looks-simple-popup .garderobo-widget-popup-list-item-text-title {
    font-size: .75rem;
    margin: 0;
    min-height: 0.8125rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.garderobo-widget-popup-list-item-text-prices {
    color: #000;
    font-size: 20px;
    font-weight: 700;
    height: 22px;
}

.garderobo-widget-popup-list-item-text-new-price {
    margin-right: 0;
}

.garderobo-widget-popup-list-item-text-discount {
    order: 2;
    font-weight: 500;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    font-weight: normal;
    color: #fff;
    background-color: #000 !important;
    border: none;
    border-radius: 4px;
    text-transform: capitalize;
    padding: 10px 30px !important;
}

.garderobo-widget-popup-list-content .garderobo-widget-popup-list-item:first-child {
    padding-top: 18px;
}

.garderobo-widget-feed-items-counter-text {
    margin: 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item_inner {
    padding-inline: 0.25rem;
}

@media only screen and (max-width: 1026px) {

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 50%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: flex-start;
    }

}

@media screen and (max-width: 767px) {

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
        border: none !important;
    }

    .garderobo-widget-container .garderobo-widget-feed {
        justify-content: center;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 1.25rem;
        line-height: 1.8rem;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
        line-height: 13px;
    }

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
        flex-wrap: wrap;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg id='Стрелка_xA0_Изображение_1_' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 110.4 173.6'%3e%3cswitch%3e%3cg%3e%3cpath fill='%239D9D9D' d='M89.9,162c-3.5-3.2-82.1-75.3-85.4-77.7c-0.8-0.6-0.7-1.5,0-2.3C7,79.5,64.1,27.8,89.2,4.9 c0.2-0.2,0.4-0.3,0.7-0.6c2.9,3.2,5.7,6.3,8.7,9.6C73.2,37,47.9,60,22.5,83.1c25.4,23.1,50.7,46.1,76.1,69.2 C95.7,155.6,92.8,158.7,89.9,162z'/%3e%3c/g%3e%3c/switch%3e%3c/svg%3e");
        background-position: unset;
        transform: unset;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        transform: rotate(180deg);
    }
}

@media screen and (max-width: 425px) {

    .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer {
        width: 80%;
    }
}

.garderobo-widget-look-product--shoes {
    height: 20%;
}

.garderobo-widget-look-product--bag {
    width: 21%;
    height: 21%;
}

.garderobo-widget-look-product--left-center-bag {
    top: 54%;
}

.garderobo-widget-look-product--gloves {
    width: 12%;
    height: 20%;
    top: 30%;
    background-position-y: bottom;
}

/* CHILDS */

.garderobo-childs .garderobo-widget-look-product--center-hat {
    left: 42%;
}

.garderobo-childs .garderobo-widget-look-product--hat {
    width: 18%;
}

.garderobo-childs .garderobo-widget-look-product--center-bottom {
    width: 30% !important;
}

.garderobo-childs .garderobo-widget-look-product--center.garderobo-widget-look-product--layer-1_top {
    width: 30% !important;
}

.garderobo-childs .garderobo-widget-look-product--right-top-3 {
    left: auto !important;
    right: 0 !important;
    top: 31% !important;
    z-index: 10 !important;
}

.garderobo-childs .garderobo-widget-look-product--belt-2 {
    top: 43%;
}

.garderobo-childs .garderobo-widget-look-product--socks {
    width: 13% !important;
    height: 20% !important;
    bottom: 23% !important;
    display: block !important;
}

.garderobo-childs .garderobo-widget-look-product--accessory-head {
    width: 14%;
    height: 14%;
    top: 2%;
    z-index: 12 !important;
}

.garderobo-childs .garderobo-widget-look-product--accessory-head-2 {
    /*left: 25%;*/
}

.garderobo-childs .garderobo-widget-look-product--accessory-glasses {
    width: 14%;
    height: 14%;
    right: 2%;
    z-index: 12 !important;
}

.garderobo-childs .garderobo-widget-look-product--accessory-glasses-2 {
    right: 25% !important;
}

.garderobo-childs .garderobo-widget-look-product--right-gloves {
    right: 24% !important;
    top: 53% !important;
    z-index: 12 !important;
}

.garderobo-childs .garderobo-widget-look-product--shoes {
    left: 12% !important;
    width: 24% !important;
}

.garderobo-childs .garderobo-widget-look-product--bag {
    width: 25%;
    height: 25%;
    right: 6%;
}

#garderobo-additional-recommended header {
    border-bottom: 1px solid #d9d9d9;
    padding: 0 0 7px !important;
    font-size: 16px !important;
    margin-top: 16px !important;
    margin-bottom: 16px !important;
    font-weight: normal;
    text-align: left;
}

#garderobo-additional-recommended .garderobo-widget-feed-item {
    width: 25% !important;
}

#garderobo-additional-recommended .garderobo-widget-product-footer {
    display: none !important;
}

#garderobo-additional-recommended .garderobo-widget-product-image {
    height: 115px !important;
}

#garderobo-additional-recommended .garderobo-widget-control-left,
#garderobo-additional-recommended .garderobo-widget-control-right {
    margin-top: 50px !important;
}

#garderobo-additional-recommended .garderobo-widget-feed {
    margin-bottom: -16px !important;
}

/* Grid Look */
.garderobo-grid-look__socks {
    display: block;
}

@media screen and (max-width: 560px) {}

@media screen and (max-width: 425px) {

    #garderobo-additional-recommended .garderobo-widget-feed-item {
        width: 25% !important;
    }

    #garderobo-additional-recommended header {
        font-size: 17px !important;
        border-bottom: none !important;
        margin-bottom: 0 !important;
        text-align: left;
        font-weight: normal;
    }

    #garderobo-additional-recommended .garderobo-widget-feed {
        margin-bottom: -14px !important;
    }

    #garderobo-additional-recommended .garderobo-widget-feed-container {
        width: calc(100% - 50px) !important;
    }

    #garderobo-additional-recommended .garderobo-widget-control-left,
    #garderobo-additional-recommended .garderobo-widget-control-right {
        margin-top: 30px !important;
    }

    .garderobo-widget-feed-looks .garderobo-widget-feed-items {
        display: grid !important;
        grid-template-columns: repeat(auto-fill, minmax(50%, 1fr));
    }

    .garderobo-widget-feed-looks .garderobo-widget-feed-items .duplicate {
        display: none;
    }

    .garderobo-widget-feed-looks .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 100% !important;
    }

    .garderobo-widget-feed-looks .garderobo-widget-feed-items .garderobo-widget-feed-items-counter-text {
        display: none;
    }
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
        width: calc(33% - 10px) !important;
    }
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    /*border: 1px solid #eee !important;*/
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed .garderobo-widget-feed-container.garderobo-widget-feed-item-look .garderobo-widget-feed-items .garderobo-widget-feed-item {
    border: none !important;
}

.garderobo-widget-popup-collage-container .garderobo-widget-feed-item .garderobo-widget-look-container {
    padding: 0 !important;
}

.garderobo-widget-look-container {
    padding: 7px;
}

@media screen and (min-width: 768px) {

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page{
        justify-content: flex-start;
    }
}

.garderobo-widget-look-container div[data-category-collage="socks"] {
    display: none;
}

.garderobo-widget-look-container div[data-category-collage="shoes"] img {
    -moz-transform: scaleX(-1);
    -o-transform: scaleX(-1);
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1);
}

.garderobo-widget-look-container div[data-category-collage="bag"] {
    grid-row: 29/39 !important;
}

.garderobo-widget-look-container div[data-position="top"] {
    grid-column-end: 40 !important;
}

.garderobo-widget-look-container div[data-position="top2"] {
    grid-column-start: 1 !important;
}

.garderobo-widget-feed-items-counter-text {
    display: none;
}

.garderobo-multiple-controls {
    display: none;
}

/* CARDS VIEW */
.garderobo-widget-look-container-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.garderobo-widget-look-container-cards__top-line {
    display: flex;
    flex: 1;
    gap: 10px;
}

.garderobo-widget-look-container-cards__bottom-line {
    display: flex;
    gap: 10px;
    width: 100%;
    justify-content: flex-start;
    height: 130px;
}

.garderobo-widget-look-container-cards__item {
    border: 1px solid #E1E1E1;
    width: 100%;
    background-size: cover;
    background-position: center;
    position: relative;
    background-repeat: no-repeat;
    overflow: hidden;
}

.garderobo-widget-look-container-cards__item__texts {
    position: absolute;
    left: 0;
    width: 100%;
    bottom: 0;
    background: linear-gradient(180deg, rgba(79, 79, 79, 0) 0%, #4F4F4F 100%);
    padding: 6px 5px;
    padding-top: 10px;
    font-size: 7px;
    color: #fff;
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    gap: 5px;
}

.garderobo-widget-look-container-cards__main-item .garderobo-widget-look-container-cards__item__texts {
    width: 100%;
    padding: 4px 16px;
    padding-top: 30px;
    height: 25%;
    font-size: 10px;
    box-sizing: border-box;
}


.garderobo-widget-look-container-cards__item__title, .garderobo-widget-look-container-cards__item__price {
    width: 100%;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 400;
}

.garderobo-widget-look-container-cards__item__price {
    font-size: 8px;
    font-weight: 700;
}

.garderobo-widget-look-container-cards__item__price-old-price {
    margin-left: 5px;
    text-decoration: line-through;
}

.garderobo-widget-look-container-cards__main-item .garderobo-widget-look-container-cards__item__price {
    font-size: 12px;
}

.garderobo-widget-look-container-cards__main-item  .garderobo-widget-look-container-cards__item__price-old-price {
    font-size: 10px;
}

.garderobo-widget-look-container-cards__main-item {
    flex: 66%;
    height: 280px;
}

.garderobo-widget-look-container-cards__right-col {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    flex: calc(33% - 5px);
}

.garderobo-widget-look-container-cards__bottom-line .garderobo-widget-look-container-cards__item {
    width: calc(33% - 5px);
}

.garderobo-widget-feed-item-look .garderobo-widget-look-container {
    position: unset;
    max-width: 320px;
    margin: 0 auto;
    background: #fff;
}

.garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    padding-bottom: 0 !important;
}

.garderobo-widget-feed-items-counter-text {
    display: block;
}

.garderobo-widget-feed-item--special .garderobo-widget-look-container {
    border: none;
    padding: 7px;
}

.garderobo-widget-look__badge-special {
    display: none;
}

.garderobo-widget-look__btn-buy {
    background: #000;
    width: 100%;
    max-width: 320px;
    border: none;
    color: #fff;
    border-radius: 4px;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 500;
    text-transform: capitalize;
}

.garderobo-widget-look__btn-buy:hover {
    background-color: #000;
}

/*POPUP */

.garderobo-widget-popup-list-item-brand,
.garderobo-widget-popup-list-item-brand-hide {
    display: -webkit-box !important;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    line-clamp: 3;
    min-height: 31px;
    margin-block: 5px;
    font-size: 10px;
    line-height: 1;
    text-align: center;
    overflow: hidden;
}

.garderobo-widget-sizes-buttons-wrapper {
    display: block !important;
}

.garderobo-widget-sizes-buttons {
    display: flex !important;
    flex-wrap: wrap;
    margin: 12px 0;
    gap: 7px;
}

.garderobo-widget-sizes-buttons div {
    border: 1px solid #222;
    color: #222;
    border-radius: .1875rem;
    font-size: .8125rem;
    padding: 8px 12px;
    cursor: pointer;
    margin-right: -1px;
    margin-top: -1px;
    background: none;
}

.garderobo-widget-sizes-buttons div:hover {
    color: #fff;
    background: #000;
}

.garderobo-widget-popup-list-item-swap-button {
    display: none !important;
    border: 1px solid #BABBC1;
}

.garderobo-widget-popup-list-item-text-cart-btn {
    background: #000;
    color: #fff;
}

.garderobo-widget-popup-list-item-text-cart-btn-link-state::before {
    content: "";
}

.garderobo-widget-popup-actions-controls, .garderobo-widget-popup-action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    width: 100%;
}

.garderobo-widget-sizes {
    display: none;
    width: 100%;
    border: 1px solid #EAEAEA;
    border-radius: 3px;
    font-size: 18px !important;
    line-height: 20px;
    font-weight: 400;
    margin-right: 0 !important;
    height: 44px;
    padding: 10px !important;
}

.garderobo-widget-popup-list-item-text {
    margin: 0;
    flex: 1;
}

.garderobo-widget-popup-list-item-text-title {
    margin: 0;
    font-size: .875rem;
    font-weight: 500;
    line-height: 1.2rem;
}

.garderobo-widget-popup-list-item-text-new-price {
    font-size: 1.3125rem;
    font-weight: 700;
    color: #eb0000;
    line-height: 1.3rem;
}

.garderobo-widget-popup-list-item-swap-item-price--sale {
    color: #eb0000 !important;
}

.garderobo-widget-popup-list-item-swap-item-price--old {
    margin-left: 4px;
    text-decoration: line-through;
    color: #000;
    display: unset !important;
}

.garderobo-widget-popup-list-item-text-discount {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.35rem;
    color: #000;
}

.garderobo-widget-popup-list-item-swap-container-item {
    height: unset;
    padding: unset;
    position: unset;
    display: flex;
    flex-wrap: wrap;
    min-width: 150px;
}

.garderobo-widget-popup-list-item-swap-item-content {
    padding: 12px !important;
    position: unset;
    height: 70%;
    width: 100%;
    border: none !important;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes-wrapper {
    height: 20%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 0 12px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 4px;
}

.garderobo-widget-popup-list-item-swap-container-item-sizes div {
    margin-left: -1px;
    margin-top: -1px;
    padding: 0;
    font-size: 12px;
    line-height: 14px;
    border: 1px solid #808285;
    color: #808285;
    border-radius: 0.1875rem;
    padding: 3px 3px;
}

.garderobo-widget-popup-list-item-swap-container-item:first-child {
    margin: 0;
}

.garderobo-widget-popup-list-item-swap-item-img-wrapper {
    width: unset;
    height: 100%;
}

.garderobo-widget-popup-list-item-brand {
    display: none !important;
    width: 100%;
    margin: 0;
    padding: 0;
    min-height: unset;
    font-size: 14px;
    font-weight: 500 !important;
    line-height: 20px;
    letter-spacing: 0.5px;
}

.garderobo-widget-popup-list-item-text-brand {
    display: none;
}

.garderobo-widget-popup-list-item-swap-item-price {
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 0.5px;
    color: #222;
    text-decoration: none;
    padding: 0 !important;
}

.garderobo-widget-popup-list-item-swap-item-price {
    position: unset;
}

.garderobo-widget-popup-collage-container {
    padding: 48px 0;
}

.garderobo-widget-feed-item-look {
    height: 100%;
}

.garderobo-widget-feed-item {
    padding: 0 !important;
    margin: 0 !important;
    height: 100%;
}

.garderobo-widget-feed-item-min-width {
    padding: 0 !important;
    margin: 0 !important;
}

.garderobo-widget-popup-container .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
    padding-bottom: 0 !important;
}

.garderobo-widget-feed-container {
    align-items: center;
}

.garderobo-widget-feed-items {
    height: 100%;
}

.garderobo-widget-look-container {
    margin: auto;
    aspect-ratio: 1/1;
    max-height: 100%;
    max-width: 100%;
}

.garderobo-widget-popup-content .garderobo-widget-popup-collage-container {
    flex: 1;
    display: flex;
}

.garderobo-widget-popup-content .garderobo-widget-feed {
    flex: 1;
    align-self: center;
    height: 100%;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item {
    padding: 24px 12px;
    width: unset;
    gap: 24px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
    margin: 0;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    position: unset;
    align-items: unset;
    gap: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper {
    height: unset;
    flex: 1;
    position: unset;
    display: flex;
    justify-content: center;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img-wrapper a {
    display: flex;
    padding: 12px;
}

.garderobo-widget-popup-content .garderobo-widget-popup-list-item-img {
    position: unset;
    max-height: 200px;
    object-fit: contain;
    align-self: center;
}

.garderobo-widget-popup-list-item-swap-container-item .garderobo-widget-popup__btn-close {
    background: #fff;
    border-radius: 8px;
    opacity: 0.7;
}

.look-loading-state::after {
    content: "Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 5px;
    z-index: 1;
}

.garderobo-grid-look__product_img_with_positions {
    position: relative;
}

.garderobo-grid-look__product-disabled-layout img {
    opacity: 0.2;
}

.garderobo-grid-look__product-disabled-layout::before {
    content: "";
    display: flex;
    position: absolute;
    justify-content: center;
    height: 100%;
    width: 50%;
    left: 25%;
    text-transform: uppercase;
    align-items: center;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    text-align: center;
}

.garderobo-widget-feed-header--with-counter {
    display: flex !important;
}

.garderobo-scroll-button {
    left: 5px !important;
    top: 5px !important;
    background-color: #00a8eb !important;
}

.garderobo-scroll-button:hover {
    background-color: #008fc7 !important;
}

.garderobo-widget-product-promo {
    text-transform: uppercase;
    color: #eb0000;
    font-size: .625rem;
    font-weight: 700;
}

@media screen and (min-width: 767px) {
    .garderobo-widget-popup {
        height: 90vh !important;
        max-width: 1100px;
        max-height: 720px;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-container {
        width: 50%;
        max-width: 500px;
    }
}

@media screen and (max-width: 766px) {
    .garderobo-widget-feed-header {
        display: block;
    }

    .garderobo-widget-feed-item-look {
        height: 500px;
    }

    .garderobo-widget-feed-item-look .garderobo-widget-feed-item {
        margin-bottom: 0 !important;
    }

    .garderobo-widget-popup-content .garderobo-widget-feed-item-look {
        display: none !important;
    }

    .garderobo-widget-feed-items-counter {
        display: flex !important;
    }

    .garderobo-widget-popup-collage-container {
        padding-top: 80px;
        padding-bottom: 0;
    }

    .garderobo-widget-popup-content .garderobo-widget-popup-list-item-pic {
        width: 170px;
    }

    .garderobo-widget-popup-list-item-text-new-price, .garderobo-widget-popup-list-item-text-price {
        font-size: 1rem;
    }

    .garderobo-widget-popup-list-item-text-discount {
        font-size: 0.8rem;
    }

    .garderobo-widget-look__btn-buy {
        bottom: 15px;
    }
}

.garderobo-widget-sizes-button-active {
    background-color: #000 !important;
    color: #fff !important;
    border-color: #000 !important;
}

.garderobo-complete-look h2, .garderobo-recommendations h2, .cart-info h2 {
    display: none;
}

.cart-info .form-in {
    margin-bottom: 64px;
}

@media (min-width: 767px) {
    .garderobo-widget-feed-items {
        gap: 10px;
    }
}