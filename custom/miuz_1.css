/* MIUZ */

.garderobo-widget-container {
    margin-top: 24px;
}

.garderobo-widget-container .garderobo-widget-feed {
    margin-bottom: 20px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items {
    transition: .5s all;
}


.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
    border: 3px solid #f9f9fb;
    transition: border-color .5s;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container--mobile .garderobo-widget-feed-items .garderobo-widget-feed-item {
    margin: 5px 0;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item:hover {
    border-color: #deba8f;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item.garderobo-widget-feed-item-min-width {
    cursor: default;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item.garderobo-widget-feed-item-min-width a {
    cursor: default;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item.garderobo-widget-feed-item-min-width:hover {
    border-color: #f9f9fb;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-name {
    font-size: 16px;
    font-family: 'ABodoniNova',sans-serif;
    color: #474747;
    font-weight: 400;
    text-align: center;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price, .garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    color: #7c183e;
    text-transform: uppercase;
    font-size: 16px;
    text-align: right;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-old-price {
    color: #474747;
    text-decoration: line-through;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-footer .garderobo-widget-product-price-container {
    flex-direction: column-reverse;
    width: 100%;
    align-items: flex-end;
    margin-top: 10px;
    font-family: 'ABodoniNova',sans-serif;
    font-weight: 400;
    padding: 5px;
}
.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-header .garderobo-widget-product-name {
    font-family: 'ABodoniNova',sans-serif;
    color: #474747;
    font-size: 16px;
    font-weight: 400;
    padding: 10px;
    text-align: center;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 35px;
    text-decoration: none;
}

.garderobo-widget-container .garderobo-widget-feed-item .garderobo-widget-product-image {
    height: 140px;
    max-width: 170px;
    width: 100%;
    background-size: contain;
    background-repeat: no-repeat;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item.garderobo-widget-feed-item-min-width {
    min-width: 222px;
    min-height: 275px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item.garderobo-widget-feed-item-min-width .garderobo-widget-product-image {
    height: 206px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
    width: 100%;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left, .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    text-indent: -99999px;
    display: block;
    width: 13px;
    height: 22px;
    transform: translate(0,-50%);
    cursor: pointer;
    color: transparent;
    border: 0;
    outline: 0;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAsBAMAAABMNS/cAAAAMFBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABHcEx/v/MDAAAAEHRSTlP/+1dHd19n94T1o09tQAMAS5BP0wAAAKRJREFUGNNjeKX/Hwg+MCwQhdCBjPFg+pMAG5j+r8iQD6Y/CfCA6f8bGfzB9OcCFjD935zBH0x/nsABpv/fZGhApmHiUHUwfVBzYOZC7YHZC3MHzF0wd4LI/1oPwPRXgQVgOpAxAER/YxACyycw6oPoPwxCYPUOjPtB9BeGYrB+B3Z7EP2DYTLYvAbO+yg0TBymDq4PZg7cXJg9cHth7oC7C+hOAJlK4MmSzflFAAAAAElFTkSuQmCC");
    top: calc(50% + 25px);
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
    width: 50px;
    height: 50px;
    background-repeat: no-repeat;
    background-image: url(https://rrstatic.retailrocket.net/miuz/imgs/left.png);
    left: -25px;
}

.garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
    right: -55px;
    width: 50px;
    height: 50px;
    background-repeat: no-repeat;
    background-image: url(https://rrstatic.retailrocket.net/miuz/imgs/right.png);
}

.garderobo-widget-container .garderobo-widget-feed header {
    display: inline-block;
    color: #000;
    font-family: 'ABodoniNova',sans-serif;
    font-size: 30px;
    font-weight: 400;
    letter-spacing: 5px;
}

.garderobo-widget-container .garderobo-widget-feed {
    justify-content: flex-start;
}

/* todo - remove max-width */
@media screen and (max-width: 767px) {
    .garderobo-widget-container .garderobo-widget-feed {
        justify-content: center;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container {
        width: 100%;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(50% - 2rem);
        margin: 0 5px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container--mobile .garderobo-widget-feed-items .garderobo-widget-feed-item {
        margin: 5px 0;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item.garderobo-widget-feed-item-min-width {
        min-width: auto;
        min-height: auto;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left,
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        width: 35px;
        height: 35px;
        background-size: contain;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-left {
        left: 10px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-control-right {
        right: -10px;
    }

    .garderobo-widget-container .garderobo-widget-feed header {
        font-size: 3rem;
        margin-bottom: 30px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item.garderobo-widget-feed-item-min-width .garderobo-widget-product-image {
        height: 140px;
    }
}

@media screen and (min-width: 768px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page,
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container-new .garderobo-widget-feed-items.garderobo-widget-feed-less-than-per-page {
        justify-content: flex-start;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container--mobile .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: calc(33.33% - 40px/3);
        margin: 0 20px 20px 0;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container--mobile .garderobo-widget-feed-items .garderobo-widget-feed-item:nth-child(3n) {
        margin-right: 0;
    }
}

@media screen and (min-width: 1024px) {
    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container .garderobo-widget-feed-items .garderobo-widget-feed-item {
        width: 15.3%;
        margin: 0 10px;
    }

    .garderobo-widget-container .garderobo-widget-feed .garderobo-widget-feed-container--mobile .garderobo-widget-feed-items .garderobo-widget-feed-item:nth-child(3n) {
        margin-right: 10px;
    }
}
